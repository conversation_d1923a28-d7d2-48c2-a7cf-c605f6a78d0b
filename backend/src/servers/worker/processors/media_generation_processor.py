import asyncio
import logging
import os
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from contextlib import asynccontextmanager

from core.config import get_settings
from core.db.database import get_db_session_factory
from modules.media_generation.models import MediaJob, MediaVariant, MediaJobStatus, MediaVariantStatus
from modules.media_generation.image_service import ai_image_service
from modules.media_generation.video_service import ai_video_service
from modules.media_generation.voice_service import ai_voice_service
from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult
from modules.media_generation.storage_service import media_storage_service
from modules.media_generation.transcoding_service import video_transcoding_service
from modules.billing.service import billing_service
from core.metrics import media_generation_duration, media_generation_failures

logger = logging.getLogger(__name__)
settings = get_settings()

class MediaGenerationProcessor:
    """Enhanced media generation processor with improved error handling and monitoring."""

    def __init__(self):
        self.db_factory = get_db_session_factory()

    @asynccontextmanager
    async def get_db_session(self):
        """Context manager for database sessions."""
        db = self.db_factory()
        try:
            yield db
        finally:
            await db.close()

    def process(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process media generation job with enhanced error handling.

        Args:
            job_data: Job data dictionary

        Returns:
            Job result
        """
        return asyncio.run(self._process_async(job_data))

    async def _process_async(self, job_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Async implementation with comprehensive error handling and monitoring.
        """
        tenant_id = job_data["tenant_id"]
        db_job_id = job_data["job_id"]
        product_ids = job_data.get("product_ids", [])
        template_id = job_data.get("template_id")
        voice_id = job_data.get("voice_id")
        media_type = job_data.get("media_type", "video")

        start_time = datetime.now(timezone.utc)
        logger.info(f"Processing {media_type} generation job {db_job_id} for tenant {tenant_id}")

        try:
            async with self.get_db_session() as db:
                # Get and validate media job
                media_job = await db.get(MediaJob, db_job_id)
                if not media_job:
                    raise ValueError(f"Media job {db_job_id} not found")

                # Update job status to processing
                media_job.status = MediaJobStatus.PROCESSING
                media_job.started_at = start_time
                await db.commit()

                # Process products with error tracking
                successful_products = 0
                failed_products = 0
                total_variants = 0

                for product_id in product_ids:
                    try:
                        variants_count = await self._generate_media_for_product(
                            db, media_job, product_id, job_data
                        )
                        successful_products += 1
                        total_variants += variants_count
                        logger.info(f"Successfully processed product {product_id}: {variants_count} variants")
                    except Exception as e:
                        failed_products += 1
                        logger.error(f"Failed to process product {product_id}: {e}")
                        # Continue with other products

                # Calculate billing based on actual variants generated
                if total_variants > 0:
                    try:
                        await billing_service.record_video_generation_usage(
                            db, tenant_id, str(db_job_id), total_variants
                        )
                    except Exception as e:
                        logger.warning(f"Failed to record billing: {e}")

                # Update final job status
                end_time = datetime.now(timezone.utc)
                duration = (end_time - start_time).total_seconds()

                if failed_products == 0:
                    media_job.status = MediaJobStatus.COMPLETED
                    media_job.completed_at = end_time
                    logger.info(f"Media generation job {db_job_id} completed successfully")
                elif successful_products > 0:
                    media_job.status = MediaJobStatus.PARTIALLY_COMPLETED
                    media_job.completed_at = end_time
                    media_job.error_message = f"Completed {successful_products}/{len(product_ids)} products"
                    logger.warning(f"Media generation job {db_job_id} partially completed")
                else:
                    media_job.status = MediaJobStatus.FAILED
                    media_job.error_message = "All products failed to process"
                    logger.error(f"Media generation job {db_job_id} failed completely")

                await db.commit()

                # Record metrics
                media_generation_duration.labels(
                    media_type=media_type,
                    status=media_job.status.value
                ).observe(duration)

                return {
                    "success": media_job.status == MediaJobStatus.COMPLETED,
                    "job_id": db_job_id,
                    "products_processed": successful_products,
                    "products_failed": failed_products,
                    "variants_generated": total_variants,
                    "duration_seconds": duration,
                    "status": media_job.status.value
                }

        except Exception as e:
            logger.error(f"Critical error in media generation job {db_job_id}: {e}")

            # Update job status on critical failure
            try:
                async with self.get_db_session() as db:
                    media_job = await db.get(MediaJob, db_job_id)
                    if media_job:
                        media_job.status = MediaJobStatus.FAILED
                        media_job.error_message = f"Critical error: {str(e)}"
                        media_job.completed_at = datetime.now(timezone.utc)
                        await db.commit()

                        # Record failure metrics
                        media_generation_failures.labels(
                            media_type=media_type,
                            failure_reason="critical_error"
                        ).inc()
            except Exception as db_error:
                logger.error(f"Failed to update job status: {db_error}")

            raise e
    
    async def _generate_media_for_product(
        self,
        db,
        media_job: MediaJob,
        product_id: str,
        job_data: Dict[str, Any]
    ) -> int:
        """
        Generate media variants for a single product with enhanced error handling.

        Args:
            db: Database session
            media_job: Media job instance
            product_id: Product ID to generate for
            job_data: Complete job configuration

        Returns:
            Number of variants successfully generated
        """
        media_type = job_data.get("media_type", "video")
        template_id = job_data.get("template_id")
        voice_id = job_data.get("voice_id")

        logger.info(f"Generating {media_type} for product {product_id}")

        # Build generation request with flexible configuration
        custom_config = job_data.get("custom_config", {})
        variants_count = custom_config.get("variants_count", 4)

        request = MediaGenerationRequest(
            media_type=media_type,
            product_id=product_id,
            template_id=template_id,
            voice_id=voice_id,
            text_input=media_job.script or f"Amazing product {product_id}",
            aspect_ratio=custom_config.get("aspect_ratio", "16:9"),
            variants_count=variants_count,
            custom_config=custom_config
        )

        # Select appropriate service
        service_map = {
            "video": ai_video_service,
            "image": ai_image_service,
            "voice": ai_voice_service
        }

        if media_type not in service_map:
            raise ValueError(f"Unsupported media type: {media_type}")

        service = service_map[media_type]

        # Generate media with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await service.generate(request)
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    raise Exception(f"Generation failed after {max_retries} attempts: {e}")
                logger.warning(f"Generation attempt {attempt + 1} failed: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

        if not result.success:
            raise Exception(f"{media_type} generation failed: {result.error_message}")

        # Process variants
        successful_variants = 0
        variants_data = result.variants or result.images or []

        for i, variant_data in enumerate(variants_data):
            variant = MediaVariant(
                job_id=media_job.id,
                product_id=product_id,
                variant_name=variant_data.get("name", f"Variant {i+1}"),
                status=MediaVariantStatus.PROCESSING,
                provider_media_id=variant_data.get("id"),
                generation_params=variant_data
            )
            db.add(variant)
            await db.flush()

            try:
                await self._process_variant_media(
                    variant, variant_data, media_job.tenant_id, media_type, custom_config
                )
                variant.status = MediaVariantStatus.READY
                successful_variants += 1
                logger.info(f"Successfully processed variant {variant.id}")

            except Exception as e:
                logger.error(f"Failed to process variant {variant.id}: {e}")
                variant.status = MediaVariantStatus.FAILED
                variant.error_message = str(e)

        await db.commit()
        logger.info(f"Generated and processed {successful_variants}/{len(variants_data)} variants for product {product_id}")

        return successful_variants

    async def _process_variant_media(
        self,
        variant: MediaVariant,
        variant_data: Dict[str, Any],
        tenant_id: int,
        media_type: str,
        custom_config: Dict[str, Any]
    ):
        """Process and store media for a variant."""
        media_url = variant_data.get("video_url") or variant_data.get("image_url") or variant_data.get("voice_url")

        if not media_url:
            raise ValueError("No media URL provided in variant data")

        if media_type == "video":
            await self._process_video_variant(variant, media_url, tenant_id, custom_config)
        elif media_type == "image":
            await self._process_image_variant(variant, media_url, tenant_id)
        elif media_type == "voice":
            await self._process_voice_variant(variant, media_url, tenant_id)
        else:
            raise ValueError(f"Unsupported media type for processing: {media_type}")

    async def _process_video_variant(
        self,
        variant: MediaVariant,
        video_url: str,
        tenant_id: int,
        custom_config: Dict[str, Any]
    ):
        """Process video variant with transcoding and storage."""
        # Configure transcoding options
        output_formats = custom_config.get("output_formats", ["mp4", "hls"])
        generate_thumbnails = custom_config.get("generate_thumbnails", True)
        generate_subtitles = custom_config.get("generate_subtitles", True)
        thumbnail_count = custom_config.get("thumbnail_count", 3)

        processing_result = await video_transcoding_service.process_video(
            video_url=video_url,
            output_formats=output_formats,
            generate_thumbnails=generate_thumbnails,
            generate_subtitles=generate_subtitles,
            thumbnail_count=thumbnail_count
        )

        storage_results = await self._upload_processed_media(
            tenant_id, variant.id, processing_result, "video"
        )

        variant.video_url = storage_results.get('mp4_url')
        variant.hls_url = storage_results.get('hls_url')
        variant.thumbnail_urls = storage_results.get('thumbnail_urls', [])
        variant.subtitle_url = storage_results.get('subtitle_url')

    async def _process_image_variant(
        self,
        variant: MediaVariant,
        image_url: str,
        tenant_id: int
    ):
        """Process image variant with storage."""
        try:
            image_content = await ai_image_service.download_media(image_url)
            image_key = f"tenants/{tenant_id}/images/{variant.id}/image.jpg"

            # Handle different upload methods
            if hasattr(media_storage_service, 'upload_file'):
                if callable(getattr(media_storage_service.upload_file, '__code__', None)):
                    # Method that takes content directly
                    image_url = await media_storage_service.upload_file(image_content, image_key)
                else:
                    # Method that takes file path - write to temp file first
                    import tempfile
                    import os

                    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
                        temp_file.write(image_content)
                        temp_file.flush()
                        image_url = await media_storage_service.upload_file(temp_file.name, image_key)
                        os.unlink(temp_file.name)
            else:
                raise AttributeError("Storage service missing upload_file method")

            variant.image_url = image_url

        except Exception as e:
            logger.error(f"Failed to process image variant {variant.id}: {e}")
            raise

    async def _process_voice_variant(
        self,
        variant: MediaVariant,
        voice_url: str,
        tenant_id: int
    ):
        """Process voice variant with storage."""
        try:
            voice_content = await ai_voice_service.download_media(voice_url)
            voice_key = f"tenants/{tenant_id}/voices/{variant.id}/voice.mp3"

            # Handle different upload methods
            if hasattr(media_storage_service, 'upload_file'):
                if callable(getattr(media_storage_service.upload_file, '__code__', None)):
                    # Method that takes content directly
                    voice_url = await media_storage_service.upload_file(voice_content, voice_key)
                else:
                    # Method that takes file path - write to temp file first
                    import tempfile
                    import os

                    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_file:
                        temp_file.write(voice_content)
                        temp_file.flush()
                        voice_url = await media_storage_service.upload_file(temp_file.name, voice_key)
                        os.unlink(temp_file.name)
            else:
                raise AttributeError("Storage service missing upload_file method")

            variant.voice_url = voice_url

        except Exception as e:
            logger.error(f"Failed to process voice variant {variant.id}: {e}")
            raise

    async def _upload_processed_media(
        self,
        tenant_id: int,
        variant_id: int,
        processing_result: dict,
        media_type: str
    ) -> dict:
        """Upload processed media files to storage."""
        storage_results = {}
        
        try:
            if media_type == "video":
                # Upload MP4 video
                if 'mp4' in processing_result['outputs']:
                    mp4_path = processing_result['outputs']['mp4']
                    mp4_key = f"tenants/{tenant_id}/videos/{variant_id}/video.mp4"
                    mp4_url = await media_storage_service.upload_file(mp4_path, mp4_key)
                    storage_results['mp4_url'] = mp4_url
                
                # Upload HLS playlist and segments
                if 'hls' in processing_result['outputs']:
                    hls_path = processing_result['outputs']['hls']
                    hls_dir = os.path.dirname(hls_path)
                    
                    # Upload all HLS files
                    hls_urls = []
                    for file_name in os.listdir(hls_dir):
                        file_path = os.path.join(hls_dir, file_name)
                        hls_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/{file_name}"
                        hls_url = await media_storage_service.upload_file(file_path, hls_key)
                        hls_urls.append(hls_url)
                    
                    # The main playlist URL
                    playlist_key = f"tenants/{tenant_id}/videos/{variant_id}/hls/playlist.m3u8"
                    storage_results['hls_url'] = f"{media_storage_service.base_url}/{playlist_key}"
                
                # Upload thumbnails
                thumbnail_urls = []
                for i, thumb_path in enumerate(processing_result['thumbnails']):
                    thumb_key = f"tenants/{tenant_id}/videos/{variant_id}/thumbnails/thumb_{i+1}.jpg"
                    thumb_url = await media_storage_service.upload_file(thumb_path, thumb_key)
                    thumbnail_urls.append(thumb_url)
                storage_results['thumbnail_urls'] = thumbnail_urls
                
                # Upload subtitles
                if processing_result['subtitles']:
                    subtitle_path = processing_result['subtitles']
                    subtitle_key = f"tenants/{tenant_id}/videos/{variant_id}/subtitles.srt"
                    subtitle_url = await media_storage_service.upload_file(subtitle_path, subtitle_key)
                    storage_results['subtitle_url'] = subtitle_url
            elif media_type == "image":
                # Image processing results would be different, handle accordingly
                pass
            elif media_type == "voice":
                # Voice processing results would be different, handle accordingly
                pass
            
            return storage_results
            
        except Exception as e:
            logger.error(f"Failed to upload processed media files: {e}")
            raise
