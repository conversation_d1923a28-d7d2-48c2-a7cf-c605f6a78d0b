"""
Voice Service - Wrapper for voice generation using the unified media generation service
"""

import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from .service import media_generation_service, TTSRequest, TTSResult
from .schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class VoiceGenerationRequest(BaseModel):
    """Request model for voice generation."""
    product_id: str
    voice_id: str
    script: str
    custom_config: Optional[Dict[str, Any]] = None


class VoiceGenerationResult(BaseModel):
    """Result model for voice generation."""
    success: bool
    variants: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None


class VoiceService:
    """Service for AI voice generation."""

    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate voice using the unified media generation service."""
        try:
            # For voice generation, we use TTS directly
            if hasattr(request, 'voice_id') and hasattr(request, 'script'):
                tts_request = TTSRequest(
                    text=request.script,
                    voice_id=request.voice_id,
                    speed=getattr(request, 'speed', 1.0),
                    pitch=getattr(request, 'pitch', 1.0)
                )

                tts_result = await media_generation_service.synthesize_speech(tts_request)

                if tts_result.success:
                    return MediaGenerationResult(
                        success=True,
                        variants=[{
                            "voice_url": tts_result.audio_url,
                            "duration_seconds": tts_result.duration_seconds
                        }]
                    )
                else:
                    return MediaGenerationResult(
                        success=False,
                        error_message=tts_result.error_message
                    )
            else:
                # Fallback to provider-based generation
                voice_request = MediaGenerationRequest(
                    media_type="voice",
                    product_id=request.product_id,
                    voice_id=getattr(request, 'voice_id', None),
                    text_input=getattr(request, 'script', None),
                    custom_config=request.custom_config or {}
                )

                # Get voice provider
                provider_name = media_generation_service._get_provider_for_media_type("voice")

                # Generate using unified service
                result = await media_generation_service.generate_media_with_provider(
                    provider_name, voice_request
                )

                return result

        except Exception as e:
            logger.error(f"Voice generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download media content from URL."""
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get(media_url) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    raise Exception(f"Failed to download media: {response.status}")


# Create service instance
ai_voice_service = VoiceService()