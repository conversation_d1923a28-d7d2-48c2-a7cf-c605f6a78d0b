"""
Video Generation Schemas - ProductVideo API contracts
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from enum import Enum

from core.schemas.base_schemas import BaseSchema


class MediaGenerateRequest(BaseSchema):
    """Request to generate media for products."""
    
    media_type: str # 'video', 'image', 'voice'
    shop_id: int
    product_ids: List[str]
    template_id: Optional[str] = None
    voice_id: Optional[str] = None
    aspect_ratio: Optional[str] = "16:9"  # 16:9, 9:16, 1:1
    locale: Optional[str] = "en"
    text_input: Optional[str] = None # For voice generation


class MediaJobInfo(BaseModel):
    """Job information in generate response."""
    
    product_id: str
    job_id: int
    status: str


class MediaGenerateResponse(BaseSchema):
    """Response from generate endpoint."""
    
    jobs: List[MediaJobInfo]


class MediaVariantInfo(BaseModel):
    """Media variant information."""
    
    variant_id: int
    variant_name: str
    status: str
    video_url: Optional[str] = None
    image_url: Optional[str] = None
    voice_url: Optional[str] = None
    thumbnail_url: Optional[str] = None
    duration: Optional[float] = None


class MediaJobStatusResponse(BaseSchema):
    """Job status response."""
    
    job_id: int
    status: str
    progress: float
    variants: List[MediaVariantInfo]


class MediaRegenerateRequest(BaseSchema):
    """Request to regenerate a variant."""
    
    job_id: int
    variant_id: int
    override_params: Optional[Dict[str, Any]] = None


class PublishOptions(BaseModel):
    """Options for publishing media."""
    alt_text: Optional[str] = None
    position: Optional[int] = None
    replace_existing: bool = False


class MediaPushRequest(BaseSchema):
    """Request to push media to Store."""

    shop_id: int
    product_id: str
    variant_id: int
    publish_targets: List[str] = ["shopify"]  # e.g. shopify, tiktok, youtube
    publish_options: Optional[PublishOptions] = None


class MediaPushResponse(BaseSchema):
    """Response from push endpoint."""
    
    push_id: str
    status: str
    message: str


class MediaTemplateResponse(BaseModel):
    """Template information."""

    id: str
    name: str
    description: Optional[str] = None
    duration_target: Optional[int] = None
    preview_url: Optional[str] = None
    category: Optional[str] = None


class MediaTemplateListResponse(BaseSchema):
    """Response for listing media templates."""
    templates: List[Dict[str, Any]]
    total: int
    page: int
    per_page: int


class VoiceResponse(BaseModel):
    """Voice information."""
    
    id: str
    name: str
    gender: Optional[str] = None
    accent: Optional[str] = None
    language: str = "en"
    sample_url: Optional[str] = None


class AnalyticsRequest(BaseSchema):
    """Analytics request parameters."""
    
    product_id: str
    from_date: Optional[str] = None
    to_date: Optional[str] = None
    variant_id: Optional[int] = None


class AnalyticsMetrics(BaseModel):
    """Analytics metrics."""
    
    views: int = 0
    plays: int = 0
    completion_rate: float = 0.0
    avg_watch_time: float = 0.0
    ctr: float = 0.0
    conversions: int = 0
    conversion_lift: Optional[float] = None


class AnalyticsResponse(BaseSchema):
    """Analytics response."""

    product_id: str
    variant_id: Optional[int] = None
    metrics: AnalyticsMetrics
    period: Dict[str, str]


# Additional missing models

class MediaGenerationRequest(BaseModel):
    """Request for media generation (alias for MediaGenerateRequest)."""
    product_title: str
    product_description: Optional[str] = None
    product_images: Optional[List[str]] = None
    product_price: Optional[float] = None
    num_images: int = 4
    variants_count: int = 4
    aspect_ratio: str = "16:9"
    style: str = "product_photography"
    custom_prompt: Optional[str] = None
    template_id: Optional[str] = None
    voice_id: Optional[str] = None
    text_input: Optional[str] = None
    locale: str = "en"


class MediaGenerationResult(BaseModel):
    """Result from media generation."""
    success: bool
    provider_job_id: Optional[str] = None
    images: Optional[List[Dict[str, Any]]] = None
    variants: Optional[List[Dict[str, Any]]] = None
    estimated_completion_time: Optional[int] = None
    error_message: Optional[str] = None


class MediaJobListResponse(BaseSchema):
    """Response for listing media jobs."""
    jobs: List[Dict[str, Any]]  # Simplified, could use MediaJobInfo
    total: int
    page: int
    per_page: int


class MediaVariantListResponse(BaseSchema):
    """Response for listing media variants."""
    variants: List[MediaVariantInfo]
    total: int
    page: int
    per_page: int


class MediaLimitsResponse(BaseSchema):
    """Response for media limits."""
    current_count: int
    max_count: int
    remaining: int
    can_add_more: bool


class PushStatusResponse(BaseSchema):
    """Response for push status."""
    variant_id: str
    status: str
    media_id: Optional[str] = None
    pushed_at: Optional[str] = None
    error_message: Optional[str] = None
