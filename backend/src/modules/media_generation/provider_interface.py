"""
Generic Media Provider Interface for ProductVideo platform.
Provides abstraction layer for different AI media generation providers.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from pydantic import BaseModel

from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult


class ProviderConfig(BaseModel):
    """Configuration for a media provider."""
    name: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    timeout: int = 300
    max_retries: int = 3
    rate_limit: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class MediaProviderPlugin(ABC):
    """Abstract base class for media provider plugins."""

    @property
    @abstractmethod
    def provider_name(self) -> str:
        """Name of the provider."""
        pass

    @property
    @abstractmethod
    def supported_media_types(self) -> List[str]:
        """List of supported media types (image, video, voice)."""
        pass

    @abstractmethod
    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the provider with configuration."""
        pass

    @abstractmethod
    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate media using the provider."""
        pass

    @abstractmethod
    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get job status from the provider."""
        pass

    @abstractmethod
    async def download_media(self, media_url: str) -> bytes:
        """Download media content from the provider."""
        pass

    @abstractmethod
    async def get_provider_info(self) -> Dict[str, Any]:
        """Get provider information and capabilities."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup provider resources."""
        pass


class MediaProviderRegistry:
    """Registry for managing media provider plugins."""

    def __init__(self):
        self._providers: Dict[str, MediaProviderPlugin] = {}
        self._configs: Dict[str, ProviderConfig] = {}

    def register_provider(self, provider: MediaProviderPlugin, config: ProviderConfig):
        """Register a provider plugin."""
        self._providers[provider.provider_name] = provider
        self._configs[provider.provider_name] = config

    def unregister_provider(self, provider_name: str):
        """Unregister a provider plugin."""
        if provider_name in self._providers:
            del self._providers[provider_name]
            del self._configs[provider_name]

    def get_provider(self, provider_name: str) -> Optional[MediaProviderPlugin]:
        """Get a registered provider."""
        return self._providers.get(provider_name)

    def get_available_providers(self, media_type: Optional[str] = None) -> List[str]:
        """Get list of available providers, optionally filtered by media type."""
        if media_type:
            return [
                name for name, provider in self._providers.items()
                if media_type in provider.supported_media_types
            ]
        return list(self._providers.keys())

    def get_provider_config(self, provider_name: str) -> Optional[ProviderConfig]:
        """Get configuration for a provider."""
        return self._configs.get(provider_name)

    async def initialize_all_providers(self) -> Dict[str, bool]:
        """Initialize all registered providers."""
        results = {}
        for name, provider in self._providers.items():
            config = self._configs.get(name)
            if config:
                results[name] = await provider.initialize(config)
            else:
                results[name] = False
        return results

    async def cleanup_all_providers(self) -> None:
        """Cleanup all registered providers."""
        for provider in self._providers.values():
            await provider.cleanup()


# Global provider registry instance
provider_registry = MediaProviderRegistry()