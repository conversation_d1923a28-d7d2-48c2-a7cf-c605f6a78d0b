"""
Banana AI Provider Plugin for ProductVideo platform.
Provides image generation using Banana AI services.
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any

import httpx

from modules.media_generation.provider_interface import (
    MediaProviderPlugin,
    ProviderConfig
)
from modules.media_generation.schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class BananaProvider(MediaProviderPlugin):
    """Banana AI provider plugin for image generation."""

    def __init__(self):
        self.client: Optional[httpx.AsyncClient] = None
        self.config: Optional[ProviderConfig] = None
        self.base_url = "https://api.banana.dev/v1"
        self.model_key = "flux-1.1-pro"  # Using Flux 1.1 Pro model

    @property
    def provider_name(self) -> str:
        return "banana"

    @property
    def supported_media_types(self) -> List[str]:
        return ["image"]

    async def initialize(self, config: ProviderConfig) -> bool:
        """Initialize the Banana provider."""
        try:
            self.config = config
            self.client = httpx.AsyncClient(
                timeout=config.timeout,
                headers={
                    "Authorization": f"Bearer {config.api_key}",
                    "Content-Type": "application/json"
                }
            )

            if config.base_url:
                self.base_url = config.base_url

            logger.info(f"Initialized Banana provider with model: {self.model_key}")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize Banana provider: {e}")
            return False

    async def generate_media(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate images using Banana."""
        if not self.client or not self.config:
            return MediaGenerationResult(
                success=False,
                error_message="Provider not initialized"
            )

        try:
            # Create optimized prompt for product images
            prompt = self._create_image_prompt(request)

            payload = {
                "modelKey": self.model_key,
                "modelInputs": {
                    "prompt": prompt,
                    "num_images": request.num_images,
                    "width": self._get_width_for_aspect(request.aspect_ratio),
                    "height": self._get_height_for_aspect(request.aspect_ratio),
                    "guidance_scale": 7.5,
                    "num_inference_steps": 50,
                    "seed": -1,  # Random seed
                    "safety_check": True
                }
            }

            response = await self.client.post(
                f"{self.base_url}/start/v4",
                json=payload
            )
            response.raise_for_status()

            data = response.json()

            # Process generated images
            images = []
            if "modelOutputs" in data and "images" in data["modelOutputs"]:
                for i, image_data in enumerate(data["modelOutputs"]["images"]):
                    images.append({
                        "image_url": image_data.get("url"),
                        "thumbnail_url": image_data.get("thumbnail_url"),
                        "width": self._get_width_for_aspect(request.aspect_ratio),
                        "height": self._get_height_for_aspect(request.aspect_ratio),
                        "style": request.style,
                        "variant_name": f"variant_{i+1}"
                    })

            return MediaGenerationResult(
                success=True,
                provider_job_id=data.get("id"),
                images=images,
                estimated_completion_time=data.get("estimated_time", 60)
            )

        except Exception as e:
            logger.error(f"Banana image generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    def _create_image_prompt(self, request: MediaGenerationRequest) -> str:
        """Create optimized prompt for Banana image generation."""
        if request.custom_prompt:
            return request.custom_prompt

        base_prompt = f"Professional product photography of {request.product_title}"

        if request.product_description:
            base_prompt += f", {request.product_description[:150]}"

        # Add style-specific elements
        style_prompts = {
            "product_photography": "clean white background, studio lighting, high resolution, commercial photography",
            "lifestyle": "lifestyle setting, natural lighting, in-use context, appealing environment",
            "minimalist": "minimal background, clean composition, simple elegant styling",
            "luxury": "premium setting, sophisticated lighting, high-end presentation, luxury aesthetic",
            "social_media": "trendy, eye-catching, social media optimized, vibrant colors"
        }

        style_addition = style_prompts.get(request.style, "professional product photography")
        base_prompt += f", {style_addition}"

        # Add quality and technical specifications
        base_prompt += ", 8K resolution, sharp focus, professional lighting, high quality"

        return base_prompt

    def _get_width_for_aspect(self, aspect_ratio: str) -> int:
        """Get width for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1920,
            "9:16": 1080,
            "4:5": 1080,
            "3:4": 1080
        }
        return dimensions.get(aspect_ratio, 1024)

    def _get_height_for_aspect(self, aspect_ratio: str) -> int:
        """Get height for aspect ratio."""
        dimensions = {
            "1:1": 1024,
            "16:9": 1080,
            "9:16": 1920,
            "4:5": 1350,
            "3:4": 1440
        }
        return dimensions.get(aspect_ratio, 1024)

    async def get_job_status(self, job_id: str) -> Dict[str, Any]:
        """Get Banana job status."""
        if not self.client:
            return {"status": "error", "error": "Provider not initialized"}

        try:
            response = await self.client.get(
                f"{self.base_url}/check/v4/{job_id}"
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Failed to get Banana job status: {e}")
            return {"status": "error", "error": str(e)}

    async def download_media(self, media_url: str) -> bytes:
        """Download media from Banana."""
        if not self.client:
            raise ValueError("Provider not initialized")

        response = await self.client.get(media_url)
        response.raise_for_status()
        return response.content

    async def get_provider_info(self) -> Dict[str, Any]:
        """Get Banana provider information."""
        return {
            "name": "Banana AI",
            "supported_formats": ["image"],
            "models": [self.model_key],
            "max_images_per_request": 4,
            "supported_aspect_ratios": ["1:1", "16:9", "9:16", "4:5", "3:4"],
            "estimated_cost_per_image": 0.02  # Approximate cost in USD
        }

    async def cleanup(self) -> None:
        """Cleanup Banana provider resources."""
        if self.client:
            await self.client.aclose()
            self.client = None
            logger.info("Cleaned up Banana provider")