"""
Media Generation Module
Provides AI-powered media generation services for ProductVideo platform.
Generic module that uses provider plugins for different AI services.
"""

# Main services
from .service import media_generation_service
from core.services.queue_service import celery_service as queue_service
from .storage_service import media_storage_service
from .template_service import template_service
from .transcoding_service import video_transcoding_service

# Provider system
from .provider_interface import provider_registry
from .provider_manager import provider_manager

# Integrations

# Configuration
from .providers_config import (
    PROVIDER_CONFIGS,
    get_provider_config,
    get_default_provider,
    get_fallback_chain
)

# Models and schemas
from . import models
from . import schemas

__all__ = [
    # Services
    "media_generation_service",
    "queue_service",
    "media_storage_service",
    "template_service",
    "video_transcoding_service",

    # Provider system
    "provider_registry",
    "provider_manager",

    # Integrations

    # Configuration
    "PROVIDER_CONFIGS",
    "get_provider_config",
    "get_default_provider",
    "get_fallback_chain",

    # Modules
    "models",
    "schemas",
]
