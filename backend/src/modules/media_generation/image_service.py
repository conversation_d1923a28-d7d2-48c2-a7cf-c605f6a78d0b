"""
Image Service - Wrapper for image generation using the unified media generation service
"""

import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from .service import media_generation_service
from .schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class ImageGenerationRequest(BaseModel):
    """Request model for image generation."""
    product_id: str
    template_id: Optional[str] = None
    aspect_ratio: str = "1:1"
    variants_count: int = 4
    custom_config: Optional[Dict[str, Any]] = None


class ImageGenerationResult(BaseModel):
    """Result model for image generation."""
    success: bool
    images: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None


class ImageService:
    """Service for AI image generation."""

    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate images using the unified media generation service."""
        try:
            # Create image-specific request
            image_request = MediaGenerationRequest(
                media_type="image",
                product_id=request.product_id,
                template_id=request.template_id,
                custom_config={
                    "aspect_ratio": request.aspect_ratio or "1:1",
                    "variants_count": getattr(request, 'variants_count', 4),
                    **(request.custom_config or {})
                }
            )

            # Get image provider
            provider_name = media_generation_service._get_provider_for_media_type("image")

            # Generate using unified service
            result = await media_generation_service.generate_media_with_provider(
                provider_name, image_request
            )

            return result

        except Exception as e:
            logger.error(f"Image generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download media content from URL."""
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get(media_url) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    raise Exception(f"Failed to download media: {response.status}")


# Create service instance
ai_image_service = ImageService()