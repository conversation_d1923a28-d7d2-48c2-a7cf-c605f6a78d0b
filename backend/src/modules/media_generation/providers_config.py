"""
Configuration for Media Generation Providers.
Centralized configuration for all AI provider plugins.
"""

from typing import Dict, Any, Optional
from modules.media_generation.provider_interface import ProviderConfig


# Provider configurations
PROVIDER_CONFIGS: Dict[str, ProviderConfig] = {
    "mock": ProviderConfig(
        name="mock",
        api_key="mock_key",
        timeout=30,
        max_retries=3,
        metadata={
            "description": "Mock provider for testing and development",
            "cost_per_request": 0.0,
            "rate_limit": 100
        }
    ),

    "banana": ProviderConfig(
        name="banana",
        api_key="${BANANA_API_KEY}",  # Will be replaced with actual env var
        base_url="https://api.banana.dev/v1",
        timeout=300,
        max_retries=3,
        metadata={
            "description": "Banana AI for image generation",
            "cost_per_image": 0.02,
            "supported_formats": ["image"],
            "max_images_per_request": 4,
            "rate_limit": 10
        }
    ),

    "veo3": ProviderConfig(
        name="veo3",
        api_key="${VEO3_API_KEY}",  # Will be replaced with actual env var
        base_url="https://generativelanguage.googleapis.com/v1beta",
        timeout=600,
        max_retries=3,
        metadata={
            "description": "Google Veo 3 for video generation",
            "cost_per_minute": 0.5,
            "supported_formats": ["video"],
            "max_duration_seconds": 60,
            "rate_limit": 5
        }
    ),

    "elevenlabs": ProviderConfig(
        name="elevenlabs",
        api_key="${ELEVENLABS_API_KEY}",
        base_url="https://api.elevenlabs.io/v1",
        timeout=120,
        max_retries=3,
        metadata={
            "description": "ElevenLabs for voice generation",
            "cost_per_character": 0.0002,
            "supported_formats": ["voice"],
            "max_characters": 5000,
            "rate_limit": 20
        }
    ),

    "openai": ProviderConfig(
        name="openai",
        api_key="${OPENAI_API_KEY}",
        base_url="https://api.openai.com/v1",
        timeout=120,
        max_retries=3,
        metadata={
            "description": "OpenAI for image and video generation",
            "cost_per_image": 0.04,
            "cost_per_video": 1.0,
            "supported_formats": ["image", "video"],
            "rate_limit": 10
        }
    )
}


# Default provider mappings by media type
DEFAULT_PROVIDERS = {
    "image": "banana",
    "video": "veo3",
    "voice": "elevenlabs"
}


# Fallback provider chains
FALLBACK_CHAINS = {
    "image": ["banana", "openai", "mock"],
    "video": ["veo3", "openai", "mock"],
    "voice": ["elevenlabs", "mock"]
}


def get_provider_config(provider_name: str) -> ProviderConfig:
    """Get configuration for a specific provider."""
    if provider_name not in PROVIDER_CONFIGS:
        raise ValueError(f"Provider {provider_name} not configured")

    config = PROVIDER_CONFIGS[provider_name]

    # Replace environment variable placeholders
    if config.api_key and config.api_key.startswith("${") and config.api_key.endswith("}"):
        env_var = config.api_key[2:-1]
        import os
        actual_key = os.getenv(env_var)
        if actual_key:
            config.api_key = actual_key
        else:
            raise ValueError(f"Environment variable {env_var} not set for provider {provider_name}")

    return config


def get_default_provider(media_type: str) -> str:
    """Get the default provider for a media type."""
    return DEFAULT_PROVIDERS.get(media_type, "mock")


def get_fallback_chain(media_type: str) -> list:
    """Get the fallback provider chain for a media type."""
    return FALLBACK_CHAINS.get(media_type, ["mock"])


def get_available_providers(media_type: Optional[str] = None) -> Dict[str, ProviderConfig]:
    """Get all available provider configurations, optionally filtered by media type."""
    if media_type:
        # Filter providers that support the media type
        filtered = {}
        for name, config in PROVIDER_CONFIGS.items():
            supported_formats = config.metadata.get("supported_formats", [])
            if media_type in supported_formats:
                filtered[name] = config
        return filtered

    return PROVIDER_CONFIGS


def validate_provider_config(provider_name: str) -> bool:
    """Validate that a provider configuration is complete."""
    try:
        config = get_provider_config(provider_name)
        # Check required fields
        if not config.api_key:
            return False
        if not config.name:
            return False
        return True
    except (ValueError, KeyError):
        return False


# Provider health check configurations
HEALTH_CHECK_CONFIGS = {
    "timeout": 10,  # seconds
    "max_failures": 3,
    "check_interval": 300  # 5 minutes
}


def get_health_check_config() -> Dict[str, Any]:
    """Get health check configuration."""
    return HEALTH_CHECK_CONFIGS