"""
Media Storage Service for ProductMedia platform.
Handles S3 storage with tenant isolation, media transcoding, and media processing.
"""

import asyncio
import hashlib
import logging
import mimetypes
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, BinaryIO
from urllib.parse import urlparse
from enum import Enum

import boto3
import httpx
from botocore.exceptions import ClientError, NoCredentialsError
from pydantic import BaseModel

from google.cloud import storage

from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class StorageProvider(str, Enum):
    """Storage provider options."""
    S3 = "s3"
    CLOUDFLARE_R2 = "cloudflare_r2"
    GOOGLE_CLOUD = "google_cloud"
    LOCAL = "local"


class MediaFormat(str, Enum):
    """Media format options."""
    MP4 = "mp4"
    HLS = "hls"
    WEBM = "webm"
    MOV = "mov"


class ProcessingStatus(str, Enum):
    """Media processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class MediaFile(BaseModel):
    """Media file metadata."""
    file_id: str
    tenant_id: int
    original_filename: str
    storage_path: str
    public_url: str
    file_size: int
    duration_seconds: Optional[float] = None
    resolution: Optional[str] = None
    format: MediaFormat
    processing_status: ProcessingStatus = ProcessingStatus.PENDING
    created_at: datetime
    metadata: Optional[Dict[str, Any]] = None


class MediaStorageService:
    """
    Media storage service with multi-provider support.
    Handles media upload, processing, and CDN distribution.
    """
    
    def __init__(
        self,
        provider: StorageProvider = StorageProvider.S3,
        bucket_name: Optional[str] = None,
        aws_access_key: Optional[str] = None,
        aws_secret_key: Optional[str] = None,
        aws_region: str = "us-east-1",
        cloudflare_r2_config: Optional[Dict] = None,
        gcp_project_id: Optional[str] = None
    ):
        self.provider = provider
        self.aws_region = aws_region
        
        # Initialize storage client based on provider
        if provider == StorageProvider.S3:
            self.bucket_name = bucket_name or settings.S3_BUCKET_NAME
            self.s3_client = boto3.client(
                's3',
                aws_access_key_id=aws_access_key or settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=aws_secret_key or settings.AWS_SECRET_ACCESS_KEY,
                region_name=aws_region
            )
        elif provider == StorageProvider.CLOUDFLARE_R2:
            # Cloudflare R2 uses S3-compatible API
            self.bucket_name = bucket_name or settings.S3_BUCKET_NAME
            self.s3_client = boto3.client(
                's3',
                endpoint_url=cloudflare_r2_config.get('endpoint') if cloudflare_r2_config else settings.CLOUDFLARE_R2_ENDPOINT,
                aws_access_key_id=cloudflare_r2_config.get('access_key') if cloudflare_r2_config else settings.CLOUDFLARE_R2_ACCESS_KEY,
                aws_secret_access_key=cloudflare_r2_config.get('secret_key') if cloudflare_r2_config else settings.CLOUDFLARE_R2_SECRET_KEY,
                region_name='auto'
            )
        elif provider == StorageProvider.GOOGLE_CLOUD:
            self.gcs_client = storage.Client(project=gcp_project_id or settings.GCP_PROJECT_ID)
            self.bucket_name = bucket_name or settings.GCP_BUCKET_NAME
            self.gcs_bucket = self.gcs_client.bucket(self.bucket_name)
        elif provider == StorageProvider.LOCAL:
            self.local_storage_path = settings.LOCAL_STORAGE_PATH or "./storage"
            os.makedirs(self.local_storage_path, exist_ok=True)
            self.local_base_url = settings.LOCAL_STORAGE_BASE_URL or "http://localhost:8000/storage"
        
        logger.info(f"Initialized media storage service with provider: {provider}")
    
    async def upload_media(
        self,
        tenant_id: int,
        media_content: bytes,
        filename: str,
        content_type: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> MediaFile:
        """
        Upload media to storage with tenant isolation.

        Args:
            tenant_id: Tenant ID for isolation
            media_content: Media file content
            filename: Original filename
            content_type: MIME type
            metadata: Additional metadata

        Returns:
            MediaFile object with storage details
        """
        # Generate unique file ID
        file_id = self._generate_file_id(tenant_id, filename)
        
        # Determine content type
        if not content_type:
            content_type, _ = mimetypes.guess_type(filename)
            content_type = content_type or 'video/mp4'  # Default to video/mp4 for media files
        
        # Generate storage path with tenant isolation
        storage_path = self._generate_storage_path(tenant_id, file_id, filename)
        
        try:
            if self.provider in [StorageProvider.S3, StorageProvider.CLOUDFLARE_R2]:
                # Upload to S3/R2
                await self._upload_to_s3(storage_path, media_content, content_type, metadata)
                public_url = self._generate_public_url(storage_path)
            elif self.provider == StorageProvider.GOOGLE_CLOUD:
                await self._upload_to_gcs(storage_path, media_content, content_type, metadata)
                public_url = self._generate_public_url(storage_path)
            elif self.provider == StorageProvider.LOCAL:
                # Upload to local storage
                public_url = await self._upload_to_local(storage_path, media_content)
            else:
                raise ValueError(f"Unsupported storage provider: {self.provider}")

            # Create MediaFile object
            media_file = MediaFile(
                file_id=file_id,
                tenant_id=tenant_id,
                original_filename=filename,
                storage_path=storage_path,
                public_url=public_url,
                file_size=len(media_content),
                format=self._detect_media_format(filename),
                created_at=datetime.utcnow(),
                metadata=metadata or {}
            )

            logger.info(f"Successfully uploaded media {file_id} for tenant {tenant_id}")
            return media_file
            
        except Exception as e:
            logger.error(f"Failed to upload media {filename} for tenant {tenant_id}: {e}")
            raise
    
    async def download_media(self, storage_path: str) -> bytes:
        """Download media content from storage."""
        try:
            if self.provider in [StorageProvider.S3, StorageProvider.CLOUDFLARE_R2]:
                response = self.s3_client.get_object(Bucket=self.bucket_name, Key=storage_path)
                return response['Body'].read()
            elif self.provider == StorageProvider.GOOGLE_CLOUD:
                return await self._download_from_gcs(storage_path)
            elif self.provider == StorageProvider.LOCAL:
                local_path = os.path.join(self.local_storage_path, storage_path)
                with open(local_path, 'rb') as f:
                    return f.read()
            else:
                raise ValueError(f"Unsupported storage provider: {self.provider}")
                
        except Exception as e:
            logger.error(f"Failed to download media {storage_path}: {e}")
            raise
    
    async def delete_media(self, storage_path: str) -> bool:
        """Delete media from storage."""
        try:
            if self.provider in [StorageProvider.S3, StorageProvider.CLOUDFLARE_R2]:
                self.s3_client.delete_object(Bucket=self.bucket_name, Key=storage_path)
            elif self.provider == StorageProvider.GOOGLE_CLOUD:
                await self._delete_from_gcs(storage_path)
            elif self.provider == StorageProvider.LOCAL:
                local_path = os.path.join(self.local_storage_path, storage_path)
                if os.path.exists(local_path):
                    os.remove(local_path)
            
            logger.info(f"Successfully deleted media {storage_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete media {storage_path}: {e}")
            return False
    
    async def generate_presigned_url(
        self, 
        storage_path: str, 
        expiration: int = 3600
    ) -> str:
        """Generate presigned URL for temporary access."""
        if self.provider in [StorageProvider.S3, StorageProvider.CLOUDFLARE_R2]:
            try:
                url = self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={'Bucket': self.bucket_name, 'Key': storage_path},
                    ExpiresIn=expiration
                )
                return url
            except Exception as e:
                logger.error(f"Failed to generate presigned URL for {storage_path}: {e}")
                raise
        elif self.provider == StorageProvider.GOOGLE_CLOUD:
            return await self._generate_gcs_signed_url(storage_path, expiration)
        else:
            # For local storage, return direct URL
            return self._generate_public_url(storage_path)
    
    def _generate_file_id(self, tenant_id: int, filename: str) -> str:
        """Generate unique file ID."""
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        hash_input = f"{tenant_id}_{filename}_{timestamp}".encode()
        file_hash = hashlib.md5(hash_input).hexdigest()[:8]
        return f"{timestamp}_{file_hash}"
    
    def _generate_storage_path(self, tenant_id: int, file_id: str, filename: str) -> str:
        """Generate storage path with tenant isolation."""
        # Extract file extension
        _, ext = os.path.splitext(filename)
        ext = ext.lower() or '.mp4'
        
        # Create path: tenant_id/year/month/file_id.ext
        now = datetime.utcnow()
        return f"tenant_{tenant_id}/{now.year:04d}/{now.month:02d}/{file_id}{ext}"
    
    def _generate_public_url(self, storage_path: str) -> str:
        """Generate public URL for the file."""
        if self.provider == StorageProvider.S3:
            return f"https://{self.bucket_name}.s3.{self.aws_region}.amazonaws.com/{storage_path}"
        elif self.provider == StorageProvider.CLOUDFLARE_R2:
            return f"{settings.CLOUDFLARE_R2_PUBLIC_DOMAIN}/{storage_path}"
        elif self.provider == StorageProvider.GOOGLE_CLOUD:
            return f"https://storage.googleapis.com/{self.bucket_name}/{storage_path}"
        elif self.provider == StorageProvider.LOCAL:
            return f"{settings.LOCAL_CDN_URL}/{storage_path}"
        else:
            raise ValueError(f"Unsupported storage provider: {self.provider}")
    
    async def _upload_to_s3(
        self, 
        storage_path: str, 
        content: bytes, 
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Upload to S3 or S3-compatible storage."""
        extra_args = {
            'ContentType': content_type,
            'CacheControl': 'max-age=31536000',  # 1 year cache
        }
        
        if metadata:
            extra_args['Metadata'] = {k: str(v) for k, v in metadata.items()}
        
        # Use asyncio to run the blocking S3 operation
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            None,
            lambda: self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=storage_path,
                Body=content,
                **extra_args
            )
        )
    
    async def _upload_to_local(self, storage_path: str, content: bytes) -> str:
        """Upload to local storage."""
        local_path = os.path.join(self.local_storage_path, storage_path)
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # Write file
        with open(local_path, 'wb') as f:
            f.write(content)
        
        return self._generate_public_url(storage_path)
    
    async def _upload_to_gcs(
        self,
        storage_path: str,
        content: bytes,
        content_type: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """Upload to Google Cloud Storage."""
        loop = asyncio.get_event_loop()
        blob = self.gcs_bucket.blob(storage_path)
        
        # Set content type and metadata
        blob.content_type = content_type
        if metadata:
            blob.metadata = {k: str(v) for k, v in metadata.items()}
            
        await loop.run_in_executor(None, lambda: blob.upload_from_string(content))

    async def _download_from_gcs(self, storage_path: str) -> bytes:
        """Download from Google Cloud Storage."""
        loop = asyncio.get_event_loop()
        blob = self.gcs_bucket.blob(storage_path)
        return await loop.run_in_executor(None, lambda: blob.download_as_bytes())

    async def _delete_from_gcs(self, storage_path: str) -> bool:
        """Delete from Google Cloud Storage."""
        loop = asyncio.get_event_loop()
        blob = self.gcs_bucket.blob(storage_path)
        await loop.run_in_executor(None, lambda: blob.delete())
        return True

    async def _generate_gcs_signed_url(self, storage_path: str, expiration: int) -> str:
        """Generate signed URL for Google Cloud Storage."""
        loop = asyncio.get_event_loop()
        blob = self.gcs_bucket.blob(storage_path)
        
        # Generate a signed URL for the blob
        # The URL will be valid for 'expiration' seconds
        url = await loop.run_in_executor(
            None,
            lambda: blob.generate_signed_url(expiration=timedelta(seconds=expiration))
        )
        return url
    
    def _detect_media_format(self, filename: str) -> MediaFormat:
        """Detect media format from filename."""
        _, ext = os.path.splitext(filename.lower())

        format_map = {
            '.mp4': MediaFormat.MP4,
            '.mov': MediaFormat.MOV,
            '.webm': MediaFormat.WEBM,
            '.m3u8': MediaFormat.HLS
        }

        return format_map.get(ext, MediaFormat.MP4)


# Create service instance
media_storage_service = MediaStorageService(
    provider=StorageProvider(settings.STORAGE_PROVIDER or StorageProvider.LOCAL),
    bucket_name=settings.S3_BUCKET_NAME,
    aws_access_key=settings.AWS_ACCESS_KEY_ID,
    aws_secret_key=settings.AWS_SECRET_ACCESS_KEY,
    aws_region=settings.AWS_REGION,
    gcp_project_id=settings.GCP_PROJECT_ID
)
