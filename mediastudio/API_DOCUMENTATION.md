# API Documentation for MediaStudio

## ⚠️ IMPORTANT: APIs We're Using

**WE ARE USING THE GEMINI API (REST), NOT VERTEX AI, NOT SDK**

### Video Generation API (Veo)
- **API**: Gemini API (REST)
- **Base URL**: `https://generativelanguage.googleapis.com/v1beta`
- **Endpoint**: `/models/{model}:predictLongRunning`
- **Authentication**: `x-goog-api-key: {API_KEY}`
- **Models**: `veo-2.0-generate-001`, `veo-3.0-generate-001`

### Image Generation API (Imagen)
- **API**: Gemini API (REST)
- **Base URL**: `https://generativelanguage.googleapis.com/v1beta`
- **Endpoint**: `/models/gemini-2.5-flash-image-preview:generateContent`
- **Authentication**: `x-goog-api-key: {API_KEY}`

## 📚 Official Documentation URLs

### Primary Documentation
1. **Gemini API Video Generation**: https://ai.google.dev/gemini-api/docs/video
2. **Gemini API Cookbook (Veo)**: https://colab.research.google.com/github/google-gemini/cookbook/blob/main/quickstarts/Get_started_Veo.ipynb
3. **Gemini API Reference**: https://ai.google.dev/api

### Secondary Documentation
4. **Gemini API Models**: https://ai.google.dev/api/models
5. **Gemini API Quickstart**: https://ai.google.dev/gemini-api/docs/quickstart

## 🔧 Correct Request Structure for Video with Reference Image

```bash
curl -s "https://generativelanguage.googleapis.com/v1beta/models/veo-2.0-generate-001:predictLongRunning" \
  -H "x-goog-api-key: $GEMINI_API_KEY" \
  -H "Content-Type: application/json" \
  -X "POST" \
  -d '{
    "instances": [{
        "prompt": "dance salsa",
        "image": {
          "imageBytes": "base64_encoded_image_data",
          "mimeType": "image/png"
        }
      }
    ],
    "parameters": {
      "aspectRatio": "16:9",
      "durationSeconds": 5
    }
  }'
```

## ❌ Common Mistakes to Avoid

1. **Don't use Vertex AI endpoints** (`aiplatform.googleapis.com`)
2. **Don't use SDK methods** (`@google/genai` for video generation)
3. **Don't use `bytesBase64Encoded`** field (doesn't exist in Gemini API)
4. **Don't use OAuth tokens** (use API keys)

## ✅ What We're Actually Using

- **File**: `api/src/services/geminiService.ts`
- **Function**: `generateVideo()`
- **API**: Gemini API REST
- **Authentication**: API Key
- **Request Structure**: `{ instances: [...], parameters: {...} }`
