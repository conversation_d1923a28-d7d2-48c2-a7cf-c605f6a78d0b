{"name": "ecommerce-media-studio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest"}, "dependencies": {"react": "^19.1.1", "react-dom": "^19.1.1", "react-mentions": "^4.4.10", "react-router-dom": "^7.8.2", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^2.1.0", "styled-jsx": "^5.1.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.14.0", "@types/react-virtualized-auto-sizer": "^1.0.8", "@types/react-window": "^1.8.8", "jsdom": "^26.1.0", "typescript": "~5.8.2", "vite": "^6.2.0", "vitest": "^3.2.4"}}