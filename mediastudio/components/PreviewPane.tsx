import React from "react";
import type { Asset } from "../types";
import { API_BASE_URL } from "../services/geminiService";
import { Thumbnail } from "./Thumbnail";
import { generateDisplayName } from "../utils/assetNaming";

// Simple gallery of generated assets with basic filters
export const PreviewPane: React.FC = () => {
  const [assets, setAssets] = React.useState<Asset[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  // Pagination
  const [nextCursor, setNextCursor] = React.useState<string | null>(null);
  const [isLoadingMore, setIsLoadingMore] = React.useState(false);

  // Gallery filters (client-side)
  const [typeFilter, setTypeFilter] = React.useState<"all" | "image" | "video">(
    "all"
  );
  const [promptQuery, setPromptQuery] = React.useState<string>("");
  const [debouncedQuery, setDebouncedQuery] = React.useState<string>("");
  const [hasPromptOnly, setHasPromptOnly] = React.useState<boolean>(false);

  // Collections data and mapping for filters
  const [availableCollections, setAvailableCollections] = React.useState<
    { id: string; name: string; color: string }[]
  >([]);
  const [selectedCollectionIds, setSelectedCollectionIds] = React.useState<
    string[]
  >([]);
  const [collectionsByProduct, setCollectionsByProduct] = React.useState<
    Record<string, string[]>
  >({});
  const [collOpen, setCollOpen] = React.useState(false);

  // Debounce query to reduce recompute churn
  React.useEffect(() => {
    const t = window.setTimeout(
      () => setDebouncedQuery(promptQuery.trim().toLowerCase()),
      200
    );
    return () => window.clearTimeout(t);
  }, [promptQuery]);

  // Helper to map API items -> Asset
  const mapItemsToAssets = React.useCallback((items: any[]): Asset[] => {
    const mapped: Asset[] = (items || [])
      .map((a: any) => {
        const productId = String(a?.product_id ?? "");
        const type = (a?.type as "image" | "video") || "image";
        const fileUri = String(a?.file_uri || "");
        const preview = String(a?.preview_uri || "");
        const prompt = typeof a?.prompt === "string" ? a.prompt : "";
        const filename = (() => {
          const name = (fileUri.split("/").pop() || "").trim();
          if (name) return name;
          return type === "video" ? "generated.mp4" : "generated.jpg";
        })();
        const url = type === "video" ? preview || fileUri : fileUri;
        const asset = {
          id: String(a?.id || ""),
          productId,
          url,
          type,
          filename,
          fileUrl: fileUri || undefined,
          previewUrl: preview || undefined,
          prompt: prompt || undefined,
        } as Asset;

        // Generate display name for @mentions
        asset.displayName = generateDisplayName(asset);

        return asset;
      })
      .filter((x: Asset) => x && x.id);

    // Dedupe by id, newest-first as provided by API
    const out: Asset[] = [];
    const seen = new Set<string>();
    for (const m of mapped) {
      if (seen.has(m.id)) continue;
      seen.add(m.id);
      out.push(m);
    }
    return out;
  }, []);

  // Initial paged load (smaller first batch)
  React.useEffect(() => {
    let cancelled = false;
    const load = async () => {
      try {
        setLoading(true);
        setError(null);
        const resp = await fetch(`${API_BASE_URL}/assets/generated?limit=60`);
        if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
        const data = await resp.json();
        if (cancelled) return;
        const items: Array<any> = Array.isArray(data?.items) ? data.items : [];
        const out = mapItemsToAssets(items);
        setAssets(out);
        setNextCursor(
          typeof data?.next_cursor === "string" ? data.next_cursor : null
        );
      } catch (e: any) {
        if (cancelled) return;
        setError(
          typeof e?.message === "string"
            ? `Failed to load: ${e.message}`
            : "Failed to load gallery"
        );
        setAssets([]);
        setNextCursor(null);
      } finally {
        if (!cancelled) setLoading(false);
      }
    };
    load();
    return () => {
      cancelled = true;
    };
  }, [mapItemsToAssets]);

  // Load more via cursor (appends to list)
  const loadMore = React.useCallback(async () => {
    if (!nextCursor || isLoadingMore) return;
    setIsLoadingMore(true);
    try {
      const params = new URLSearchParams();
      params.set("limit", "60");
      params.set("cursor", nextCursor);
      const resp = await fetch(
        `${API_BASE_URL}/assets/generated?${params.toString()}`
      );
      if (!resp.ok) throw new Error(`HTTP ${resp.status}`);
      const data = await resp.json();
      const items: Array<any> = Array.isArray(data?.items) ? data.items : [];
      const mapped = mapItemsToAssets(items);
      setAssets((prev) => {
        const seen = new Set(prev.map((x) => x.id));
        const appended = mapped.filter((m) => !seen.has(m.id));
        return [...prev, ...appended];
      });
      setNextCursor(
        typeof data?.next_cursor === "string" ? data.next_cursor : null
      );
    } catch {
      // swallow; UI will keep current items
    } finally {
      setIsLoadingMore(false);
    }
  }, [nextCursor, isLoadingMore, mapItemsToAssets]);

  // Bottom sentinel for auto "infinite scroll"
  const sentinelRef = React.useRef<HTMLDivElement | null>(null);
  React.useEffect(() => {
    if (!sentinelRef.current) return;
    const el = sentinelRef.current;
    const io = new IntersectionObserver(
      (entries) => {
        const e = entries[0];
        if (e && e.isIntersecting) {
          // Trigger load-more when sentinel enters view
          if (nextCursor && !isLoadingMore) {
            void loadMore();
          }
        }
      },
      { root: null, rootMargin: "600px 0px 0px 0px", threshold: 0 }
    );
    io.observe(el);
    return () => io.disconnect();
  }, [loadMore, nextCursor, isLoadingMore]);

  // Fetch available collections for the collections filter UI
  React.useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        const resp = await fetch(`${API_BASE_URL}/catalog/collections`);
        if (!resp.ok) return;
        const data = await resp.json();
        if (cancelled) return;
        const items: Array<{ id: string; name: string; color: string }> =
          Array.isArray(data?.items) ? data.items : [];
        setAvailableCollections(items);
      } catch {}
    })();
    return () => {
      cancelled = true;
    };
  }, []);

  const filteredAssets = React.useMemo(() => {
    const q = debouncedQuery;
    const selected = new Set(selectedCollectionIds || []);
    return assets.filter((a) => {
      const typeOk = typeFilter === "all" || a.type === typeFilter;
      const text = `${a.prompt || ""} ${a.filename || ""}`.toLowerCase();
      const queryOk = q === "" || text.includes(q);
      const promptOk =
        !hasPromptOnly ||
        (typeof a.prompt === "string" && a.prompt.trim().length > 0);
      const colOk =
        selected.size === 0 ||
        (Array.isArray(collectionsByProduct[a.productId]) &&
          collectionsByProduct[a.productId].some((cid) => selected.has(cid)));
      return typeOk && queryOk && promptOk && colOk;
    });
  }, [
    assets,
    typeFilter,
    debouncedQuery,
    hasPromptOnly,
    selectedCollectionIds,
    collectionsByProduct,
  ]);

  const onSelect = React.useCallback(() => {}, []);

  return (
    <div className="w-full h-full flex-1 self-stretch overflow-auto p-4 lg:p-6 bg-transparent">
      {loading ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm">
          Loading…
        </div>
      ) : error ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-amber-700 dark:text-amber-300 text-sm">
          {error}
        </div>
      ) : assets.length === 0 ? (
        <div className="h-full min-h-[200px] flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm">
          No generated assets yet.
        </div>
      ) : (
        <>
          {/* Filters toolbar (tags) */}
          <div className="mb-3 flex items-center gap-2 flex-wrap">
            {/* Type tags */}
            <button
              type="button"
              onClick={() => setTypeFilter("all")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "all"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "all"}
              title="All media"
            >
              All
            </button>
            <button
              type="button"
              onClick={() => setTypeFilter("image")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "image"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "image"}
              title="Images"
            >
              Images
            </button>
            <button
              type="button"
              onClick={() => setTypeFilter("video")}
              className={`px-2.5 py-1 text-xs rounded-full border ${
                typeFilter === "video"
                  ? "bg-gray-200 dark:bg-gray-600 text-gray-900 dark:text-gray-100 border-gray-300 dark:border-gray-600"
                  : "bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 border-gray-300 dark:border-gray-600"
              }`}
              aria-pressed={typeFilter === "video"}
              title="Videos"
            >
              Videos
            </button>

            {/* Count tag aligned right */}
            <span className="ml-auto inline-flex items-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-100 px-2.5 py-0.5 text-[11px] font-medium">
              {filteredAssets.length} items
            </span>
          </div>

          {/* Simple Grid */}
          <div className="relative h-full w-full overflow-auto">
            <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 pb-10">
              {filteredAssets.map((asset) => (
                <div key={asset.id} className="w-full aspect-square">
                  <Thumbnail
                    asset={asset}
                    isSelected={false}
                    onSelect={onSelect}
                    isGenerated
                    navGroup="gallery"
                  />
                </div>
              ))}
            </div>

            {/* Bottom sentinel for auto-load */}
            <div ref={sentinelRef} className="w-full h-10" />
            {isLoadingMore && (
              <div className="absolute bottom-2 left-1/2 -translate-x-1/2 text-xs text-gray-500 dark:text-gray-400">
                Loading more…
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};
