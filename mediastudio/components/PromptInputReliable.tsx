import React from "react";
import { MentionsInput, Mention } from "react-mentions";
import type { Asset, Product } from "../types";
import { generateDisplayName } from "../utils/assetNaming";
import "./mentions-alignment.css";

interface PromptInputReliableProps {
  productId: string;
  productTitle: string;
  value: string;
  onChange: (productId: string, value: string) => void;
  className?: string;
  assets?: Asset[];
  products?: Product[];
}

export const PromptInputReliable: React.FC<PromptInputReliableProps> = ({
  productId,
  productTitle,
  value,
  onChange,
  className = "",
  assets = [],
  products = [],
}) => {
  const [isDragOver, setIsDragOver] = React.useState(false);

  // Find the current product for context
  const currentProduct = products.find((p) => p.id === productId);

  // Prepare mention data from assets
  const mentionData = React.useMemo(() => {
    return assets.map((asset) => {
      const product = products.find((p) => p.id === asset.productId);
      const displayName = generateDisplayName(asset, product, 30);

      return {
        id: asset.id,
        display: displayName,
        assetId: asset.id,
        filename: asset.filename,
        url: asset.url,
        productId: asset.productId,
      };
    });
  }, [assets, products]);

  const handleChange = React.useCallback(
    (event: any, newValue: string) => {
      onChange(productId, newValue);
    },
    [productId, onChange]
  );

  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
    setIsDragOver(true);
  }, []);

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        const dragDataStr = e.dataTransfer.getData("application/json");
        if (!dragDataStr) return;

        const dragData = JSON.parse(dragDataStr);
        if (dragData.type !== "image") return;

        // Find the asset data
        const asset = assets.find((a) => a.id === dragData.assetId);
        if (!asset) return;

        const product = products.find((p) => p.id === asset.productId);
        const displayName = generateDisplayName(asset, product, 30);

        // Insert mention at the end of current text
        const newValue =
          value + (value ? " " : "") + `@[${displayName}](${asset.id})`;
        onChange(productId, newValue);
      } catch (error) {
        console.error("Failed to handle dropped image:", error);
      }
    },
    [assets, products, value, productId, onChange]
  );

  const renderSuggestion = React.useCallback(
    (
      entry: any,
      search: string,
      highlightedDisplay: React.ReactNode,
      index: number,
      focused: boolean
    ) => {
      return (
        <div
          className={`px-3 py-2 cursor-pointer ${
            focused
              ? "bg-blue-500 text-white"
              : "bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700"
          }`}
        >
          <div className="flex items-center gap-2">
            <img
              src={entry.url}
              alt={entry.display}
              className="w-8 h-8 object-cover rounded"
              loading="lazy"
            />
            <span className="text-sm font-medium">{highlightedDisplay}</span>
          </div>
        </div>
      );
    },
    []
  );

  // Custom styles for react-mentions - Comprehensive alignment fix
  const mentionsInputStyle = {
    control: {
      backgroundColor: "transparent",
      fontSize: "14px",
      fontWeight: "400",
      fontFamily:
        "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
      lineHeight: "20px", // Fixed pixel value instead of relative
      letterSpacing: "normal",
      textRendering: "optimizeLegibility",
      WebkitFontSmoothing: "antialiased",
      MozOsxFontSmoothing: "grayscale",
    },
    "&multiLine": {
      control: {
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
        minHeight: 128,
        border: "none",
        outline: "none",
        fontSize: "14px",
        lineHeight: "20px",
        fontWeight: "400",
        letterSpacing: "normal",
        textRendering: "optimizeLegibility",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
      },
      highlighter: {
        padding: "8px 8px 8px 8px", // Explicit all sides
        margin: "0",
        border: "none",
        fontSize: "14px",
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
        lineHeight: "20px",
        fontWeight: "400",
        letterSpacing: "normal",
        color: "transparent !important", // Force transparent
        overflow: "hidden",
        whiteSpace: "pre-wrap",
        wordWrap: "break-word",
        wordBreak: "break-word",
        textRendering: "optimizeLegibility",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
        boxSizing: "border-box",
        position: "absolute",
        top: "0",
        left: "0",
        right: "0",
        bottom: "0",
        pointerEvents: "none",
      },
      input: {
        padding: "8px 8px 8px 8px", // Exact same as highlighter
        margin: "0",
        border: "none",
        outline: "none",
        backgroundColor: "transparent",
        color: "inherit",
        resize: "none",
        fontSize: "14px",
        fontFamily:
          "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
        lineHeight: "20px",
        fontWeight: "400",
        letterSpacing: "normal",
        overflow: "hidden",
        whiteSpace: "pre-wrap",
        wordWrap: "break-word",
        wordBreak: "break-word",
        textRendering: "optimizeLegibility",
        WebkitFontSmoothing: "antialiased",
        MozOsxFontSmoothing: "grayscale",
        boxSizing: "border-box",
        position: "relative",
        zIndex: "1",
      },
    },
    suggestions: {
      list: {
        backgroundColor: "white",
        border: "1px solid #e5e7eb",
        borderRadius: 6,
        fontSize: 14,
        maxHeight: 200,
        overflow: "auto",
        boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
        zIndex: 1000,
      },
      item: {
        padding: 0,
        borderBottom: "1px solid #f3f4f6",
        "&focused": {
          backgroundColor: "#3b82f6",
          color: "white",
        },
      },
    },
  };

  // Dark mode styles
  const darkModeStyles = {
    ...mentionsInputStyle,
    suggestions: {
      ...mentionsInputStyle.suggestions,
      list: {
        ...mentionsInputStyle.suggestions.list,
        backgroundColor: "#374151",
        border: "1px solid #4b5563",
        color: "#f9fafb",
      },
      item: {
        ...mentionsInputStyle.suggestions.item,
        borderBottom: "1px solid #4b5563",
      },
    },
  };

  return (
    <div className="relative">
      <div
        className={`mentions-alignment-fix w-full min-h-32 p-2 text-sm text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700/50 rounded-md focus-within:ring-2 focus-within:ring-primary-focus focus-within:bg-white dark:focus-within:bg-gray-700 transition-colors ${
          isDragOver
            ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : ""
        } ${className}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{
          // Ensure perfect text alignment for react-mentions
          fontFamily:
            "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif",
          fontSize: "14px",
          lineHeight: "20px",
          fontWeight: "400",
          letterSpacing: "normal",
          textRendering: "optimizeLegibility",
          WebkitFontSmoothing: "antialiased",
          MozOsxFontSmoothing: "grayscale",
        }}
      >
        <MentionsInput
          value={value}
          onChange={handleChange}
          style={mentionsInputStyle}
          placeholder={`e.g., A model wearing the ${productTitle.toLowerCase()} in a sunlit park... (Drag images here to attach)`}
          allowSpaceInQuery
          className="w-full h-full"
        >
          <Mention
            trigger="@"
            data={mentionData}
            renderSuggestion={renderSuggestion}
            markup="@[__display__](__id__)"
            displayTransform={(id: string, display: string) => `@${display}`}
            appendSpaceOnAdd
            style={{
              backgroundColor: "#dbeafe",
              color: "#1e40af",
              padding: "2px 4px",
              borderRadius: "4px",
              fontWeight: "500",
            }}
          />
        </MentionsInput>
      </div>

      {/* Drop indicator */}
      {isDragOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-md pointer-events-none">
          <div className="px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-md">
            Drop image to attach inline
          </div>
        </div>
      )}
    </div>
  );
};
