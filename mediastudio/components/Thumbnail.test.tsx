import React from 'react'
import { describe, it, expect, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { Thumbnail } from './Thumbnail'
import type { Asset } from '../types'

const asset: Asset = {
  id: 'a1',
  productId: 'p1',
  url: 'https://picsum.photos/seed/test/800/600',
  type: 'image',
  filename: 'test.jpg',
}

function openPreview() {
  render(
    <div>
      <div id="root" />
      <Thumbnail asset={asset} isSelected={false} onSelect={() => {}} />
    </div>
  )
  // Open preview by clicking the first expand button
  const expandBtns = screen.getAllByRole('button', { name: /enlarge image/i })
  fireEvent.click(expandBtns[0])
}

function mockLayout() {
  // Mock container size and position
  const dialog = screen.getByRole('dialog')
  // inner container is first child div
  const container = dialog.querySelector('div > div') as HTMLDivElement
  expect(container).toBeTruthy()

  Object.defineProperty(container, 'clientWidth', { value: 1000, configurable: true })
  Object.defineProperty(container, 'clientHeight', { value: 800, configurable: true })
  container.getBoundingClientRect = () => ({
    left: 100,
    top: 50,
    width: 1000,
    height: 800,
    right: 1100,
    bottom: 850,
    x: 100,
    y: 50,
    toJSON: () => ({}),
  } as any)

  // Mock image natural size
  const img = container.querySelector('img') as HTMLImageElement
  Object.defineProperty(img, 'naturalWidth', { value: 1200, configurable: true })
  Object.defineProperty(img, 'naturalHeight', { value: 900, configurable: true })
  fireEvent.load(img)

  return { container, img }
}

describe('Thumbnail preview modal', () => {
  beforeEach(() => {
    // JSDOM lacks layout; ensure scroll is not locked across tests
    document.body.style.overflow = ''
  })

  it('opens and closes with escape', () => {
    openPreview()
    expect(screen.getByRole('dialog')).toBeInTheDocument()
    fireEvent.keyDown(window, { key: 'Escape' })
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument()
  })

  it('click toggles zoom and reset', () => {
    openPreview()
    const { img } = mockLayout()

    // initial zoom 1
    expect(img.style.transform).toContain('scale(1)')

    // click center to zoom in
    fireEvent.click(img, { clientX: 600, clientY: 450 })
    expect(img.style.transform).toContain('scale(2)')

    // click again to reset
    fireEvent.click(img, { clientX: 600, clientY: 450 })
    expect(img.style.transform).toContain('scale(1)')
    expect(img.style.transform).toContain('translate(0px, 0px)')
  })

  it('drags to pan when zoomed', () => {
    openPreview()
    const { img } = mockLayout()

    // zoom in first
    fireEvent.click(img, { clientX: 600, clientY: 450 })

    // drag by 30px,30px
    fireEvent.pointerDown(img, { clientX: 600, clientY: 450 })
    fireEvent.pointerMove(img, { clientX: 630, clientY: 480 })
    fireEvent.pointerUp(img, { clientX: 630, clientY: 480 })

    expect(img.style.transform).toMatch(/translate\([^0]/)
    expect(img.style.transform).toContain('scale(2)')
  })

  it('wheel zooms at pointer', () => {
    openPreview()
    const { container, img } = mockLayout()

    // wheel up to zoom in
    fireEvent.wheel(container, { deltaY: -100, clientX: 600, clientY: 450 })
    expect(img.style.transform).toMatch(/scale\((1\.2|1\.25)/)

    // wheel down to zoom out
    fireEvent.wheel(container, { deltaY: 100, clientX: 600, clientY: 450 })
    // might clamp to >= 1
    expect(img.style.transform).toContain('scale(1)')
  })
})
