import React from "react";
import { CheckIcon, SparklesIcon, PlusIcon } from "./icons";

type ModelOption = {
  value: string;
  title: string;
  subtitle: string;
  disabled?: boolean;
  badge?: string;
  comingSoon?: boolean;
  // Optional icon renderer (w-5 h-5 recommended)
  icon?: React.ReactNode;
};

interface ModelSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: ModelOption[];
  className?: string;
  dense?: boolean;
}

export const ModelSelect: React.FC<ModelSelectProps> = ({
  label,
  value,
  onChange,
  options,
  className,
  dense = false,
}) => {
  const [open, setOpen] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const onDoc = (e: MouseEvent) => {
      if (!ref.current) return;
      if (!ref.current.contains(e.target as Node)) setOpen(false);
    };
    document.addEventListener("mousedown", onDoc);
    return () => document.removeEventListener("mousedown", onDoc);
  }, []);

  const selected = options.find((o) => o.value === value) || options[0];

  return (
    <div className={`relative ${className || ""}`} ref={ref}>
      <button
        type="button"
        onClick={() => setOpen((v) => !v)}
        className={`flex ${dense ? 'h-12' : 'h-16'} w-full items-center justify-between rounded-lg bg-gray-100 px-3 text-left hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors`}
        aria-haspopup="dialog"
        aria-expanded={open}
      >
        <div className="min-w-0 pr-2">
          <div className="text-xs text-gray-500 dark:text-gray-400">{label}</div>
          <div className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
            {selected?.title || "Select"}
          </div>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 flex-shrink-0 text-gray-400 dark:text-gray-500 transition-transform ${
            open ? "rotate-180" : ""
          }`}
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      </button>

      {open && (
        <div className="absolute bottom-full mb-2 left-0 w-[22rem] max-w-[80vw] rounded-2xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-xl overflow-hidden">
          <div className="px-4 py-3 text-sm font-semibold text-gray-700 dark:text-gray-200 border-b border-gray-200 dark:border-gray-700">
            Select model
          </div>
          <ul className="py-2 max-h-[70vh] overflow-auto">
            {options.map((opt) => {
              const isActive = !opt.disabled && opt.value === value;
              return (
                <li key={opt.value} className="px-3 py-1.5">
                  <button
                    type="button"
                    disabled={!!opt.disabled}
                    onClick={() => {
                      if (opt.disabled) return;
                      onChange(opt.value);
                      setOpen(false);
                    }}
                    className={`w-full flex items-center gap-3 rounded-xl px-2 py-2 text-left transition-colors ${
                      opt.disabled
                        ? "opacity-60 cursor-not-allowed bg-gray-100 dark:bg-gray-700/30"
                        : isActive
                        ? "bg-gray-100 dark:bg-gray-700/60"
                        : "hover:bg-gray-100 dark:hover:bg-gray-700/40"
                    }`}
                  >
                    {/* Icon tile */}
                    <div className="flex items-center justify-center h-12 w-12 rounded-xl bg-gray-200 text-primary dark:bg-gray-700/60 dark:text-primary">
                      {opt.icon || <SparklesIcon className="h-5 w-5" />}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <div className="font-medium text-gray-900 dark:text-gray-100 truncate">
                          {opt.title}
                        </div>
                        {opt.badge && (
                          <span className="text-[10px] px-1.5 py-0.5 rounded-md bg-lime-300/30 text-lime-700 dark:bg-lime-400/20 dark:text-lime-300 whitespace-nowrap">
                            {opt.badge}
                          </span>
                        )}
                        {opt.comingSoon && (
                          <span className="text-[10px] px-1.5 py-0.5 rounded-md bg-gray-300/40 text-gray-700 dark:bg-gray-700/40 dark:text-gray-300 whitespace-nowrap">
                            Coming soon
                          </span>
                        )}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                        {opt.subtitle}
                      </div>
                    </div>
                    {!opt.disabled && isActive && (
                      <CheckIcon className="h-5 w-5 text-primary" />
                    )}
                  </button>
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

// Simple brand-ish icons for the list
export const BrandGIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M12 2a10 10 0 100 20 10 10 0 000-20zm3.5 11H12v-2h6c.06.32.1.65.1 1 0 3.86-3.14 7-7 7a7 7 0 110-14c1.7 0 3.26.6 4.46 1.61l-1.42 1.42A4.98 4.98 0 0011.1 6 5 5 0 1017 11c0 .34-.03.67-.1 1z" />
  </svg>
);

export const FluxIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M12 3l9 16H3L12 3zm0 4.5L6.8 17h10.4L12 7.5z" />
  </svg>
);

export const WanIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M12 2l2.4 4.9L20 8l-4 4 1 5.9L12 15l-5 2.9L8 12 4 8l5.6-1.1L12 2z" />
  </svg>
);

export const MultiRefIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
    <rect x="3" y="3" width="10" height="10" rx="2" />
    <path d="M20 10h-4a1 1 0 100 2h3v3a1 1 0 102 0v-4a1 1 0 00-1-1z" />
  </svg>
);

export const OpenAIIcon: React.FC<React.SVGProps<SVGSVGElement>> = (props) => (
  <svg viewBox="0 0 24 24" fill="currentColor" {...props}>
    <path d="M11.99 2.5c1.7 0 3.27.66 4.45 1.73 1.3-.43 2.78-.2 3.9.62 1.12.81 1.83 2.07 1.9 3.44.04.77-.14 1.53-.5 2.2.66 1.21.78 2.67.28 4-.5 1.33-1.57 2.38-2.9 2.87-.64.24-1.32.34-1.99.3-.66 1.19-1.76 2.1-3.1 2.53-1.34.43-2.8.32-4.05-.31-.66.38-1.42.58-2.2.58-1.37 0-2.63-.62-3.45-1.74-.81-1.1-1.06-2.54-.68-3.86-.98-.9-1.6-2.16-1.67-3.55-.07-1.4.39-2.77 1.29-3.82.49-.58 1.1-1.03 1.79-1.34.19-1.63 1.12-3.11 2.53-4 1.41-.88 3.15-1.09 4.73-.57Zm-3.9 5.1c-.32.19-.61.43-.85.72-.62.74-.93 1.69-.88 2.65.06.97.46 1.88 1.1 2.58l.47.5-.2.66c-.27.87-.13 1.83.38 2.57.51.73 1.33 1.18 2.22 1.21.59.02 1.16-.13 1.65-.43l.56-.34.56.34c.83.5 1.86.6 2.79.3.93-.3 1.7-.99 2.08-1.88l.28-.66.71.02c.55.02 1.1-.07 1.6-.25 1.01-.37 1.83-1.18 2.2-2.2.38-1.01.3-2.17-.2-3.12l-.33-.64.44-.57c.35-.45.52-1.02.5-1.59-.04-.9-.48-1.74-1.2-2.26-.72-.54-1.66-.7-2.51-.45l-.67.2-.5-.47c-1.03-.96-2.4-1.48-3.8-1.4-1.39.08-2.7.75-3.58 1.85l-.44.55-.7-.02c-.6-.02-1.2.12-1.73.4Z" />
  </svg>
);
