import React from "react";
import type { Product, Asset, MainTab, PromptWithImages } from "../types";
import { Chip } from "./Chip";
import { Thumbnail } from "./Thumbnail";
import { PromptInputReliable } from "./PromptInputReliable";
import { PlusIcon, CopyIcon, CheckIcon, FunnelIcon } from "./icons";
import { createPromptFromText } from "../utils/promptUtils";

interface ProductGridProps {
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, PromptWithImages>;
  onPromptChange: (productId: string, value: PromptWithImages) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onSelectAllProducts: (isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generationBatch?: {
    batchId: string;
    total: number;
    completed: number;
    failed: number;
    status: string;
  } | null;
  failedPlaceholderIds?: Set<string>;
  generatedImages?: Record<string, Asset[]>;
  rowCountsByProduct?: Record<
    string,
    { completed: number; total: number; pending: number }
  >;
  availableCollections?: { id: string; name: string; color: string }[];
  collectionFilters?: string[];
  onCollectionFiltersChange?: (ids: string[]) => void;
  productTotalCount?: number;
  // Inline search (compact, header height must not increase)
  searchQuery?: string;
  onSearchQueryChange?: (q: string) => void;
  // Row-state filters and sorting (client-only)
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (v: boolean) => void;
  filterHasGenerated?: boolean;
  onFilterHasGeneratedChange?: (v: boolean) => void;
  sortMode?: "default" | "selected_first" | "generated_first";
  onSortModeChange?: (
    m: "default" | "selected_first" | "generated_first"
  ) => void;
}

const ProductActionTile: React.FC<{ label: string; onClick: () => void }> = ({
  label,
  onClick,
}) => (
  <button
    onClick={onClick}
    className="flex items-center space-x-2 w-full p-2 rounded-md bg-gray-100 dark:bg-gray-700/50 hover:bg-gray-200 dark:hover:bg-gray-600/50 transition-colors cursor-pointer"
  >
    <div className="flex-shrink-0 flex items-center justify-center h-6 w-6 rounded-md bg-gray-200 dark:bg-gray-800 text-gray-500 dark:text-gray-400">
      <PlusIcon className="h-4 w-4" />
    </div>
    <span className="text-xs font-medium text-gray-700 dark:text-gray-300">
      {label}
    </span>
  </button>
);

// Collections filter popover component
const CollectionsFilter: React.FC<{
  options: { id: string; name: string; color: string }[];
  selectedIds: string[];
  onChange?: (ids: string[]) => void;
  compact?: boolean;
}> = ({ options, selectedIds, onChange, compact = false }) => {
  const [open, setOpen] = React.useState(false);
  const toggle = () => setOpen((v) => !v);
  const isSelected = (id: string) => selectedIds.includes(id);
  const applyToggle = (id: string) => {
    if (!onChange) return;
    if (isSelected(id)) onChange(selectedIds.filter((x) => x !== id));
    else onChange([...selectedIds, id]);
  };
  const count = selectedIds.length;

  return (
    <div className="relative">
      <button
        type="button"
        onClick={toggle}
        className={`${
          compact
            ? "px-2 py-0.5 text-[11px] leading-none h-6"
            : "px-3 py-1.5 text-xs"
        } font-medium rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600`}
      >
        Collections{count > 0 ? ` (${count})` : ""}
      </button>
      {open && (
        <div className="absolute mt-1 w-64 max-h-64 overflow-auto z-20 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-2">
          {options.length === 0 && (
            <div className="text-xs text-gray-500 p-2">No collections</div>
          )}
          {options.map((opt) => (
            <button
              type="button"
              key={opt.id}
              onClick={() => applyToggle(opt.id)}
              className={`w-full flex items-center justify-between px-2 py-1.5 text-sm rounded hover:bg-gray-100 dark:hover:bg-gray-700 ${
                isSelected(opt.id)
                  ? "bg-gray-50 dark:bg-gray-700/60"
                  : "bg-transparent"
              }`}
            >
              <span className="flex items-center gap-2 text-gray-800 dark:text-gray-200">
                <span
                  className={`h-2 w-2 rounded-full ${opt.color} border border-gray-300 dark:border-gray-600`}
                />
                {opt.name}
              </span>
              <span className="h-4 w-4 inline-flex items-center justify-center text-gray-400">
                {isSelected(opt.id) && <CheckIcon className="h-4 w-4" />}
              </span>
            </button>
          ))}
          <div className="flex items-center justify-between gap-2 pt-2 mt-2 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              onClick={() => onChange && onChange([])}
            >
              Clear
            </button>
            <button
              type="button"
              className="text-xs text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100"
              onClick={() => setOpen(false)}
            >
              Done
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

const ProductRow: React.FC<{
  product: Product;
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, PromptWithImages>;
  onPromptChange: (productId: string, value: PromptWithImages) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generationBatch?: {
    batchId: string;
    total: number;
    completed: number;
    failed: number;
    status: string;
  } | null;
  generatedImages?: Record<string, Asset[]>;
  rowCountsByProduct?: Record<
    string,
    { completed: number; total: number; pending: number }
  >;
  failedPlaceholderIds?: Set<string>;
}> = ({
  product,
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onCopyPromptToAll,
  generationBatch,
  generatedImages = {},
  rowCountsByProduct = {},
  failedPlaceholderIds = new Set(),
}) => {
  const isRowSelected = selectedProductIds.has(product.id);
  const hasPlaceholder = React.useMemo(
    () => product.assets.some((a) => a.id.startsWith("temp_")),
    [product.assets]
  );
  const isGenerating =
    (generationBatch?.status === "processing" &&
      selectedProductIds.has(product.id)) ||
    hasPlaceholder;
  const existingAssetIds = React.useMemo(
    () => new Set(product.assets.map((a) => a.id)),
    [product.assets]
  );

  const productGeneratedImages = generatedImages[product.id] || [];
  const filteredGeneratedImages = React.useMemo(
    () => productGeneratedImages.filter((a) => !existingAssetIds.has(a.id)),
    [productGeneratedImages, existingAssetIds]
  );

  const rowCounts = rowCountsByProduct[product.id] || {
    completed: 0,
    total: 0,
    pending: 0,
  };

  // Row-level completion indicator that appears when generation finishes
  const [justCompleted, setJustCompleted] = React.useState(false);
  const prevHasPlaceholder = React.useRef<boolean>(false);
  const prevIsGenerating = React.useRef<boolean>(false);
  React.useEffect(() => {
    // Transition from having a placeholder to none -> mark as completed briefly
    if (prevHasPlaceholder.current && !hasPlaceholder) {
      setJustCompleted(true);
      const t = window.setTimeout(() => setJustCompleted(false), 5000);
      return () => window.clearTimeout(t);
    }
    prevHasPlaceholder.current = hasPlaceholder;
  }, [hasPlaceholder]);
  React.useEffect(() => {
    // Fallback: transition from generating to not generating -> mark as completed briefly
    if (prevIsGenerating.current && !isGenerating) {
      setJustCompleted(true);
      const t = window.setTimeout(() => setJustCompleted(false), 5000);
      return () => window.clearTimeout(t);
    }
    prevIsGenerating.current = isGenerating || false;
  }, [isGenerating]);

  return (
    <tr
      className={`border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
        isRowSelected ? "bg-gray-50 dark:bg-gray-800/50" : ""
      }`}
    >
      {/* Product & Assets Column */}
      <td className="p-3 align-top">
        <div className="mb-3 flex justify-between items-center gap-4">
          <div
            className="font-medium text-sm text-gray-900 dark:text-gray-100 truncate min-w-0"
            title={product.title}
          >
            {product.title}
          </div>
          <div className="flex-shrink-0 flex items-center gap-1.5">
            {(product.collections ?? []).map((col) => (
              <Chip key={col.id} color={col.color}>
                {col.name}
              </Chip>
            ))}
          </div>
        </div>

        <div
          className="flex space-x-2 overflow-x-auto pb-2 asset-scrollbar"
          data-product-row={product.id}
        >
          {product.assets.map((asset) => (
            <div key={asset.id} className="w-24 flex-shrink-0">
              <Thumbnail
                asset={asset}
                isSelected={selectedAssetIds.has(asset.id)}
                onSelect={onAssetSelect}
                isGenerated={false}
                isFailedPlaceholder={failedPlaceholderIds.has(asset.id)}
              />
            </div>
          ))}

          {/* Show generated images as selectable assets (deduped against product.assets) */}
          {filteredGeneratedImages.map((asset) => (
            <div key={asset.id} className="w-24 flex-shrink-0">
              <Thumbnail
                asset={asset}
                isSelected={selectedAssetIds.has(asset.id)}
                onSelect={onAssetSelect}
                isGenerated={true}
                isFailedPlaceholder={false}
              />
            </div>
          ))}
        </div>
      </td>

      {/* Selection/Copy Column (moved before Prompt) */}
      <td className="pl-1 pr-3 py-3 align-top">
        <div className="flex flex-col items-center h-full pt-0 space-y-6">
          <label className="relative inline-flex items-center justify-center h-6 w-6 cursor-pointer">
            <input
              type="checkbox"
              className="appearance-none h-5 w-5 rounded-[4px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 peer hover:border-gray-600 dark:hover:border-gray-300"
              checked={isRowSelected}
              onChange={(e) =>
                onProductSelectionChange(product.id, e.target.checked)
              }
              title={isRowSelected ? "Deselect row" : "Select row"}
            />
            <CheckIcon className="pointer-events-none absolute h-4 w-4 text-gray-400 dark:text-gray-500 opacity-0 peer-checked:opacity-100 peer-hover:text-gray-600 dark:peer-hover:text-gray-300" />
          </label>
          <button
            onClick={() => onCopyPromptToAll(product.id)}
            title="Copy prompt to all rows"
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          >
            <CopyIcon className="h-5 w-5" />
          </button>

          {/* Row generation status indicator */}
          <div
            className="h-6 flex items-center justify-center gap-1"
            aria-live="polite"
          >
            {isGenerating && (
              <div
                className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent"
                title="Generating…"
                aria-label="Generating…"
              />
            )}
            {!isGenerating && justCompleted && (
              <div
                className="rounded-full h-4 w-4 bg-green-500 flex items-center justify-center"
                title="Completed"
                aria-label="Completed"
              >
                <svg
                  className="w-2.5 h-2.5 text-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            )}
            <span className="text-[10px] tabular-nums text-gray-500 dark:text-gray-400">
              {isGenerating
                ? `${rowCounts.completed}/${rowCounts.total}`
                : `${rowCounts.completed}`}
            </span>
          </div>
        </div>
      </td>

      {/* Prompt Column */}
      <td className="pl-1 pr-1 py-3 align-top">
        <PromptInputReliable
          productId={product.id}
          productTitle={product.title}
          value={(prompts[product.id] || createPromptFromText("")).getText()}
          onChange={(productId: string, value: string) => {
            // Convert string back to PromptWithImages for compatibility
            const promptWithImages = createPromptFromText(value);
            onPromptChange(productId, promptWithImages);
          }}
          assets={products.flatMap((p) => p.assets)}
          products={products}
        />
      </td>

      {/* Add-ons Column */}
      <td className="pl-1 pr-1 py-3 align-top">
        <div className="flex flex-col space-y-1.5">
          <ProductActionTile
            label="Model"
            onClick={() => onTabChange("models")}
          />
          <ProductActionTile
            label="Props"
            onClick={() => onTabChange("props")}
          />
          <ProductActionTile
            label="Scenes"
            onClick={() => onTabChange("scenes")}
          />
        </div>
      </td>
    </tr>
  );
};

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onSelectAllProducts,
  onCopyPromptToAll,
  generationBatch,
  failedPlaceholderIds = new Set(),
  generatedImages = {},
  rowCountsByProduct = {},
  availableCollections = [],
  collectionFilters = [],
  onCollectionFiltersChange,
  searchQuery = "",
  onSearchQueryChange,
  filterSelectedOnly = false,
  onFilterSelectedOnlyChange,
  filterHasGenerated = false,
  onFilterHasGeneratedChange,
  sortMode = "default",
  onSortModeChange,
  productTotalCount,
}) => {
  const areAllProductsSelected =
    products.length > 0 && selectedProductIds.size === products.length;

  // Header filters popover
  const [filtersOpen, setFiltersOpen] = React.useState(false);
  const isClientFilterActive =
    (searchQuery ?? "").trim().length > 0 ||
    filterSelectedOnly ||
    filterHasGenerated;

  return (
    <div className="relative flex-1 flex flex-col overflow-y-auto bg-white dark:bg-gray-800">
      <table className="w-full table-fixed">
        <colgroup>
          <col style={{ width: "432px" }} />
          <col style={{ width: "48px" }} />
          <col />
          <col style={{ width: "10rem" }} />
        </colgroup>
        <thead className="sticky top-0 z-10 bg-gray-50 dark:bg-gray-700/50 backdrop-blur-sm">
          <tr>
            <th className="w-[432px] text-left text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase p-3">
              <div className="flex items-center gap-2">
                <span>Item</span>
                <CollectionsFilter
                  options={availableCollections}
                  selectedIds={collectionFilters}
                  onChange={onCollectionFiltersChange}
                  compact
                />
                <input
                  type="text"
                  value={searchQuery ?? ""}
                  onChange={(e) =>
                    onSearchQueryChange && onSearchQueryChange(e.target.value)
                  }
                  placeholder="Search…"
                  className="h-6 px-2 py-0.5 text-[11px] leading-none rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 placeholder-gray-400 w-40 focus:outline-none focus:ring-1 focus:ring-primary"
                />
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setFiltersOpen((v) => !v)}
                    className={`h-6 w-6 inline-flex items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 ${
                      isClientFilterActive ? "ring-1 ring-primary" : ""
                    }`}
                    title="Row filters"
                  >
                    <FunnelIcon className="h-3.5 w-3.5" />
                  </button>
                  {filtersOpen && (
                    <div className="absolute right-0 mt-1 w-64 z-20 rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg p-2">
                      <div className="px-1 py-1">
                        <div className="text-[11px] uppercase tracking-wide text-gray-500 dark:text-gray-400 mb-1">
                          Filters
                        </div>
                        <label className="flex items-center gap-2 px-1 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/60">
                          <input
                            type="checkbox"
                            className="h-3.5 w-3.5"
                            checked={!!filterSelectedOnly}
                            onChange={(e) =>
                              onFilterSelectedOnlyChange &&
                              onFilterSelectedOnlyChange(e.target.checked)
                            }
                          />
                          <span className="text-xs text-gray-800 dark:text-gray-100">
                            Selected only
                          </span>
                        </label>
                        <label className="flex items-center gap-2 px-1 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/60">
                          <input
                            type="checkbox"
                            className="h-3.5 w-3.5"
                            checked={!!filterHasGenerated}
                            onChange={(e) =>
                              onFilterHasGeneratedChange &&
                              onFilterHasGeneratedChange(e.target.checked)
                            }
                          />
                          <span className="text-xs text-gray-800 dark:text-gray-100">
                            Has generated
                          </span>
                        </label>
                      </div>
                      <div className="px-1 py-1 mt-1 border-t border-gray-200 dark:border-gray-700">
                        <div className="text-[11px] uppercase tracking-wide text-gray-500 dark:text-gray-400 mb-1">
                          Sort
                        </div>
                        <label className="flex items-center gap-2 px-1 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/60">
                          <input
                            type="radio"
                            name="rowSort"
                            className="h-3.5 w-3.5"
                            checked={sortMode === "default"}
                            onChange={() =>
                              onSortModeChange && onSortModeChange("default")
                            }
                          />
                          <span className="text-xs text-gray-800 dark:text-gray-100">
                            Default order
                          </span>
                        </label>
                        <label className="flex items-center gap-2 px-1 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/60">
                          <input
                            type="radio"
                            name="rowSort"
                            className="h-3.5 w-3.5"
                            checked={sortMode === "selected_first"}
                            onChange={() =>
                              onSortModeChange &&
                              onSortModeChange("selected_first")
                            }
                          />
                          <span className="text-xs text-gray-800 dark:text-gray-100">
                            Selected first
                          </span>
                        </label>
                        <label className="flex items-center gap-2 px-1 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700/60">
                          <input
                            type="radio"
                            name="rowSort"
                            className="h-3.5 w-3.5"
                            checked={sortMode === "generated_first"}
                            onChange={() =>
                              onSortModeChange &&
                              onSortModeChange("generated_first")
                            }
                          />
                          <span className="text-xs text-gray-800 dark:text-gray-100">
                            Generated first
                          </span>
                        </label>
                      </div>
                      <div className="flex items-center justify-between gap-2 pt-2 mt-2 border-t border-gray-200 dark:border-gray-700">
                        <button
                          type="button"
                          className="text-xs text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          onClick={() => {
                            onFilterSelectedOnlyChange &&
                              onFilterSelectedOnlyChange(false);
                            onFilterHasGeneratedChange &&
                              onFilterHasGeneratedChange(false);
                            onSortModeChange && onSortModeChange("default");
                          }}
                        >
                          Clear
                        </button>
                        <button
                          type="button"
                          className="text-xs text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100"
                          onClick={() => setFiltersOpen(false)}
                        >
                          Done
                        </button>
                      </div>
                    </div>
                  )}
                </div>
                <span className="ml-2 inline-flex items-center rounded-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-100 px-2 py-0.5 text-[11px] font-medium">
                  #
                  {isClientFilterActive
                    ? products.length
                    : typeof productTotalCount === "number"
                    ? productTotalCount
                    : products.length}{" "}
                  items
                </span>
              </div>
            </th>
            <th className="w-12 text-center text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase pl-1 pr-3 py-3">
              <label className="relative inline-flex items-center justify-center h-6 w-6 cursor-pointer">
                <input
                  type="checkbox"
                  className="appearance-none h-5 w-5 rounded-[4px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 peer hover:border-gray-600 dark:hover:border-gray-300"
                  checked={areAllProductsSelected}
                  onChange={(e) => onSelectAllProducts(e.target.checked)}
                  title="Select all rows"
                />
                <CheckIcon className="pointer-events-none absolute h-4 w-4 text-gray-400 dark:text-gray-500 opacity-0 peer-checked:opacity-100 peer-hover:text-gray-600 dark:peer-hover:text-gray-300" />
              </label>
            </th>
            <th className="text-left text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase pl-1 pr-1 py-3">
              Prompt
            </th>
            <th className="w-40 text-left text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase pl-1 pr-1 py-3">
              Add-ons
            </th>
          </tr>
        </thead>
        <tbody className="bg-white dark:bg-gray-800">
          {products.map((product) => (
            <ProductRow
              key={product.id}
              product={product}
              products={products}
              selectedAssetIds={selectedAssetIds}
              onAssetSelect={onAssetSelect}
              prompts={prompts}
              onPromptChange={onPromptChange}
              onTabChange={onTabChange}
              selectedProductIds={selectedProductIds}
              onProductSelectionChange={onProductSelectionChange}
              onCopyPromptToAll={onCopyPromptToAll}
              generationBatch={generationBatch}
              generatedImages={generatedImages}
              rowCountsByProduct={rowCountsByProduct}
              failedPlaceholderIds={failedPlaceholderIds}
            />
          ))}
        </tbody>
      </table>
      {/* Spacer to prevent the floating bar from obscuring the last row */}
      <div className="h-40 flex-shrink-0" />
    </div>
  );
};
