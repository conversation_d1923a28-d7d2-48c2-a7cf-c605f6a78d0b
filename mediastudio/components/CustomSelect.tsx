import React, { useState, useEffect, useRef } from 'react';
import { ChevronDownIcon } from './icons';

interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  icon?: string;
  type?: string;
}

interface CustomSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  className?: string;
  popupPosition?: 'top' | 'bottom';
  compact?: boolean; // smaller chip-style variant (no label, rounded-full)
  dense?: boolean; // shorter height, keeps label/value layout
  allowAddNew?: boolean;
  onAddNew?: (newValue: string) => void;
  addNewPlaceholder?: string;
  allowRemove?: boolean;
  onRemove?: (value: string) => void;
  removableTypes?: string[]; // array of types that can be removed
}

export const CustomSelect: React.FC<CustomSelectProps> = ({
  label,
  value,
  onChange,
  options,
  className,
  popupPosition = 'top',
  compact = false,
  dense = false,
  allowAddNew = false,
  onAddNew,
  addNewPlaceholder = "Add new...",
  allowRemove = false,
  onRemove,
  removableTypes = []
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newValue, setNewValue] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setIsAddingNew(false);
        setNewValue('');
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (isAddingNew && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isAddingNew]);

  const selectedOption = options.find(opt => opt.value === value);

  const handleAddNew = () => {
    if (newValue.trim() && onAddNew) {
      onAddNew(newValue.trim());
      setNewValue('');
      setIsAddingNew(false);
      setIsOpen(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddNew();
    } else if (e.key === 'Escape') {
      setIsAddingNew(false);
      setNewValue('');
    }
  };

  const popupClasses = popupPosition === 'top'
    ? "absolute bottom-full z-20 mb-1 w-full rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-800"
    : "absolute top-full z-20 mt-1 w-full rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-800";

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={compact
          ? "flex h-8 w-full items-center justify-between rounded-full bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm px-2 text-left border border-gray-300 dark:border-gray-600 shadow hover:bg-white dark:hover:bg-gray-700 transition-colors"
          : `flex ${dense ? 'h-12' : 'h-16'} w-full items-center justify-between rounded-lg bg-gray-100 px-3 text-left hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors`}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={`${label}, Current value: ${selectedOption ? selectedOption.label : 'Select...'}`}
      >
        {compact ? (
          <div className="flex items-center gap-1 pr-2">
            {selectedOption?.icon && <span className="text-xs">{selectedOption.icon}</span>}
            <div className="text-xs font-medium text-gray-800 dark:text-gray-200 truncate">
              {selectedOption ? selectedOption.label : 'Select'}
            </div>
          </div>
        ) : (
          <div className="min-w-0 pr-2">
            <div className="text-xs text-gray-500 dark:text-gray-400">{label}</div>
            <div className="flex items-center gap-2">
              {selectedOption?.icon && <span className="text-sm">{selectedOption.icon}</span>}
              <div className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">
                {selectedOption ? selectedOption.label : 'Select...'}
              </div>
            </div>
          </div>
        )}
        <ChevronDownIcon className={`${compact ? 'h-4 w-4' : 'h-5 w-5'} flex-shrink-0 text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className={popupClasses}>
          <ul className="max-h-60 overflow-y-auto py-1" role="listbox">
            {options.map(option => (
              <li
                key={option.value}
                onClick={() => {
                  if (option.disabled) return; // Skip disabled
                  onChange(option.value);
                  setIsOpen(false);
                }}
                className={`px-3 ${compact ? 'py-1.5 text-xs' : 'py-2 text-sm'} truncate ${
                  option.disabled
                    ? 'opacity-50 cursor-not-allowed text-gray-500 dark:text-gray-400'
                    : 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 ' + (value === option.value ? 'font-semibold text-primary' : 'text-gray-800 dark:text-gray-200')
                }`}
                role="option"
                aria-selected={value === option.value}
                aria-disabled={option.disabled ? true : undefined}
              >
                <div className="flex items-center justify-between gap-2">
                  <div className="flex items-center gap-2 min-w-0">
                    {option.icon && <span className="text-sm flex-shrink-0">{option.icon}</span>}
                    <span className="truncate">{option.label}</span>
                  </div>
                  {allowRemove && removableTypes.includes(option.type || '') && onRemove && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onRemove(option.value);
                      }}
                      className="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 flex-shrink-0 text-sm p-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded"
                      title={`Remove ${option.label}`}
                    >
                      ✕
                    </button>
                  )}
                </div>
              </li>
            ))}

            {allowAddNew && (
              <li
                onClick={() => setIsAddingNew(true)}
                className={`px-3 ${compact ? 'py-1.5 text-xs' : 'py-2 text-sm'} cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 border-t border-gray-200 dark:border-gray-600 mt-1`}
                role="option"
              >
                {isAddingNew ? (
                  <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                    <input
                      ref={inputRef}
                      value={newValue}
                      onChange={(e) => setNewValue(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={addNewPlaceholder}
                      className="flex-1 bg-transparent outline-none text-gray-800 dark:text-gray-200 placeholder-gray-500 dark:placeholder-gray-400"
                      onClick={(e) => e.stopPropagation()}
                    />
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleAddNew();
                      }}
                      className="text-xs px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Add
                    </button>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span className="text-sm">➕</span>
                    <span>{addNewPlaceholder}</span>
                  </div>
                )}
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
};
