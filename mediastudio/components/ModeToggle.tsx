import React from 'react';
import type { GenerationMode } from '../types';
import { ImageIcon, VideoIcon } from './icons';

interface ModeToggleProps {
  mode: GenerationMode;
  onChange: (mode: GenerationMode) => void;
  orientation?: 'vertical' | 'horizontal';
  fullWidth?: boolean;
}

export const ModeToggle: React.FC<ModeToggleProps> = ({ mode, onChange, orientation = 'vertical', fullWidth = false }) => {
  const containerClasses = orientation === 'vertical'
    ? "flex flex-col p-1 bg-gray-100 dark:bg-gray-800 rounded-lg space-y-1 h-16 w-32"
    : `flex flex-row p-1 bg-gray-100 dark:bg-gray-800 rounded-lg space-x-1 h-10 ${fullWidth ? 'w-full' : ''}`;

  const buttonClasses = `flex-1 px-4 text-sm font-semibold rounded-md flex items-center justify-center gap-2 transition-colors`;
  const activeClasses = 'bg-white dark:bg-gray-700 shadow-sm text-primary';
  const inactiveClasses = 'text-gray-500 hover:text-gray-800 dark:hover:text-gray-200';

  return (
    <div className={containerClasses}>
      <button onClick={() => onChange('image')} className={`${buttonClasses} ${mode === 'image' ? activeClasses : inactiveClasses}`}>
        <ImageIcon className="h-5 w-5" /> Image
      </button>
      <button onClick={() => onChange('video')} className={`${buttonClasses} ${mode === 'video' ? activeClasses : inactiveClasses}`}>
        <VideoIcon className="h-5 w-5" /> Video
      </button>
    </div>
  );
};
