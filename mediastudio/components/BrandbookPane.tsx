import React from 'react';
import { UploadIcon, FileTextIcon, PaletteIcon, TypeIcon } from './icons';

// Placeholder for uploaded brand assets
const BrandAssetCard: React.FC<{ name: string; type: 'Logo' | 'Color Palette' | 'Font'; children: React.ReactNode }> = ({ name, type, children }) => {
  const Icon = type === 'Logo' ? FileTextIcon : type === 'Color Palette' ? PaletteIcon : TypeIcon;
  return (
    <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3 text-left">
      <div className="aspect-[4/3] w-full rounded-md mb-2 bg-gray-100 dark:bg-gray-800 flex items-center justify-center overflow-hidden">
        {children}
      </div>
      <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{name}</p>
      <div className="flex items-center mt-1">
        <Icon className="h-3 w-3 text-gray-500 dark:text-gray-400 mr-1.5" />
        <p className="text-xs text-gray-500 dark:text-gray-400">{type}</p>
      </div>
    </div>
  );
};


export const BrandbookPane: React.FC = () => {
  return (
    <div className="w-full max-w-4xl mx-auto flex flex-col items-center text-center p-8 md:p-12 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl">
      {/* Image Collage */}
      <div className="relative h-48 w-full mb-10 flex items-center justify-center">
          <img src="https://images.unsplash.com/photo-1596799321993-9c1a39ce672b?w=400&auto=format&fit=crop" alt="Brand Colors" className="absolute w-36 rounded-lg shadow-xl transform -rotate-12 top-10 left-12 z-10 border-2 border-white dark:border-gray-600" />
          <img src="https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&auto=format&fit=crop" alt="Product Style" className="absolute w-60 rounded-lg shadow-2xl transform rotate-3 z-20 border-2 border-white dark:border-gray-600" />
          <img src="https://images.unsplash.com/photo-1618477388954-7852f32655ec?w=400&auto=format&fit=crop" alt="Brand Logo" className="absolute w-32 rounded-lg shadow-xl transform rotate-12 top-6 right-24 z-10 border-2 border-white dark:border-gray-600" />
          <img src="https://images.unsplash.com/photo-1556740772-1a741367b93e?w=500&auto=format&fit=crop" alt="Lifestyle Vibe" className="absolute w-40 rounded-lg shadow-xl transform rotate-6 bottom-0 right-14 z-30 border-2 border-white dark:border-gray-600" />
      </div>

      <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
        Define Your Visual Identity with Brandbook
      </h2>
      <p className="mt-4 text-gray-600 dark:text-gray-300 max-w-xl">
        Upload your core brand assets—logos, color palettes, typography, and key visuals. Our AI will learn your style to ensure every generated image and video is perfectly on-brand.
      </p>
       <p className="mt-2 text-gray-600 dark:text-gray-300 max-w-xl">
        Brandbook also powers intelligent prompt generation, helping you create consistent, high-quality content for your entire product catalogue at scale.
      </p>
      
      <div className="flex items-center mt-8">
        <button className="flex items-center justify-center space-x-2 px-6 py-3 text-sm font-semibold text-gray-800 dark:text-gray-100 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors shadow-lg">
          <UploadIcon className="h-5 w-5" />
          <span>Upload Brand Assets</span>
        </button>
      </div>
      
      {/* Uploaded Assets Display */}
    </div>
  );
};