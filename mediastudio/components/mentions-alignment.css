/* React-mentions alignment fixes to prevent double text overlay */

.mentions-alignment-fix .mentions__control {
  position: relative !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', '<PERSON>buntu', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 14px !important;
  line-height: 20px !important;
  font-weight: 400 !important;
  letter-spacing: normal !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.mentions-alignment-fix .mentions__highlighter {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  pointer-events: none !important;
  color: transparent !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 8px 8px 8px 8px !important;
  box-sizing: border-box !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 14px !important;
  line-height: 20px !important;
  font-weight: 400 !important;
  letter-spacing: normal !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow: hidden !important;
}

.mentions-alignment-fix .mentions__input {
  position: relative !important;
  z-index: 1 !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 8px 8px 8px 8px !important;
  box-sizing: border-box !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 14px !important;
  line-height: 20px !important;
  font-weight: 400 !important;
  letter-spacing: normal !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  white-space: pre-wrap !important;
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow: hidden !important;
  resize: none !important;
  outline: none !important;
}

.mentions-alignment-fix .mentions__mention {
  background-color: #3b82f6 !important;
  color: white !important;
  padding: 2px 4px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  display: inline !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 14px !important;
  line-height: 20px !important;
  letter-spacing: normal !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

/* Dark mode support */
.dark .mentions-alignment-fix .mentions__mention {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Ensure suggestions dropdown works properly */
.mentions-alignment-fix .mentions__suggestions {
  position: absolute !important;
  z-index: 1000 !important;
}

.mentions-alignment-fix .mentions__suggestions__list {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

.dark .mentions-alignment-fix .mentions__suggestions__list {
  background-color: #374151 !important;
  border-color: #4b5563 !important;
}

.mentions-alignment-fix .mentions__suggestions__item {
  padding: 8px 12px !important;
  border-bottom: 1px solid #f3f4f6 !important;
  cursor: pointer !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
  font-size: 14px !important;
  line-height: 20px !important;
}

.dark .mentions-alignment-fix .mentions__suggestions__item {
  border-bottom-color: #4b5563 !important;
  color: #f9fafb !important;
}

.mentions-alignment-fix .mentions__suggestions__item--focused {
  background-color: #3b82f6 !important;
  color: white !important;
}

/* Fix for any remaining alignment issues */
.mentions-alignment-fix * {
  box-sizing: border-box !important;
}

/* Prevent text selection issues */
.mentions-alignment-fix .mentions__highlighter::selection {
  background: transparent !important;
}

.mentions-alignment-fix .mentions__highlighter::-moz-selection {
  background: transparent !important;
}
