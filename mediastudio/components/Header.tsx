import React from "react";
import { Link, useLocation } from "react-router-dom";
import { SunIcon, MoonIcon, ChevronDownIcon } from "./icons";

interface HeaderProps {
  theme: "light" | "dark";
  setTheme: (theme: "light" | "dark") => void;
  onOpenScraper?: () => void;
  testingMode?: boolean;
  onTestingModeChange?: (enabled: boolean) => void;
  testingStats?: {
    total_products: number;
    domains: Array<{domain: string; count: number}>;
    message?: string;
  } | null;
}

export const Header: React.FC<HeaderProps> = ({
  theme,
  setTheme,
  onOpenScraper,
  testingMode = false,
  onTestingModeChange,
  testingStats,
}) => {
  const location = useLocation();
  const isFetchPage = location.pathname === '/fetch';

  return (
    <header className="flex-shrink-0 h-12 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 flex items-center px-4 justify-between">
      <div className="flex items-center gap-2">
        {isFetchPage && (
          <Link
            to="/"
            className="h-8 px-3 rounded-lg bg-gray-600 text-white hover:bg-gray-700 text-sm flex items-center"
          >
            ← Back to Main App
          </Link>
        )}
      </div>
      <div className="flex items-center space-x-4">
        <button
          onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
          className="text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
        >
          {theme === "dark" ? (
            <SunIcon className="h-5 w-5" />
          ) : (
            <MoonIcon className="h-5 w-5" />
          )}
        </button>
        <div className="text-sm">
          <span className="text-gray-500 dark:text-gray-400">Credits: </span>
          <span className="font-medium text-gray-800 dark:text-gray-200">
            1,204
          </span>
        </div>
        <div className="flex items-center space-x-2">
          <img
            src="https://picsum.photos/seed/avatar/32"
            alt="User Avatar"
            className="h-8 w-8 rounded-full"
          />
          <button className="flex items-center text-sm font-medium">
            My Account
            <ChevronDownIcon className="h-4 w-4 text-gray-500 ml-1" />
          </button>
        </div>
      </div>
    </header>
  );
};
