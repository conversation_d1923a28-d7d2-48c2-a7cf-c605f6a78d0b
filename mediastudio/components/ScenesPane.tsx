import React from 'react';
import { ModeToggle } from './ModeToggle';
import type { GenerationMode } from '../types';
import { SparklesIcon } from './icons';

interface ScenesPaneProps {
  generationMode: GenerationMode;
  onModeChange: (mode: GenerationMode) => void;
}

const SceneCard: React.FC<{ title: string; description: string; imageUrl: string }> = ({ title, description, imageUrl }) => (
    <div className="relative group aspect-[4/5] bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden cursor-pointer">
        <img src={imageUrl} alt={title} className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent" />
        <div className="absolute bottom-0 left-0 p-4">
            <h3 className="font-bold text-white text-lg">{title}</h3>
            <p className="text-gray-200 text-sm mt-1">{description}</p>
        </div>
        <div className="absolute top-3 right-3 bg-white/20 backdrop-blur-sm p-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity">
            <SparklesIcon className="h-5 w-5 text-white" />
        </div>
    </div>
);

const imageScenes = [
    { title: 'Studio Product Shot', description: 'Clean, professional lighting', imageUrl: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&auto=format&fit=crop' },
    { title: 'Lifestyle Context', description: 'Product in a natural setting', imageUrl: 'https://images.unsplash.com/photo-1556740772-1a741367b93e?w=500&auto=format&fit=crop' },
    { title: 'Flat Lay', description: 'Top-down, organized composition', imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&auto=format&fit=crop' },
    { title: 'Minimalist Scene', description: 'Geometric shapes and shadows', imageUrl: 'https://images.unsplash.com/photo-1618477388954-7852f32655ec?w=400&auto=format&fit=crop' },
    { title: 'Cinematic', description: 'Dramatic, moody lighting', imageUrl: 'https://images.unsplash.com/photo-1596799321993-9c1a39ce672b?w=400&auto=format&fit=crop' },
    { title: 'Bold & Vibrant', description: 'Saturated colors, high energy', imageUrl: 'https://images.unsplash.com/photo-1571939228919-2371473919a4?w=400&auto=format&fit=crop' },
];

const videoScenes = [
    { title: '360° Spin', description: 'Showcase all angles', imageUrl: 'https://images.unsplash.com/photo-1560343090-f0409e92791a?w=400&auto=format&fit=crop' },
    { title: 'Unboxing Sequence', description: 'Build anticipation', imageUrl: 'https://images.unsplash.com/photo-1571939228919-2371473919a4?w=400&auto=format&fit=crop' },
    { title: 'Cinematic Reveal', description: 'Slow-motion, dramatic focus', imageUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ab?w=400&auto=format&fit=crop' },
    { title: 'Stop Motion', description: 'Playful and creative', imageUrl: 'https://images.unsplash.com/photo-1581235720704-06d3acfcb36f?w=400&auto=format&fit=crop' },
    { title: 'Product Demo', description: 'Highlight key features', imageUrl: 'https://images.unsplash.com/photo-1555529771-83d820b13867?w=400&auto=format&fit=crop' },
    { title: 'User Testimonial', description: 'Social proof focus', imageUrl: 'https://images.unsplash.com/photo-1556742502-ec7c0e9f34b1?w=400&auto=format&fit=crop' },
];


export const ScenesPane: React.FC<ScenesPaneProps> = ({ generationMode, onModeChange }) => {
    const scenes = generationMode === 'image' ? imageScenes : videoScenes;
    return (
        <div className="w-full h-full flex flex-col gap-4">
            <div className="flex-shrink-0 flex justify-center">
                <ModeToggle mode={generationMode} onChange={onModeChange} orientation="horizontal" />
            </div>
            <div className="flex-1 grid grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 gap-4 overflow-y-auto pr-2">
                 {scenes.map(scene => (
                    <SceneCard 
                        key={scene.title}
                        title={scene.title}
                        description={scene.description}
                        imageUrl={scene.imageUrl}
                    />
                 ))}
            </div>
        </div>
    );
};
