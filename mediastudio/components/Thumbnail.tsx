import React from "react";
import type { Asset } from "../types";
import { API_BASE_URL } from "../services/geminiService";
import {
  CheckIcon,
  ZoomInIcon,
  XIcon,
  SparklesIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlayIcon,
  CopyIcon,
} from "./icons";

interface ThumbnailProps {
  asset: Asset;
  isSelected: boolean;
  onSelect: (asset: Asset, isMultiSelect: boolean) => void;
  isGenerated?: boolean;
  // When the placeholder failed, stop spinner and show a red X briefly
  isFailedPlaceholder?: boolean;
  // Optional navigation group id to scope modal arrow nav (e.g., 'gallery')
  navGroup?: string;
}

const ThumbnailComponent: React.FC<ThumbnailProps> = ({
  asset,
  isSelected,
  onSelect,
  isGenerated = false,
  isFailedPlaceholder = false,
  navGroup,
}) => {
  const [showPreview, setShowPreview] = React.useState(false);
  const [hasLoaded, setHasLoaded] = React.useState(false);
  const [copySuccess, setCopySuccess] = React.useState(false);

  // Define isPlaceholder early so it can be used in callbacks
  const isPlaceholder = asset.id.startsWith("temp_");

  React.useEffect(() => {
    if (!showPreview) return;
    const onKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") setShowPreview(false);
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [showPreview]);

  const handleCopyPrompt = React.useCallback(
    async (e: React.MouseEvent) => {
      e.stopPropagation();
      if (!asset.prompt) return;

      try {
        await navigator.clipboard.writeText(asset.prompt);
        setCopySuccess(true);
        setTimeout(() => setCopySuccess(false), 2000);
      } catch {
        // Fallback for older browsers
        try {
          const ta = document.createElement("textarea");
          ta.value = asset.prompt;
          ta.style.position = "fixed";
          ta.style.opacity = "0";
          document.body.appendChild(ta);
          ta.focus();
          ta.select();
          document.execCommand("copy");
          document.body.removeChild(ta);
          setCopySuccess(true);
          setTimeout(() => setCopySuccess(false), 2000);
        } catch {
          // Silent fail if copy is not supported
        }
      }
    },
    [asset.prompt]
  );

  const handleDragStart = React.useCallback(
    (e: React.DragEvent) => {
      // Only allow dragging of actual images (not placeholders or videos)
      if (isPlaceholder || asset.type === "video") {
        e.preventDefault();
        return;
      }

      // Set drag data with image information
      const dragData = {
        type: "image",
        assetId: asset.id,
        filename: asset.filename,
        displayName: asset.displayName, // Include display name for @mentions
        url: asset.url,
        productId: asset.productId,
      };

      e.dataTransfer.setData("application/json", JSON.stringify(dragData));
      e.dataTransfer.setData("text/plain", asset.filename); // Fallback
      e.dataTransfer.effectAllowed = "copy";

      // Add visual feedback
      e.currentTarget.style.opacity = "0.5";
    },
    [asset, isPlaceholder]
  );

  const handleDragEnd = React.useCallback((e: React.DragEvent) => {
    // Reset visual feedback
    e.currentTarget.style.opacity = "1";
  }, []);

  const handleClick = (_e: React.MouseEvent) => {
    // For videos, open the preview modal directly; selection is less relevant for outputs
    if (!isPlaceholder && asset.type === "video") {
      setShowPreview(true);
      return;
    }
    // Default to additive toggle; no modifier key required
    onSelect(asset, true);
  };

  // Compute a safe playback URL for videos (use server proxy for Gemini URIs)
  const playbackSrc = React.useMemo(() => {
    const direct =
      asset.type === "video" ? asset.fileUrl || asset.url : asset.url;
    if (asset.type === "video" && typeof direct === "string") {
      if (direct.includes("generativelanguage.googleapis.com")) {
        return `${API_BASE_URL}/assets/${asset.id}/stream`;
      }
      if (direct.startsWith("/assets/")) {
        return `${API_BASE_URL}${direct}`;
      }
    }
    return direct;
  }, [asset]);

  return (
    <div className="relative">
      {/* Copy success message - floating above the thumbnail */}
      {copySuccess && (
        <div className="absolute -top-3 left-1/2 -translate-x-1/2 z-30 px-3 py-2 bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200 text-xs font-medium rounded-lg shadow-sm whitespace-nowrap">
          Prompt copied!
        </div>
      )}

      <div
        onClick={handleClick}
        draggable={!isPlaceholder && asset.type === "image"}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        className={`relative aspect-square rounded-md overflow-hidden group bg-gray-100 dark:bg-gray-700 flex items-center justify-center ${
          isPlaceholder ? "ring-1 ring-blue-500/30" : ""
        } ${
          !isPlaceholder && asset.type === "image"
            ? "cursor-grab active:cursor-grabbing"
            : "cursor-pointer"
        }`}
        data-asset-id={asset.id}
        data-product-id={asset.productId}
        data-asset-type={asset.type}
        data-nav-group={navGroup || undefined}
        data-video-url={
          asset.type === "video" ? (playbackSrc as string) : undefined
        }
        data-placeholder={isPlaceholder ? "1" : "0"}
        role={isPlaceholder ? "status" : undefined}
        aria-busy={isPlaceholder && !isFailedPlaceholder ? true : undefined}
        aria-label={
          isPlaceholder
            ? isFailedPlaceholder
              ? "Generation failed"
              : "Generating thumbnail"
            : !isPlaceholder && asset.type === "image"
            ? "Drag to add to prompt"
            : undefined
        }
      >
        {!isPlaceholder && (
          <>
            {asset.type === "video" ? (
              <img
                src={
                  asset.previewUrl ||
                  // Fallback to a tiny 1x1 PNG so <img> never tries to load the MP4
                  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAc8AAnkB2nJxAQAAAABJRU5ErkJggg=="
                }
                alt={asset.filename}
                loading="lazy"
                decoding="async"
                fetchpriority="low"
                onLoad={() => setHasLoaded(true)}
                onError={() => setHasLoaded(true)}
                className={`w-full h-full object-cover transition-opacity duration-300 ${
                  hasLoaded ? "opacity-100" : "opacity-0"
                }`}
              />
            ) : (
              <img
                src={asset.url}
                alt={asset.filename}
                loading="lazy"
                decoding="async"
                fetchpriority="low"
                onLoad={() => setHasLoaded(true)}
                onError={() => setHasLoaded(true)}
                className={`w-full h-full object-contain transition-opacity duration-300 ${
                  hasLoaded ? "opacity-100" : "opacity-0"
                }`}
              />
            )}
          </>
        )}

        {isPlaceholder && (
          <div
            className={`absolute inset-0 z-10 flex flex-col items-center justify-center ${
              isFailedPlaceholder ? "bg-red-500/15" : "bg-blue-500/15"
            }`}
          >
            {!isFailedPlaceholder ? (
              <>
                <div className="animate-spin rounded-full h-7 w-7 border-2 border-blue-500 border-t-transparent"></div>
                <div className="mt-1.5 text-[11px] font-medium text-blue-700 dark:text-blue-300 select-none">
                  Generating…
                </div>
              </>
            ) : (
              <>
                <div className="rounded-full h-7 w-7 bg-red-500 flex items-center justify-center">
                  <svg
                    className="w-4 h-4 text-white"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    aria-hidden
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm-1.414-5.414a1 1 0 011.414 0L10 12.586l.707-.707a1 1 0 111.414 1.414L11.414 14l.707.707a1 1 0 01-1.414 1.414L10 15.414l-.707.707a1 1 0 01-1.414-1.414L8.586 14l-.707-.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="mt-1.5 text-[11px] font-medium text-red-700 dark:text-red-300 select-none">
                  Failed
                </div>
              </>
            )}
          </div>
        )}
        {/* Centered play affordance (visual only) */}
        {!isPlaceholder && asset.type === "video" && (
          <>
            <div
              className="pointer-events-none absolute inset-0 flex items-center justify-center"
              aria-hidden
            >
              <div className="h-10 w-10 rounded-full bg-black/35 text-white flex items-center justify-center">
                <PlayIcon className="h-5 w-5" />
              </div>
            </div>
          </>
        )}

        {/* Hover tools: enlarge button */}
        {!isPlaceholder && (
          <button
            type="button"
            onClick={(e) => {
              e.stopPropagation();
              setShowPreview(true);
            }}
            title="Enlarge"
            aria-label="Enlarge image"
            className="absolute bottom-1 right-1 z-10 opacity-0 group-hover:opacity-100 transition-opacity inline-flex items-center justify-center h-7 w-7 rounded-md border border-gray-300 dark:border-gray-600 bg-white/90 dark:bg-gray-800/90 text-gray-700 dark:text-gray-200 shadow-sm hover:bg-white dark:hover:bg-gray-700"
          >
            <ZoomInIcon className="h-4 w-4" />
          </button>
        )}

        {/* selection overlay */}
        <div
          className={`absolute inset-0 transition-all duration-200 pointer-events-none ${
            isSelected
              ? "bg-blue-500/50"
              : isPlaceholder
              ? "bg-transparent"
              : "bg-black/20 opacity-0 group-hover:opacity-100"
          }`}
        />
        {/* Top-right selection checkbox (matches 2nd-column style) */}
        <button
          type="button"
          role="checkbox"
          aria-checked={isSelected}
          title={isSelected ? "Deselect" : "Select"}
          aria-label={isSelected ? "Deselect image" : "Select image"}
          onClick={(e) => {
            e.stopPropagation();
            // Toggle selection without modifiers
            onSelect(asset, true);
          }}
          className={`absolute top-1.5 right-1.5 z-10 h-5 w-5 rounded-[4px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-primary-focus transition-opacity ${
            isSelected ? "" : "opacity-0 group-hover:opacity-100"
          }`}
        >
          {isSelected && (
            <CheckIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
          )}
        </button>

        {/* Copy prompt button (top-left, only for generated assets with prompts) */}
        {isGenerated && asset.prompt && (
          <button
            type="button"
            onClick={handleCopyPrompt}
            title="Copy prompt"
            aria-label="Copy generation prompt"
            className="absolute top-1.5 left-1.5 z-10 h-5 w-5 rounded-[4px] border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 flex items-center justify-center focus:outline-none focus:ring-2 focus:ring-primary-focus transition-opacity duration-200 opacity-0 group-hover:opacity-100 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <CopyIcon className="h-3 w-3 text-gray-400 dark:text-gray-500" />
          </button>
        )}

        {/* Fullscreen-ish preview overlay */}
        {showPreview && !isPlaceholder && (
          <PreviewModal
            src={asset.type === "video" ? (playbackSrc as string) : asset.url}
            alt={asset.filename}
            assetId={asset.id}
            productId={asset.productId}
            mediaType={asset.type}
            poster={asset.previewUrl || undefined}
            onClose={() => setShowPreview(false)}
          />
        )}
      </div>
    </div>
  );
};

export const Thumbnail = React.memo(ThumbnailComponent);

const PreviewModal: React.FC<{
  src: string; // image src or video src depending on mediaType
  alt: string;
  assetId: string;
  productId: string;
  mediaType?: "image" | "video";
  poster?: string; // for videos
  onClose: () => void;
}> = ({
  src,
  alt,
  assetId,
  productId,
  mediaType = "image",
  poster,
  onClose,
}) => {
  // Track current media so navigation can update it
  const [currSrc, setCurrSrc] = React.useState(src);
  const [currAlt, setCurrAlt] = React.useState(alt);
  const [currAssetId, setCurrAssetId] = React.useState(assetId);
  const [currType, setCurrType] = React.useState(mediaType);
  const [currPoster, setCurrPoster] = React.useState(poster);

  const [currProductId, setCurrProductId] = React.useState(productId);
  const [zoom, setZoom] = React.useState(1);
  const [isDragging, setIsDragging] = React.useState(false);
  const [offset, setOffset] = React.useState({ x: 0, y: 0 });
  const containerRef = React.useRef<HTMLDivElement>(null);
  const imgRef = React.useRef<HTMLImageElement>(null);
  const videoRef = React.useRef<HTMLVideoElement>(null);

  // Natural size of the image and base rendered size (at scale 1)
  const naturalSize = React.useRef({ w: 0, h: 0 });
  // Base rendered image size at zoom=1. The modal container matches this.
  const [baseSize, setBaseSize] = React.useState({ w: 0, h: 0 });

  // Drag helpers
  const dragStart = React.useRef({ x: 0, y: 0 });
  const startOffset = React.useRef({ x: 0, y: 0 });
  const movedEnough = React.useRef(false);

  const imgCursorClass =
    zoom === 1
      ? "cursor-zoom-in"
      : isDragging
      ? "cursor-grabbing"
      : "cursor-grab";

  const clampOffset = React.useCallback(
    (next: { x: number; y: number }, z: number) => {
      const rect = containerRef.current?.getBoundingClientRect();
      const cw = rect?.width ?? 0;
      const ch = rect?.height ?? 0;
      const w = baseSize.w * z;
      const h = baseSize.h * z;
      // Keep a tiny safety padding to prevent 1px bleed from borders/rounding
      const padding = 1;
      const maxX = Math.max(0, (w - cw) / 2) + padding;
      const maxY = Math.max(0, (h - ch) / 2) + padding;
      return {
        x: Math.max(-maxX, Math.min(maxX, next.x)),
        y: Math.max(-maxY, Math.min(maxY, next.y)),
      };
    },
    [baseSize.w, baseSize.h]
  );

  // Compute base image size (zoom=1) to fit within viewport bounds
  const recalcBaseSize = React.useCallback(() => {
    const maxW = Math.floor(window.innerWidth * 0.92); // 92vw
    const maxH = Math.floor(window.innerHeight * 0.9); // 90vh
    const nw = naturalSize.current.w;
    const nh = naturalSize.current.h;
    if (!nw || !nh) {
      setBaseSize({ w: maxW || 800, h: maxH || 600 });
      return;
    }
    const r = nw / nh;
    const rc = maxW / maxH;
    if (rc > r) {
      const h0 = maxH;
      const w0 = Math.floor(h0 * r);
      setBaseSize({ w: w0, h: h0 });
    } else {
      const w0 = maxW;
      const h0 = Math.floor(w0 / r);
      setBaseSize({ w: w0, h: h0 });
    }
  }, []);

  // Re-clamp offset when base size or zoom changes
  React.useEffect(() => {
    setOffset((prev) => clampOffset(prev, zoom));
  }, [baseSize.w, baseSize.h, zoom, clampOffset]);

  const applyZoomAtPoint = React.useCallback(
    (clientX: number, clientY: number, nextZoom: number) => {
      setZoom((prevZoom) => {
        const rect = containerRef.current?.getBoundingClientRect();
        if (!rect) return prevZoom;
        const cx = rect.left + rect.width / 2;
        const cy = rect.top + rect.height / 2;
        const relX = clientX - cx;
        const relY = clientY - cy;
        const k = nextZoom / prevZoom;
        if (prevZoom === nextZoom) return prevZoom;
        setOffset((prev) =>
          clampOffset(
            { x: prev.x - relX * (k - 1), y: prev.y - relY * (k - 1) },
            nextZoom
          )
        );
        return nextZoom;
      });
    },
    [clampOffset]
  );

  const applyZoomAtCenter = React.useCallback(
    (nextZoom: number) => {
      const rect = containerRef.current?.getBoundingClientRect();
      const cx = rect ? rect.left + rect.width / 2 : window.innerWidth / 2;
      const cy = rect ? rect.top + rect.height / 2 : window.innerHeight / 2;
      applyZoomAtPoint(cx, cy, nextZoom);
    },
    [applyZoomAtPoint]
  );

  const handleToggleZoom = (e: React.MouseEvent) => {
    e.preventDefault();
    if (movedEnough.current) {
      // Suppress click-to-zoom after a drag
      movedEnough.current = false;
      return;
    }
    if (zoom === 1) {
      const next = 2;
      applyZoomAtPoint(e.clientX, e.clientY, next);
    } else {
      setZoom(1);
      setOffset({ x: 0, y: 0 });
    }
  };

  const onPointerDown = (e: React.PointerEvent) => {
    if (zoom === 1) return;
    movedEnough.current = false;
    setIsDragging(true);
    dragStart.current = { x: e.clientX, y: e.clientY };
    startOffset.current = { ...offset };
    try {
      (e.target as Element).setPointerCapture?.(e.pointerId);
    } catch {}
  };

  const onPointerMove = (e: React.PointerEvent) => {
    if (!isDragging) return;
    const dx = e.clientX - dragStart.current.x;
    const dy = e.clientY - dragStart.current.y;
    if (!movedEnough.current && Math.hypot(dx, dy) > 4)
      movedEnough.current = true;
    setOffset(
      clampOffset(
        { x: startOffset.current.x + dx, y: startOffset.current.y + dy },
        zoom
      )
    );
  };

  const onPointerUp = (e: React.PointerEvent) => {
    if (!isDragging) return;
    setIsDragging(false);
    try {
      (e.target as Element).releasePointerCapture?.(e.pointerId);
    } catch {}
  };

  const onWheel = (e: React.WheelEvent) => {
    const step = 0.25;
    const delta = -e.deltaY; // up is zoom in
    let next = zoom + (delta > 0 ? step : -step);
    next = Math.max(1, Math.min(4, next));
    if (next === zoom) return; // no change; allow default scroll when at base

    // Only prevent page scroll if we are or will be zoomed
    if (zoom > 1 || next > 1) e.preventDefault();
    applyZoomAtPoint(e.clientX, e.clientY, next);
  };

  // DOM helpers for navigation
  // If the opened media belongs to a custom navigation group (e.g., Gallery),
  // prefer navigating within that group. Otherwise, fall back to product rows.
  const getGroupContext = React.useCallback(() => {
    const el = document.querySelector(
      `[data-asset-id="${CSS.escape(currAssetId)}"][data-nav-group]`
    ) as HTMLElement | null;
    if (!el)
      return null as null | {
        group: string;
        elements: HTMLElement[];
        index: number;
      };
    const group = el.getAttribute("data-nav-group") || "";
    if (!group) return null;
    const all = Array.from(
      document.querySelectorAll(`[data-nav-group="${CSS.escape(group)}"]`)
    ) as HTMLElement[];
    const elements = all.filter(
      (e) => e.getAttribute("data-placeholder") !== "1"
    );
    const index = elements.findIndex((e) => e === el);
    if (index < 0) return null;
    return { group, elements, index };
  }, [currAssetId]);

  const groupRows = React.useCallback((elements: HTMLElement[]) => {
    const rows: HTMLElement[][] = [];
    let currentTop: number | null = null;
    let currentRow: HTMLElement[] = [];
    for (const el of elements) {
      const rect = el.getBoundingClientRect();
      const top = Math.round(rect.top);
      if (currentTop === null) {
        currentTop = top;
        currentRow = [el];
      } else if (Math.abs(top - currentTop) <= 8) {
        currentRow.push(el);
      } else {
        rows.push(currentRow);
        currentTop = top;
        currentRow = [el];
      }
    }
    if (currentRow.length > 0) rows.push(currentRow);
    return rows;
  }, []);

  const getAllRows = React.useCallback((): HTMLElement[] => {
    return Array.from(
      document.querySelectorAll("[data-product-row]")
    ) as HTMLElement[];
  }, []);

  const getRowByProductId = React.useCallback(
    (pid: string): HTMLElement | null => {
      const rows = getAllRows();
      return (
        rows.find((el) => el.getAttribute("data-product-row") === pid) || null
      );
    },
    [getAllRows]
  );

  const getThumbsInRow = React.useCallback(
    (rowEl: HTMLElement): HTMLElement[] => {
      const all = Array.from(
        rowEl.querySelectorAll("[data-asset-id]")
      ) as HTMLElement[];
      // Skip placeholders
      return all.filter((el) => el.getAttribute("data-placeholder") !== "1");
    },
    []
  );

  const resolveFromThumbEl = React.useCallback(
    (
      el: HTMLElement
    ): {
      src: string; // image src or video src depending on mediaType
      alt: string;
      assetId: string;
      productId: string;
      mediaType?: "image" | "video";
      poster?: string;
    } | null => {
      const aid = el.getAttribute("data-asset-id") || "";
      const pid =
        (el.closest("[data-product-row]") as HTMLElement | null)?.getAttribute(
          "data-product-row"
        ) ||
        el.getAttribute("data-product-id") ||
        "";
      const img = el.querySelector("img") as HTMLImageElement | null;
      if (!aid || !pid || !img) return null;
      const t = (el.getAttribute("data-asset-type") || "image") as
        | "image"
        | "video";
      const vsrc = el.getAttribute("data-video-url") || undefined;
      if (t === "video") {
        return {
          src: vsrc || img.src,
          poster: img.src,
          alt: img.alt || "",
          assetId: aid,
          productId: pid,
          mediaType: "video",
        };
      }
      return {
        src: img.src,
        alt: img.alt || "",
        assetId: aid,
        productId: pid,
        mediaType: "image",
      };
    },
    []
  );

  const findIndexInRow = React.useCallback(
    (rowEl: HTMLElement, aid: string): number => {
      const thumbs = getThumbsInRow(rowEl);
      return thumbs.findIndex((el) => el.getAttribute("data-asset-id") === aid);
    },
    [getThumbsInRow]
  );

  const canNavigate = React.useCallback(() => {
    const g = getGroupContext();
    if (g) {
      const { elements, index } = g;
      const rows = groupRows(elements);
      // locate row/col
      let rowIdx = 0;
      let colIdx = 0;
      let acc = 0;
      for (let i = 0; i < rows.length; i++) {
        if (index < acc + rows[i].length) {
          rowIdx = i;
          colIdx = index - acc;
          break;
        }
        acc += rows[i].length;
      }
      return {
        left: index > 0,
        right: index < elements.length - 1,
        up: rowIdx > 0 && rows[rowIdx - 1].length > 0,
        down: rowIdx < rows.length - 1 && rows[rowIdx + 1].length > 0,
      };
    }
    // Fallback: navigate within product grid rows
    const row = getRowByProductId(currProductId);
    const rows = getAllRows();
    if (!row) return { left: false, right: false, up: false, down: false };
    const thumbs = getThumbsInRow(row);
    const idx = findIndexInRow(row, currAssetId);
    const rowIdx = rows.findIndex((r) => r === row);
    return {
      left: idx > 0,
      right: idx >= 0 && idx < thumbs.length - 1,
      up:
        rowIdx > 0 &&
        ((): boolean => {
          const prevThumbs = getThumbsInRow(rows[rowIdx - 1]);
          return prevThumbs.length > 0;
        })(),
      down:
        rowIdx >= 0 &&
        rowIdx < rows.length - 1 &&
        ((): boolean => {
          const nextThumbs = getThumbsInRow(rows[rowIdx + 1]);
          return nextThumbs.length > 0;
        })(),
    };
  }, [
    currProductId,
    currAssetId,
    getGroupContext,
    groupRows,
    getRowByProductId,
    getAllRows,
    getThumbsInRow,
    findIndexInRow,
  ]);

  const navigateTo = React.useCallback(
    (target: {
      src: string; // image or video src
      alt: string;
      assetId: string;
      productId: string;
      mediaType?: "image" | "video";
      poster?: string;
    }) => {
      setCurrSrc(target.src);
      setCurrAlt(target.alt);
      setCurrAssetId(target.assetId);
      setCurrProductId(target.productId);
      setCurrType(target.mediaType || "image");
      setCurrPoster(target.poster);
      // Reset view for images only
      setZoom(1);
      setOffset({ x: 0, y: 0 });
    },
    []
  );

  const navLeft = React.useCallback(() => {
    const g = getGroupContext();
    if (g) {
      const { elements, index } = g;
      const nextIdx = index - 1;
      if (index <= 0 || nextIdx < 0) return false;
      const el = elements[nextIdx];
      const info = resolveFromThumbEl(el);
      if (!info) return false;
      navigateTo(info);
      return true;
    }
    const row = getRowByProductId(currProductId);
    if (!row) return false;
    const thumbs = getThumbsInRow(row);
    const idx = findIndexInRow(row, currAssetId);
    const nextIdx = idx - 1;
    if (idx <= 0 || nextIdx < 0) return false;
    const el = thumbs[nextIdx];
    const info = resolveFromThumbEl(el);
    if (!info) return false;
    navigateTo(info);
    return true;
  }, [
    currProductId,
    currAssetId,
    getGroupContext,
    getRowByProductId,
    getThumbsInRow,
    findIndexInRow,
    resolveFromThumbEl,
    navigateTo,
  ]);

  const navRight = React.useCallback(() => {
    const g = getGroupContext();
    if (g) {
      const { elements, index } = g;
      const nextIdx = index + 1;
      if (index < 0 || nextIdx >= elements.length) return false;
      const el = elements[nextIdx];
      const info = resolveFromThumbEl(el);
      if (!info) return false;
      navigateTo(info);
      return true;
    }
    const row = getRowByProductId(currProductId);
    if (!row) return false;
    const thumbs = getThumbsInRow(row);
    const idx = findIndexInRow(row, currAssetId);
    const nextIdx = idx + 1;
    if (idx < 0 || nextIdx >= thumbs.length) return false;
    const el = thumbs[nextIdx];
    const info = resolveFromThumbEl(el);
    if (!info) return false;
    navigateTo(info);
    return true;
  }, [
    currProductId,
    currAssetId,
    getGroupContext,
    getRowByProductId,
    getThumbsInRow,
    findIndexInRow,
    resolveFromThumbEl,
    navigateTo,
  ]);

  const navUpDown = React.useCallback(
    (dir: -1 | 1) => {
      const g = getGroupContext();
      if (g) {
        const { elements, index } = g;
        const rows = groupRows(elements);
        // find row/col of current index
        let rowIdx = 0;
        let colIdx = 0;
        let acc = 0;
        for (let i = 0; i < rows.length; i++) {
          if (index < acc + rows[i].length) {
            rowIdx = i;
            colIdx = index - acc;
            break;
          }
          acc += rows[i].length;
        }
        const targetRowIdx = rowIdx + dir;
        if (targetRowIdx < 0 || targetRowIdx >= rows.length) return false;
        const targetRow = rows[targetRowIdx];
        if (targetRow.length === 0) return false;
        const targetIdx = Math.max(0, Math.min(colIdx, targetRow.length - 1));
        const el = targetRow[targetIdx];
        const info = resolveFromThumbEl(el);
        if (!info) return false;
        navigateTo(info);
        return true;
      }
      const rows = getAllRows();
      const row = getRowByProductId(currProductId);
      if (!row) return false;
      const currRowIdx = rows.findIndex((r) => r === row);
      const targetRowIdx = currRowIdx + dir;
      if (targetRowIdx < 0 || targetRowIdx >= rows.length) return false;
      const currThumbs = getThumbsInRow(row);
      const currIdx = findIndexInRow(row, currAssetId);
      const targetRow = rows[targetRowIdx];
      const targetThumbs = getThumbsInRow(targetRow);
      if (targetThumbs.length === 0) return false;
      const targetIdx = Math.max(
        0,
        Math.min(currIdx >= 0 ? currIdx : 0, targetThumbs.length - 1)
      );
      const el = targetThumbs[targetIdx];
      const info = resolveFromThumbEl(el);
      if (!info) return false;
      navigateTo(info);
      return true;
    },
    [
      currProductId,
      currAssetId,
      getGroupContext,
      groupRows,
      getAllRows,
      getRowByProductId,
      getThumbsInRow,
      findIndexInRow,
      resolveFromThumbEl,
      navigateTo,
    ]
  );

  const navUp = React.useCallback(() => navUpDown(-1), [navUpDown]);
  const navDown = React.useCallback(() => navUpDown(1), [navUpDown]);

  React.useEffect(() => {
    const onKey = (ev: KeyboardEvent) => {
      if (ev.key === "Escape") onClose();
      if (ev.key === "ArrowLeft") {
        ev.preventDefault();
        navLeft();
      }
      if (ev.key === "ArrowRight") {
        ev.preventDefault();
        navRight();
      }
      if (ev.key === "ArrowUp") {
        ev.preventDefault();
        navUp();
      }
      if (ev.key === "ArrowDown") {
        ev.preventDefault();
        navDown();
      }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [onClose, navLeft, navRight, navUp, navDown]);

  // Ensure the modal has a sensible size immediately on mount (before media metadata loads)
  React.useEffect(() => {
    recalcBaseSize();
  }, []);

  // Recompute base size on window resize so modal fits viewport
  React.useEffect(() => {
    const handleResize = () => recalcBaseSize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [recalcBaseSize]);

  // Lock background scroll while modal is open
  React.useEffect(() => {
    const prev = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    return () => {
      document.body.style.overflow = prev;
    };
  }, []);

  const onImageLoad = () => {
    if (imgRef.current) {
      naturalSize.current = {
        w: imgRef.current.naturalWidth || imgRef.current.clientWidth,
        h: imgRef.current.naturalHeight || imgRef.current.clientHeight,
      };
      recalcBaseSize();
    }
  };
  const handleVideoMeta = () => {
    if (videoRef.current) {
      const v = videoRef.current;
      naturalSize.current = {
        w: v.videoWidth || naturalSize.current.w || 1280,
        h: v.videoHeight || naturalSize.current.h || 720,
      };
      recalcBaseSize();
    }
  };

  // Re-sync local state if the opening props change (defensive)
  React.useEffect(() => {
    setCurrSrc(src);
    setCurrAlt(alt);
    setCurrAssetId(assetId);

    setCurrProductId(productId);
    setCurrType(mediaType);
    setCurrPoster(poster);
    setZoom(1);
    setOffset({ x: 0, y: 0 });
  }, [src, alt, assetId, productId, mediaType, poster]);

  return (
    <div
      role="dialog"
      aria-modal="true"
      className="fixed inset-0 z-[60] flex items-center justify-center p-4 bg-black/60"
      onClick={(e) => {
        // Close only when clicking the backdrop, not inner content
        if (e.target === e.currentTarget) onClose();
      }}
      onMouseDown={(e) => {
        // Support platforms that don't reliably emit click on backdrop
        (e.currentTarget as any).__downOnBackdrop =
          e.target === e.currentTarget;
      }}
      onMouseUp={(e) => {
        const down = (e.currentTarget as any).__downOnBackdrop;
        if (down && e.target === e.currentTarget) onClose();
        (e.currentTarget as any).__downOnBackdrop = false;
      }}
    >
      <div
        className={`relative overflow-hidden rounded-lg bg-white/95 dark:bg-gray-900/95 shadow-2xl border border-gray-200 dark:border-gray-700`}
        style={{
          touchAction: "none",
          width: baseSize.w ? `${baseSize.w}px` : "92vw",
          height: baseSize.h ? `${baseSize.h}px` : "90vh",
        }}
        onWheel={onWheel}
        onClick={(e) => e.stopPropagation()}
        ref={containerRef}
      >
        {currType === "video" ? (
          <video
            ref={videoRef}
            src={currSrc}
            poster={currPoster}
            controls
            autoPlay
            loop
            playsInline
            muted
            preload="metadata"
            onLoadedMetadata={handleVideoMeta}
            className="absolute left-1/2 top-1/2 z-0 max-h-[90vh] max-w-[92vw] -translate-x-1/2 -translate-y-1/2 rounded-md"
            onClick={(e) => e.stopPropagation()}
          />
        ) : (
          <img
            ref={imgRef}
            src={currSrc}
            alt={currAlt}
            draggable={false}
            onLoad={onImageLoad}
            style={{
              transform: `translate(-50%, -50%) translate(${offset.x}px, ${offset.y}px) scale(${zoom})`,
              transformOrigin: `50% 50%`,
            }}
            onClick={(e) => {
              e.stopPropagation();
              handleToggleZoom(e);
            }}
            onPointerDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onPointerDown(e);
            }}
            onPointerMove={onPointerMove}
            onPointerUp={onPointerUp}
            onPointerCancel={onPointerUp}
            className={`absolute left-1/2 top-1/2 z-0 max-h-[90vh] max-w-[92vw] object-contain will-change-transform select-none ${imgCursorClass}`}
          />
        )}

        {/* Navigation arrows */}
        {(() => {
          const caps = canNavigate();
          return (
            <>
              <button
                type="button"
                aria-label="Previous image"
                onClick={(e) => {
                  e.stopPropagation();
                  navLeft();
                }}
                disabled={!caps.left}
                className="absolute z-30 left-2 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow flex items-center justify-center hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </button>

              <button
                type="button"
                aria-label="Next image"
                onClick={(e) => {
                  e.stopPropagation();
                  navRight();
                }}
                disabled={!caps.right}
                className="absolute z-30 right-2 top-1/2 -translate-y-1/2 h-10 w-10 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow flex items-center justify-center hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
              >
                <ArrowRightIcon className="h-5 w-5" />
              </button>

              <button
                type="button"
                aria-label="Image above"
                onClick={(e) => {
                  e.stopPropagation();
                  navUp();
                }}
                disabled={!caps.up}
                className="absolute z-30 top-2 left-1/2 -translate-x-1/2 h-10 w-10 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow flex items-center justify-center hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
              >
                <ArrowUpIcon className="h-5 w-5" />
              </button>

              <button
                type="button"
                aria-label="Image below"
                onClick={(e) => {
                  e.stopPropagation();
                  navDown();
                }}
                disabled={!caps.down}
                className="absolute z-30 bottom-2 left-1/2 -translate-x-1/2 h-10 w-10 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow flex items-center justify-center hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
              >
                <ArrowDownIcon className="h-5 w-5" />
              </button>
            </>
          );
        })()}

        {/* Controls overlay (images only) */}
        {currType !== "video" && (
          <>
            <div className="absolute z-20 bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2 bg-black/60 text-white rounded-full px-2 py-1 backdrop-blur-sm">
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  const next = Math.max(
                    1,
                    Math.min(4, parseFloat((zoom - 0.25).toFixed(2)))
                  );
                  if (next !== zoom) applyZoomAtCenter(next);
                }}
                className="px-2 py-1 text-sm leading-none hover:bg-white/10 rounded"
                aria-label="Zoom out"
              >
                −
              </button>
              <span className="px-2 text-xs tabular-nums min-w-[3.5rem] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  const next = Math.max(
                    1,
                    Math.min(4, parseFloat((zoom + 0.25).toFixed(2)))
                  );
                  if (next !== zoom) applyZoomAtCenter(next);
                }}
                className="px-2 py-1 text-sm leading-none hover:bg-white/10 rounded"
                aria-label="Zoom in"
              >
                +
              </button>
              <div className="w-px h-5 bg-white/20 mx-1" />
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  setZoom(1);
                  setOffset({ x: 0, y: 0 });
                }}
                className="px-2 py-1 text-xs leading-none hover:bg-white/10 rounded"
              >
                Fit
              </button>
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  // Choose a comfortable actual-size cap and center anchor
                  const target = Math.min(
                    4,
                    Math.max(
                      1,
                      naturalSize.current.w && baseSize.w
                        ? naturalSize.current.w / baseSize.w
                        : 2
                    )
                  );
                  applyZoomAtCenter(target);
                }}
                className="px-2 py-1 text-xs leading-none hover:bg-white/10 rounded"
              >
                1:1
              </button>
            </div>

            {/* Hint */}
            <div className="absolute z-10 bottom-16 left-1/2 -translate-x-1/2 text-[11px] text-white/80 bg-black/40 rounded px-2 py-0.5 select-none">
              Click to zoom • Drag to pan • Scroll to zoom • Esc to close
            </div>
          </>
        )}

        <button
          type="button"
          onClick={(e) => {
            e.stopPropagation();
            onClose();
          }}
          aria-label="Close preview"
          className="absolute z-30 top-2 right-2 h-8 w-8 rounded-full bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <XIcon className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};
