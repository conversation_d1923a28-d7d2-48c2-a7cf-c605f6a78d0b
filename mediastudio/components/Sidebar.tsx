import React, { useState, useEffect, useRef } from 'react';
import type { GenerationMode, ImageSettings, VideoSettings } from '../types';
import { MODELS } from '../constants';
import { ChevronDownIcon, ImageIcon, VideoIcon } from './icons';

interface SidebarProps {
  generationMode: GenerationMode;
  onModeChange: (mode: GenerationMode) => void;
  settings: ImageSettings | VideoSettings;
  setSettings: React.Dispatch<React.SetStateAction<ImageSettings | VideoSettings>>;
  selectedModelId: string;
  onModelChange: (modelId: string) => void;
}

const ModeToggle: React.FC<{ mode: GenerationMode, onChange: (mode: GenerationMode) => void }> = ({ mode, onChange }) => (
  <div className="flex flex-col p-1 bg-gray-100 dark:bg-gray-800 rounded-lg space-y-1 h-16">
    <button onClick={() => onChange('image')} className={`flex-1 w-full px-3 text-sm font-semibold rounded-md flex items-center justify-center gap-2 transition-colors ${mode === 'image' ? 'bg-white dark:bg-gray-700 shadow-sm text-primary' : 'text-gray-500 hover:text-gray-800 dark:hover:text-gray-200'}`}>
      <ImageIcon className="h-5 w-5" /> Image
    </button>
    <button onClick={() => onChange('video')} className={`flex-1 w-full px-3 text-sm font-semibold rounded-md flex items-center justify-center gap-2 transition-colors ${mode === 'video' ? 'bg-white dark:bg-gray-700 shadow-sm text-primary' : 'text-gray-500 hover:text-gray-800 dark:hover:text-gray-200'}`}>
      <VideoIcon className="h-5 w-5" /> Video
    </button>
  </div>
);

const CustomSelect: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  className?: string;
}> = ({ label, value, onChange, options, className }) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const selectedOption = options.find(opt => opt.value === value);

  return (
    <div className={`relative h-16 ${className}`} ref={selectRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex h-full w-full items-center justify-between rounded-lg bg-gray-100 px-3 text-left hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors"
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label={`${label}, Current value: ${selectedOption ? selectedOption.label : 'Select...'}`}
      >
        <div>
            <div className="text-xs text-gray-500 dark:text-gray-400">{label}</div>
            <div className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate">{selectedOption ? selectedOption.label : 'Select...'}</div>
        </div>
        <ChevronDownIcon className={`h-5 w-5 text-gray-400 dark:text-gray-500 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className="absolute bottom-full z-20 mb-1 w-full rounded-md border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-gray-800">
          <ul className="max-h-60 overflow-y-auto py-1" role="listbox">
            {options.map(option => (
              <li
                key={option.value}
                onClick={() => {
                  onChange(option.value);
                  setIsOpen(false);
                }}
                className={`cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 ${
                  value === option.value ? 'font-semibold text-primary' : 'text-gray-800 dark:text-gray-200'
                }`}
                role="option"
                aria-selected={value === option.value}
              >
                {option.label}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};


export const Sidebar: React.FC<SidebarProps> = ({ 
  generationMode, 
  onModeChange, 
  settings,
  setSettings,
  selectedModelId,
  onModelChange
}) => {

  const handleSettingsChange = (field: keyof (ImageSettings | VideoSettings), value: string | number) => {
    setSettings(prev => ({...prev, [field]: value}));
  };
  
  const quality = 'quality' in settings ? settings.quality : 'Standard';

  const modelOptions = MODELS
    .filter(m => generationMode === 'video' ? m.id.includes('veo') : !m.id.includes('veo'))
    .map(model => ({ value: model.id, label: model.name }));

  const aspectRatioOptions = (generationMode === 'image' 
    ? ['1:1', '16:9', '9:16', '4:3', '3:4'] 
    : ['16:9', '9:16']
  ).map(ratio => ({ value: ratio, label: ratio }));

  const qualityOptions = [
      { value: 'Standard', label: 'Standard' },
      { value: 'High', label: 'High' }
  ];

  return (
    <div className="p-4 flex items-center justify-center w-full gap-6">
        <div className="flex-shrink-0">
          <ModeToggle mode={generationMode} onChange={onModeChange} />
        </div>

        <div className="flex items-center gap-3">
            <CustomSelect
                label="Aspect Ratio"
                value={settings.aspectRatio}
                onChange={(value) => handleSettingsChange('aspectRatio', value)}
                options={aspectRatioOptions}
                className="w-36"
            />
            <CustomSelect
                label="Quality"
                value={quality}
                onChange={(value) => handleSettingsChange('quality', value)}
                options={qualityOptions}
                className="w-36"
            />
            <CustomSelect
                label="AI Model"
                value={selectedModelId}
                onChange={onModelChange}
                options={modelOptions}
                className="w-36"
            />
        </div>
    </div>
  );
};
