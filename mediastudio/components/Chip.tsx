
import React from 'react';

interface ChipProps {
  children: React.ReactNode;
  color: string;
}

export const Chip: React.FC<ChipProps> = ({ children, color }) => {
  // Map API color classes to actual background colors
  const colorMap: Record<string, string> = {
    "bg-purple-600": "bg-purple-500",
    "bg-blue-600": "bg-blue-500",
    "bg-pink-600": "bg-pink-500",
    "bg-emerald-600": "bg-emerald-500",
    "bg-gray-500": "bg-gray-500",
  };

  const displayColor = colorMap[color] || color || "bg-gray-500";

  return (
    <div className="inline-flex items-center space-x-1.5 text-xs font-medium px-2 py-0.5 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-200">
      <span className={`h-2 w-2 rounded-full ${displayColor} border border-gray-300 dark:border-gray-600`} />
      <span>{children}</span>
    </div>
  );
};
