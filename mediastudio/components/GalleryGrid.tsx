import React from "react";
import type { Asset } from "../types";
import { Thumbnail } from "./Thumbnail";

interface GalleryGridProps {
  assets: Asset[];
}

export const GalleryGrid: React.FC<GalleryGridProps> = ({ assets }) => {
  const onSelect = React.useCallback(() => {}, []);
  if (!assets || assets.length === 0) {
    return (
      <div className="h-full min-h-[200px] flex items-center justify-center text-gray-500 dark:text-gray-400 text-sm">
        No generated images yet.
      </div>
    );
  }
  return (
    <div className="grid grid-cols-4 gap-3">
      {assets.map((asset) => (
        <div key={asset.id} className="w-full">
          <div className="w-full aspect-square">
            <Thumbnail
              asset={asset}
              isSelected={false}
              onSelect={onSelect}
              isGenerated
              navGroup="gallery"
            />
          </div>
        </div>
      ))}
    </div>
  );
};

export default GalleryGrid;
