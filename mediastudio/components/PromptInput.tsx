import React from "react";
import type { AttachedImage, PromptWithImages } from "../types";
import { XIcon } from "./icons";
import {
  createEmptyPrompt,
  createPromptFromText,
  insertMentionAtPosition,
  removeMention,
  updatePromptText,
} from "../utils/promptUtils";

interface PromptInputProps {
  productId: string;
  productTitle: string;
  value: PromptWithImages;
  onChange: (productId: string, value: PromptWithImages) => void;
  className?: string;
}

export const PromptInput: React.FC<PromptInputProps> = ({
  productId,
  productTitle,
  value,
  onChange,
  className = "",
}) => {
  const [isDragOver, setIsDragOver] = React.useState(false);
  const editorRef = React.useRef<HTMLDivElement>(null);
  const [cursorPosition, setCursorPosition] = React.useState(0);

  // Ensure we have a valid prompt structure
  const prompt = React.useMemo(() => {
    if (!value || !value.segments) {
      return createPromptFromText(typeof value === "string" ? value : "");
    }
    return value;
  }, [value]);

  // Restore cursor position after re-render
  React.useEffect(() => {
    if (!editorRef.current || cursorPosition === 0) return;

    const selection = window.getSelection();
    if (!selection) return;

    let charCount = 0;
    const walker = document.createTreeWalker(
      editorRef.current,
      NodeFilter.SHOW_TEXT,
      null
    );

    let node;
    while ((node = walker.nextNode())) {
      const nodeLength = node.textContent?.length || 0;
      if (charCount + nodeLength >= cursorPosition) {
        const range = document.createRange();
        const offset = Math.min(cursorPosition - charCount, nodeLength);
        range.setStart(node, offset);
        range.collapse(true);
        selection.removeAllRanges();
        selection.addRange(range);
        return;
      }
      charCount += nodeLength;
    }
  }, [prompt, cursorPosition]);

  const handleTextChange = React.useCallback(
    (e: React.FormEvent<HTMLDivElement>) => {
      if (!editorRef.current) return;

      const newText = editorRef.current.textContent || "";

      // Save cursor position before updating
      const selection = window.getSelection();
      let cursorPos = 0;
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0);
        const preCaretRange = range.cloneRange();
        preCaretRange.selectNodeContents(editorRef.current);
        preCaretRange.setEnd(range.endContainer, range.endOffset);
        cursorPos = preCaretRange.toString().length;
      }

      setCursorPosition(cursorPos);

      // Create a simple text-only prompt to avoid re-rendering mentions during typing
      const updatedPrompt = createPromptFromText(newText);
      onChange(productId, updatedPrompt);
    },
    [productId, onChange]
  );

  const handleDragOver = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
    setIsDragOver(true);
  }, []);

  const handleDragLeave = React.useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = React.useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);

      try {
        const jsonData = e.dataTransfer.getData("application/json");
        if (!jsonData) return;

        const dragData = JSON.parse(jsonData);
        if (dragData.type !== "image") return;

        const newImage: AttachedImage = {
          assetId: dragData.assetId,
          filename: dragData.filename,
          displayName: dragData.displayName, // Use display name if available
          url: dragData.url,
          productId: dragData.productId,
        };

        // Check if image is already attached
        const isAlreadyAttached = prompt
          .getAttachedImages()
          .some((img) => img.assetId === newImage.assetId);

        if (!isAlreadyAttached) {
          // Get cursor position from the drop event
          const selection = window.getSelection();
          let insertPosition = prompt.getText().length; // Default to end

          if (selection && selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);
            insertPosition = range.startOffset;
          }

          const updatedPrompt = insertMentionAtPosition(
            prompt,
            insertPosition,
            newImage
          );
          onChange(productId, updatedPrompt);
        }
      } catch (error) {
        console.error("Failed to handle dropped image:", error);
      }
    },
    [productId, prompt, onChange]
  );

  const removeAttachedImage = React.useCallback(
    (assetId: string) => {
      const updatedPrompt = removeMention(prompt, assetId);
      onChange(productId, updatedPrompt);
    },
    [productId, prompt, onChange]
  );

  // Render the prompt segments as JSX
  const renderPromptContent = React.useCallback(() => {
    return prompt.segments.map((segment, index) => {
      if (segment.type === "text") {
        return segment.content;
      } else {
        // Render @mention as a styled span
        const displayText =
          segment.image?.displayName || segment.content || "Unknown";
        const assetId = segment.image?.assetId || `unknown-${index}`;

        return (
          <span
            key={`mention-${assetId}-${index}`}
            className="inline-flex items-center gap-1 px-1.5 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 rounded text-sm font-medium mx-0.5"
            contentEditable={false}
          >
            @{displayText}
            <button
              type="button"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                if (segment.image?.assetId) {
                  removeAttachedImage(segment.image.assetId);
                }
              }}
              className="ml-0.5 text-blue-600 hover:text-blue-800 dark:text-blue-300 dark:hover:text-blue-100 cursor-pointer"
              aria-label={`Remove ${displayText}`}
            >
              <XIcon className="h-3 w-3" />
            </button>
          </span>
        );
      }
    });
  }, [prompt.segments, removeAttachedImage]);

  return (
    <div className="relative">
      {/* Rich Text Editor */}
      <div
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        className={`w-full min-h-32 p-2 text-sm text-gray-900 dark:text-gray-100 bg-gray-100 dark:bg-gray-700/50 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-focus focus:bg-white dark:focus:bg-gray-700 placeholder-gray-400 dark:placeholder-gray-500 transition-colors ${
          isDragOver
            ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-900/20"
            : ""
        } ${className}`}
        onInput={handleTextChange}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        style={{ whiteSpace: "pre-wrap" }}
      >
        {prompt.segments.length === 1 &&
        prompt.segments[0].type === "text" &&
        !prompt.segments[0].content ? (
          <span className="text-gray-400 dark:text-gray-500 pointer-events-none">
            e.g., A model wearing the {productTitle.toLowerCase()} in a sunlit
            park... (Drag images here to attach)
          </span>
        ) : (
          renderPromptContent()
        )}
      </div>

      {/* Drop indicator */}
      {isDragOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-500/10 border-2 border-dashed border-blue-500 rounded-md pointer-events-none">
          <div className="px-3 py-2 bg-blue-500 text-white text-sm font-medium rounded-md">
            Drop image to attach inline
          </div>
        </div>
      )}
    </div>
  );
};
