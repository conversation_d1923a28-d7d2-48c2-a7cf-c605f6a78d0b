import React from "react";
import { PromptInput } from "./PromptInput";
import { PromptInputReliable } from "./PromptInputReliable";
import type { Asset, Product, PromptWithImages } from "../types";

interface PromptInputComparisonProps {
  productId: string;
  productTitle: string;
  assets: Asset[];
  products: Product[];
}

export const PromptInputComparison: React.FC<PromptInputComparisonProps> = ({
  productId,
  productTitle,
  assets,
  products,
}) => {
  const [oldValue, setOldValue] = React.useState<PromptWithImages>({
    segments: [{ type: "text", content: "" }],
    getText: () => "",
    getAttachedImages: () => [],
  });
  
  const [newValue, setNewValue] = React.useState<string>("");

  const handleOldChange = React.useCallback((productId: string, value: PromptWithImages) => {
    setOldValue(value);
  }, []);

  const handleNewChange = React.useCallback((productId: string, value: string) => {
    setNewValue(value);
  }, []);

  return (
    <div className="space-y-8 p-6 bg-white dark:bg-gray-900 rounded-lg">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          🔧 Current Implementation (ContentEditable)
        </h3>
        <div className="space-y-2">
          <PromptInput
            productId={productId}
            productTitle={productTitle}
            value={oldValue}
            onChange={handleOldChange}
            className="border-2 border-red-200 dark:border-red-800"
          />
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Issues: Cursor jumping, text doubling, mentions disappearing
          </div>
          <div className="text-xs font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded">
            Text: "{oldValue.getText()}"
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          ✅ New Implementation (react-mentions)
        </h3>
        <div className="space-y-2">
          <PromptInputReliable
            productId={productId}
            productTitle={productTitle}
            value={newValue}
            onChange={handleNewChange}
            assets={assets}
            products={products}
            className="border-2 border-green-200 dark:border-green-800"
          />
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Benefits: Stable cursor, no text doubling, reliable mentions
          </div>
          <div className="text-xs font-mono bg-gray-100 dark:bg-gray-800 p-2 rounded">
            Value: "{newValue}"
          </div>
        </div>
      </div>

      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
          🧪 Test Instructions:
        </h4>
        <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
          <li>• Try typing in both fields - notice cursor behavior</li>
          <li>• Drag images from gallery to both fields</li>
          <li>• Type "@" to trigger mention suggestions in the new version</li>
          <li>• Try deleting mentions in both versions</li>
          <li>• Edit text around mentions</li>
        </ul>
      </div>
    </div>
  );
};
