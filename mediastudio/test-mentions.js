// Quick validation script for the new @mentions implementation
// Run this in the browser console to test core functionality

console.log('🧪 Testing New @Mentions Implementation...');

// Test 1: Check if react-mentions is loaded
function testReactMentionsLoaded() {
  const mentionsInputs = document.querySelectorAll('[data-mentions-input]');
  const hasReactMentions = document.querySelector('.mentions') !== null;
  
  console.log('✅ Test 1: React-mentions loaded:', hasReactMentions);
  return hasReactMentions;
}

// Test 2: Check if prompt inputs are using new implementation
function testNewImplementation() {
  const promptInputs = document.querySelectorAll('textarea');
  const hasOldContentEditable = document.querySelectorAll('[contenteditable="true"]').length > 0;
  
  console.log('✅ Test 2: Using textarea (not contentEditable):', promptInputs.length > 0);
  console.log('⚠️  Old contentEditable elements found:', hasOldContentEditable);
  
  return promptInputs.length > 0 && !hasOldContentEditable;
}

// Test 3: Check if mentions styling is applied
function testMentionsStyling() {
  const mentionElements = document.querySelectorAll('.mentions__mention');
  const hasProperStyling = mentionElements.length === 0 || 
    Array.from(mentionElements).some(el => 
      getComputedStyle(el).backgroundColor !== 'rgba(0, 0, 0, 0)'
    );
  
  console.log('✅ Test 3: Mentions styling applied:', hasProperStyling);
  return hasProperStyling;
}

// Test 4: Simulate typing to check cursor behavior
function testCursorBehavior() {
  const textarea = document.querySelector('textarea');
  if (!textarea) {
    console.log('❌ Test 4: No textarea found');
    return false;
  }
  
  // Focus and add some text
  textarea.focus();
  textarea.value = 'Test text';
  textarea.setSelectionRange(5, 5); // Position cursor in middle
  
  // Simulate typing
  const event = new InputEvent('input', { data: 'X' });
  textarea.dispatchEvent(event);
  
  const cursorPosition = textarea.selectionStart;
  console.log('✅ Test 4: Cursor position after typing:', cursorPosition);
  
  // Clean up
  textarea.value = '';
  
  return cursorPosition > 0;
}

// Test 5: Check if drag and drop handlers are attached
function testDragDropHandlers() {
  const promptContainers = document.querySelectorAll('[data-product-row]');
  const hasDragHandlers = Array.from(promptContainers).some(container => {
    const promptArea = container.querySelector('textarea')?.parentElement;
    return promptArea && (
      promptArea.ondragover !== null || 
      promptArea.ondrop !== null ||
      promptArea.getAttribute('ondragover') !== null
    );
  });
  
  console.log('✅ Test 5: Drag & drop handlers attached:', hasDragHandlers);
  return hasDragHandlers;
}

// Test 6: Check console for errors
function testConsoleErrors() {
  const originalError = console.error;
  let errorCount = 0;
  
  console.error = function(...args) {
    errorCount++;
    originalError.apply(console, args);
  };
  
  // Restore after a short delay
  setTimeout(() => {
    console.error = originalError;
    console.log('✅ Test 6: Console errors during test:', errorCount);
  }, 1000);
  
  return errorCount === 0;
}

// Run all tests
function runAllTests() {
  console.log('\n🚀 Running Comprehensive Tests...\n');
  
  const results = {
    reactMentionsLoaded: testReactMentionsLoaded(),
    newImplementation: testNewImplementation(),
    mentionsStyling: testMentionsStyling(),
    cursorBehavior: testCursorBehavior(),
    dragDropHandlers: testDragDropHandlers(),
  };
  
  testConsoleErrors();
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASS' : 'FAIL'}`);
  });
  
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 ALL TESTS PASSED! New implementation is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the implementation.');
  }
  
  return results;
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    setTimeout(runAllTests, 1000); // Give React time to render
  }
}

// Export for manual testing
window.testMentions = runAllTests;
