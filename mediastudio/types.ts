export interface Variant {
  id: string;
  name: string;
  color: string;
}

export interface Asset {
  id: string;
  productId: string;
  // For thumbnails, this is what the grid displays; for videos this should typically be a preview image
  url: string;
  type: "image" | "video";
  variantId?: string;
  filename: string;
  // User-friendly display name for @mentions (e.g., "1155 High Waist Leg..")
  displayName?: string;
  // Optional richer media fields (used for videos)
  fileUrl?: string; // original file URI (e.g., .mp4)
  previewUrl?: string; // poster/preview image URI
  // Generation prompt (only present for generated assets)
  prompt?: string;
}

export interface Product {
  id: string;
  title: string;
  variants: Variant[];
  // Collections this product belongs to, used for tags in the table UI
  collections?: { id: string; name: string; color: string }[];
  assets: Asset[];
}

export type GenerationMode = "image" | "video";

export interface ImageSettings {
  size: string;
  guidance: number;
  steps: number;
  strength: number;
  seed: number;
  upscale: boolean;
  safety: boolean;
  aspectRatio: string;
  quality: "Standard" | "High";
}

export interface VideoSettings {
  duration: number;
  fps: number;
  resolution: string;
  aspectRatio: string;
  motionStrength: number;
  seed: number;
  audio: boolean;
  quality: "Standard" | "High";
}

export interface StylePreset {
  id: string;
  name: string;
  thumbnailUrl: string;
}

export interface Model {
  id: string;
  name: string;
  health: "good" | "degraded" | "down";
  eta: string;
  cost: number;
}

export type MainTab = "canvas" | "models" | "props" | "scenes" | "brandbook";

export interface AttachedImage {
  assetId: string;
  filename: string;
  // User-friendly display name for @mentions (e.g., "1155 High Waist Leg..")
  displayName?: string;
  url: string;
  productId: string;
}

export interface PromptSegment {
  type: "text" | "mention";
  content: string; // For text: the actual text, For mention: the filename
  image?: AttachedImage; // Only present for mention segments
}

export interface PromptWithImages {
  segments: PromptSegment[];
  // Helper method to get plain text representation
  getText(): string;
  // Helper method to get all attached images
  getAttachedImages(): AttachedImage[];
}
