import React, { useState, useCallback, useEffect } from "react";
import { Header } from "./components/Header";
import { ProductGrid } from "./components/ProductGrid";
import { PreviewPane } from "./components/PreviewPane";
import { useTheme } from "./hooks/useTheme";
import type {
  Product,
  Asset,
  GenerationMode,
  ImageSettings,
  VideoSettings,
  MainTab,
  PromptWithImages,
} from "./types";
import { createPromptFromText } from "./utils/promptUtils";
import { generateDisplayName } from "./utils/assetNaming";
import { PRODUCTS, MODELS } from "./constants";
import { BrandbookPane } from "./components/BrandbookPane";
import { FloatingGenerateBar } from "./components/FloatingGenerateBar";
import { ScenesPane } from "./components/ScenesPane";
import { ArrowLeftIcon, ArrowRightIcon } from "./components/icons";
import { CustomSelect } from "./components/CustomSelect";
import {
  startBatch,
  getBatchStatus,
  getBatchDetails,
} from "./services/geminiService";

const MainTabs: React.FC<{
  activeTab: MainTab;
  onTabChange: (tab: MainTab) => void;
}> = ({ activeTab, onTabChange }) => {
  const tabs: Array<{ id: MainTab; label: string }> = [
    { id: "canvas", label: "Gallery" },
    { id: "models", label: "Models" },
    { id: "props", label: "Props" },
    { id: "scenes", label: "Scenes" },
    { id: "brandbook", label: "Brandbook" },
  ];

  return (
    <div className="flex-shrink-0 flex items-center border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900">
      {tabs.map(({ id, label }) => (
        <button
          key={id}
          onClick={() => onTabChange(id)}
          className={`px-4 py-2.5 text-sm font-medium transition-colors cursor-pointer ${
            activeTab === id
              ? "text-primary border-b-2 border-primary"
              : "text-gray-500 hover:text-gray-800 dark:hover:text-gray-200 border-b-2 border-transparent"
          }`}
        >
          {label}
        </button>
      ))}
    </div>
  );
};

const PlaceholderContent: React.FC<{ title: string }> = ({ title }) => (
  <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
    <h2 className="text-2xl font-semibold">{title}</h2>
  </div>
);

const App: React.FC = () => {
  const [theme, setTheme] = useTheme();
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);
  // Pagination state for product table
  const [pageHistory, setPageHistory] = useState<string[]>([""]); // '' = first page (no cursor)
  const [pageIndex, setPageIndex] = useState(0);
  const [nextCursor, setNextCursor] = useState<string | null>(null);
  // Page size selector with persistence
  const [pageSize, setPageSize] = useState<number>(() => {
    try {
      const raw = localStorage.getItem("pageSize");
      const n = raw ? Number(raw) : 50;
      return [10, 25, 50, 100].includes(n) ? n : 50;
    } catch {
      return 50;
    }
  });
  useEffect(() => {
    try {
      localStorage.setItem("pageSize", String(pageSize));
    } catch {}
  }, [pageSize]);
  const [selectedAssetIds, setSelectedAssetIds] = useState<Set<string>>(
    new Set()
  );
  const [activeAsset, setActiveAsset] = useState<Asset | null>(null);
  const [generationMode, setGenerationMode] = useState<GenerationMode>("image");
  const [activeMainTab, setActiveMainTab] = useState<MainTab>("canvas");
  const [selectedModelId, setSelectedModelId] = useState<string>(
    MODELS.filter((m) => !m.id.includes("veo"))[0].id
  );
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(
    new Set()
  );
  const [lastInteractedAsset, setLastInteractedAsset] = useState<{
    asset: Asset;
    isToggleOff: boolean;
  } | null>(null);

  const [prompts, setPrompts] = useState<Record<string, PromptWithImages>>({});
  const [collectionFilters, setCollectionFilters] = useState<string[]>([]);
  const [selectedShopIdFilter, setSelectedShopIdFilter] = useState<
    string | null
  >(null);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [allProductsForFilters, setAllProductsForFilters] = useState<
    Product[] | null
  >(null);
  // Testing mode for scraped data
  const [testingMode, setTestingMode] = useState<boolean>(false);
  const [testingStats, setTestingStats] = useState<{
    total_products: number;
    domains: Array<{ domain: string; count: number }>;
    message?: string;
  } | null>(null);
  const allDataCacheRef = React.useRef<Map<string, Product[]>>(new Map());
  const [isFetchingAllProducts, setIsFetchingAllProducts] = useState(false);
  const filtersKey = React.useMemo(
    () =>
      [
        `shop=${selectedShopIdFilter || ""}`,
        `collections=${(collectionFilters || []).slice().sort().join(",")}`,
        `testing=${testingMode}`,
      ].join("|"),
    [selectedShopIdFilter, collectionFilters, testingMode]
  );

  // Row-state filters and sorting
  const [filterSelectedOnly, setFilterSelectedOnly] = useState<boolean>(false);
  const [filterHasGenerated, setFilterHasGenerated] = useState<boolean>(false);
  const [sortMode, setSortMode] = useState<
    "default" | "selected_first" | "generated_first"
  >("default");

  // Generation state
  type BatchState = {
    batchId: string;
    total: number;
    completed: number;
    failed: number;
    status: string;
  };
  const [activeBatches, setActiveBatches] = useState<
    Record<string, BatchState>
  >({});
  const [generationBatch, setGenerationBatch] = useState<{
    batchId: string;
    total: number;
    completed: number;
    failed: number;
    status: string;
  } | null>(null);
  const [isInitiating, setIsInitiating] = useState(false);
  const [initiationMessage, setInitiationMessage] = useState<string | null>(
    null
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  // Track placeholders that failed so we can render a red X briefly
  const [failedPlaceholderIds, setFailedPlaceholderIds] = useState<Set<string>>(
    new Set()
  );
  const [lastRequestId, setLastRequestId] = useState<string | null>(null);
  const [hasMergedGenerated, setHasMergedGenerated] = useState<boolean>(false);
  const [generatedImages, setGeneratedImages] = useState<
    Record<string, Asset[]>
  >({});
  // Per-batch, per-product request progress (totals, pending, etc.)
  const [rowProgressByBatch, setRowProgressByBatch] = useState<
    Record<
      string,
      Record<
        string,
        { total: number; completed: number; failed: number; pending: number }
      >
    >
  >({});

  // Debug console (dev-only)
  const forceDebug = (() => {
    try {
      return new URLSearchParams(window.location.search).get("debug") === "1";
    } catch {
      return false;
    }
  })();
  const isDev =
    forceDebug ||
    Boolean(
      (import.meta as any)?.env?.DEV ??
        (import.meta as any)?.env?.MODE === "development"
    ) ||
    /localhost|127\.0\.0\.1/.test(window.location.hostname);
  const [debugOpen, setDebugOpen] = useState(false);
  const [debugEvents, setDebugEvents] = useState<any[]>([]);
  const loadLogs = useCallback(async () => {
    try {
      const res = await fetch(`http://localhost:8080/__debug/logs`);
      if (!res.ok) return;
      const j = await res.json();
      setDebugEvents(Array.isArray(j?.events) ? j.events : []);
    } catch {}
  }, []);
  useEffect(() => {
    let timer: any;
    const tick = async () => {
      if (debugOpen) await loadLogs();
      timer = setTimeout(tick, 2000);
    };
    if (debugOpen) tick();
    return () => timer && clearTimeout(timer);
  }, [debugOpen, loadLogs]);
  const isUsefulEvent = (ev: any): boolean => {
    if (!ev || typeof ev !== "object") return false;
    const kind = ev.kind || "";
    const status = typeof ev.status === "number" ? ev.status : undefined;
    const url = String(ev.url || "");
    if (kind === "error" || kind === "external-error") return true;
    if (kind === "response") {
      if (typeof status === "number" && status >= 400) return true;
      if (/\/generate|\/assets|\/scraper/.test(url)) return true;
      return false;
    }
    if (kind === "external-response") {
      return typeof status === "number" ? status >= 400 : true;
    }
    return false;
  };
  const usefulEvents = React.useMemo(() => {
    const arr = Array.isArray(debugEvents)
      ? debugEvents.filter(isUsefulEvent)
      : [];
    return arr.slice(-80);
  }, [debugEvents]);

  const buildDebugBundle = useCallback(() => {
    try {
      const lines: string[] = [];
      const items = usefulEvents;
      for (const ev of items) {
        const ts = ev?.ts || ev?.time || new Date().toISOString();
        const kind = ev?.kind || ev?.level || "event";
        const src = ev?.source || ev?.src || "app";
        const rid = ev?.rid ? ` rid=${ev.rid}` : "";
        const method = ev?.method ? `${ev.method} ` : "";
        const url = ev?.url || ev?.path || "";
        const status = typeof ev?.status === "number" ? ` -> ${ev.status}` : "";
        const dur =
          typeof ev?.durationMs === "number" ? ` ${ev.durationMs}ms` : "";
        lines.push(`=== ${ts} :: ${src}/${kind}${rid} ===`);
        if (method || url) lines.push(`REQ: ${method}${url}`.trim());
        if (status || dur) lines.push(`RES:${status}${dur}`.trim());
        if (ev?.note) lines.push(`NOTE: ${ev.note}`);
        if (ev?.meta) {
          try {
            lines.push(`META: ${JSON.stringify(ev.meta, null, 2)}`);
          } catch {
            lines.push(`META: [unserializable]`);
          }
        }
        lines.push("");
      }
      return lines.join("\n");
    } catch {
      return JSON.stringify(debugEvents ?? [], null, 2);
    }
  }, [debugEvents]);

  const copyAllDebug = useCallback(async () => {
    const text = buildDebugBundle();
    try {
      await navigator.clipboard.writeText(text);
    } catch {
      try {
        const ta = document.createElement("textarea");
        ta.value = text;
        ta.style.position = "fixed";
        ta.style.opacity = "0";
        document.body.appendChild(ta);
        ta.focus();
        ta.select();
        document.execCommand("copy");
        document.body.removeChild(ta);
      } catch {}
    }
  }, [buildDebugBundle]);

  const clearAllDebug = useCallback(async () => {
    try {
      await fetch("http://localhost:8080/__debug/logs", { method: "DELETE" });
      setDebugEvents([]);
    } catch {}
  }, []);

  // Total product count across all pages (optionally per shop)
  const [productTotalCount, setProductTotalCount] = useState<number | null>(
    null
  );
  // In-memory page cache for instant navigation
  const pageCache = React.useRef<
    Map<string, { products: Product[]; nextCursor: string | null }>
  >(new Map());
  // Collections options fetched from API (distinct across entire dataset per shop)
  const [availableCollectionsApi, setAvailableCollectionsApi] = useState<
    { id: string; name: string; color: string }[] | null
  >(null);

  // Compute available collections across all products
  const allCollections = React.useMemo(() => {
    const map = new Map<string, { id: string; name: string; color: string }>();
    products.forEach((p) => {
      (p.collections || []).forEach((c) => {
        if (!map.has(c.id))
          map.set(c.id, { id: c.id, name: c.name, color: c.color });
      });
    });
    return Array.from(map.values()).sort((a, b) =>
      a.name.localeCompare(b.name)
    );
  }, [products]);

  // Apply filters to products shown in the grid
  const filteredProducts = React.useMemo(() => {
    const isSearching = (searchQuery || "").trim().length > 0;
    // Source list: aggregated all pages when searching, otherwise current page
    let base =
      isSearching && allProductsForFilters ? allProductsForFilters : products;
    // Apply collections filter first
    if (collectionFilters && collectionFilters.length > 0) {
      const set = new Set(collectionFilters);
      base = base.filter((p) =>
        (p.collections || []).some((c) => set.has(c.id))
      );
    }

    // Apply inline search (title minimum; also match id, collections, variants, asset filenames/urls)
    const q = (searchQuery || "").trim().toLowerCase();
    if (!q) return base;

    const matches = (p: Product): boolean => {
      if (p.title?.toLowerCase().includes(q)) return true;
      if (p.id?.toLowerCase().includes(q)) return true;

      if (
        (p.collections || []).some((c) => {
          const n = (c.name || "").toLowerCase();
          const i = (c.id || "").toLowerCase();
          return n.includes(q) || i.includes(q);
        })
      )
        return true;

      if (
        (p.variants || []).some((v) =>
          [v.id, v.name, v.color].some((x) =>
            (x || "").toLowerCase().includes(q)
          )
        )
      )
        return true;

      if (
        (p.assets || []).some((a) =>
          [a.filename, a.url].some((x) => (x || "").toLowerCase().includes(q))
        )
      )
        return true;

      return false;
    };

    // 1) Basic search (already applied by matches)
    let filtered = base.filter(matches);

    // 2) Row-state filters
    if (filterSelectedOnly) {
      filtered = filtered.filter((p) => selectedProductIds.has(p.id));
    }
    if (filterHasGenerated) {
      filtered = filtered.filter(
        (p) => (generatedImages[p.id] || []).length > 0
      );
    }

    // 3) Sorting
    if (sortMode === "selected_first") {
      filtered = filtered.slice().sort((a, b) => {
        const sa = selectedProductIds.has(a.id) ? 1 : 0;
        const sb = selectedProductIds.has(b.id) ? 1 : 0;
        if (sa !== sb) return sb - sa;
        return String(a.title || "").localeCompare(String(b.title || ""));
      });
    } else if (sortMode === "generated_first") {
      filtered = filtered.slice().sort((a, b) => {
        const ga = (generatedImages[a.id] || []).length > 0 ? 1 : 0;
        const gb = (generatedImages[b.id] || []).length > 0 ? 1 : 0;
        if (ga !== gb) return gb - ga;
        return String(a.title || "").localeCompare(String(b.title || ""));
      });
    }

    return filtered;
  }, [
    products,
    allProductsForFilters,
    collectionFilters,
    searchQuery,
    filterSelectedOnly,
    filterHasGenerated,
    sortMode,
    selectedProductIds,
    generatedImages,
  ]);

  // Fetch total product count whenever shop filter changes or testing mode changes
  useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        const base = testingMode
          ? `http://localhost:8080/catalog/scraped-products/count`
          : `http://localhost:8080/catalog/products/count`;
        const params = new URLSearchParams();
        if (!testingMode && selectedShopIdFilter)
          params.set("shop_id", selectedShopIdFilter);
        if (!testingMode && collectionFilters && collectionFilters.length > 0)
          params.set("collections", collectionFilters.join(","));
        const res = await fetch(`${base}?${params.toString()}`);
        if (!res.ok) return;
        const data = await res.json();
        if (!cancelled) {
          setProductTotalCount(
            typeof data?.count === "number" ? data.count : 0
          );
          if (testingMode && data?.meta) {
            setTestingStats(data.meta);
          }
        }
      } catch {
        if (!cancelled) setProductTotalCount(null);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [selectedShopIdFilter, collectionFilters, testingMode]);
  // Fetch full collections list for the selected shop or scraped data
  useEffect(() => {
    let cancelled = false;
    (async () => {
      try {
        const base = testingMode
          ? `http://localhost:8080/catalog/scraped-collections`
          : `http://localhost:8080/catalog/collections`;
        const params = new URLSearchParams();
        if (!testingMode && selectedShopIdFilter)
          params.set("shop_id", selectedShopIdFilter);
        const res = await fetch(`${base}?${params.toString()}`);
        if (!res.ok) return;
        const data = await res.json();
        if (!cancelled && Array.isArray(data?.items)) {
          setAvailableCollectionsApi(data.items);
        }
      } catch {
        if (!cancelled) setAvailableCollectionsApi(null);
      }
    })();
    return () => {
      cancelled = true;
    };
  }, [selectedShopIdFilter, testingMode]);

  // Reset aggregated list when search clears
  useEffect(() => {
    if ((searchQuery || "").trim().length === 0) {
      setAllProductsForFilters(null);
      setIsFetchingAllProducts(false);
    }
  }, [searchQuery]);

  // When searching, fetch all pages for current filters (shop, collections) and aggregate client-side
  useEffect(() => {
    let cancelled = false;
    const loadAll = async () => {
      if ((searchQuery || "").trim().length === 0) return;
      const key = filtersKey;
      const cached = allDataCacheRef.current.get(key);
      if (cached) {
        if (!cancelled) setAllProductsForFilters(cached);
        return;
      }
      setIsFetchingAllProducts(true);
      try {
        let cursor = "";
        const all: Product[] = [];
        for (let i = 0; i < 100; i++) {
          const base = testingMode
            ? `http://localhost:8080/catalog/scraped-products`
            : `http://localhost:8080/catalog/products`;
          const params = new URLSearchParams();
          params.set("limit", "200");
          if (selectedShopIdFilter) params.set("shop_id", selectedShopIdFilter);
          if (collectionFilters && collectionFilters.length > 0)
            params.set("collections", collectionFilters.join(","));
          if (cursor) params.set("cursor", cursor);
          const url = `${base}?${params.toString()}`;
          const response = await fetch(url);
          if (!response.ok) break;
          const data = await response.json();
          const page: Product[] = (
            Array.isArray(data?.items) ? data.items : []
          ).map((item: any) => {
            const assets: Asset[] = [];
            const productForNaming = {
              title: item.title || item.name || "Product",
            } as any;

            if (item.images && Array.isArray(item.images)) {
              item.images.forEach((img: any) => {
                const asset: Asset = {
                  id: `asset_${img.id}`,
                  productId: item.product_id,
                  url: img.src_url,
                  type: "image" as const,
                  filename: img.src_url.split("/").pop() || "image.jpg",
                  displayName: "",
                };
                asset.displayName = generateDisplayName(
                  asset,
                  productForNaming
                );
                assets.push(asset);
              });
            }
            if (assets.length === 0 && item.default_image_url) {
              const asset: Asset = {
                id: `asset_default_${item.product_id}`,
                productId: item.product_id,
                url: item.default_image_url,
                type: "image" as const,
                filename:
                  item.default_image_url.split("/").pop() || "image.jpg",
                displayName: "",
              };
              asset.displayName = generateDisplayName(asset, productForNaming);
              assets.push(asset);
            }
            const collections = Array.isArray(item.collections)
              ? item.collections.map((c: any) => ({
                  id: String(c.id ?? c.slug ?? c.name ?? "collection"),
                  name: String(c.name ?? c.slug ?? "Collection"),
                  color: String(c.color ?? "bg-gray-500"),
                }))
              : [];
            return {
              id: item.product_id,
              title: item.title,
              variants: [],
              collections,
              assets,
            } as Product;
          });
          all.push(...page);
          const next_c = data?.next_cursor ?? null;
          if (!next_c) break;
          cursor = next_c;
        }
        if (!cancelled) {
          allDataCacheRef.current.set(key, all);
          setAllProductsForFilters(all);
        }
      } catch {
      } finally {
        if (!cancelled) setIsFetchingAllProducts(false);
      }
    };
    loadAll();
    return () => {
      cancelled = true;
    };
  }, [
    searchQuery,
    filtersKey,
    selectedShopIdFilter,
    collectionFilters,
    testingMode,
  ]);

  // Initialize pagination from URL params (deep link)
  useEffect(() => {
    try {
      const params = new URLSearchParams(window.location.search);
      const cur = params.get("cursor");
      const lim = params.get("limit");
      const shop = params.get("shop_id");
      const cols = params.get("collections");
      if (lim) {
        const n = Number(lim);
        if ([10, 25, 50, 100].includes(n)) setPageSize(n);
      }
      if (shop) setSelectedShopIdFilter(shop);
      if (cols) setCollectionFilters(cols.split(",").filter(Boolean));
      if (cur) {
        setPageHistory([cur]);
        setPageIndex(0);
      }
    } catch {}
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch products on component mount and when pagination/filter changes
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        setProductsError(null);

        const currentCursor = pageHistory[pageIndex] || "";
        // Reflect state in URL for deep linking
        try {
          const params = new URLSearchParams(window.location.search);
          if (currentCursor) params.set("cursor", currentCursor);
          else params.delete("cursor");
          if (selectedShopIdFilter) params.set("shop_id", selectedShopIdFilter);
          else params.delete("shop_id");
          if (collectionFilters && collectionFilters.length > 0)
            params.set("collections", collectionFilters.join(","));
          else params.delete("collections");
          params.set("limit", String(pageSize));
          const qs = params.toString();
          const nextUrl = qs ? `?${qs}` : window.location.pathname;
          window.history.replaceState(null, "", nextUrl);
        } catch {}

        // Cache key
        const key = [
          `shop=${selectedShopIdFilter || ""}`,
          `collections=${(collectionFilters || []).slice().sort().join(",")}`,
          `limit=${pageSize}`,
          `cursor=${currentCursor || ""}`,
        ].join("|");

        // Serve from cache if present
        const cached = pageCache.current.get(key);
        if (cached) {
          setProducts(cached.products);
          setNextCursor(cached.nextCursor);
          setIsLoadingProducts(false);
          if (cached.nextCursor) {
            const nk = [
              `shop=${selectedShopIdFilter || ""}`,
              `collections=${(collectionFilters || [])
                .slice()
                .sort()
                .join(",")}`,
              `limit=${pageSize}`,
              `cursor=${cached.nextCursor}`,
            ].join("|");
            if (!pageCache.current.has(nk))
              void prefetchPage(cached.nextCursor);
          }
          return;
        }

        const base = testingMode
            ? `http://localhost:8080/catalog/scraped-products`
            : `http://localhost:8080/catalog/products`;
        const params = new URLSearchParams();
        params.set("limit", String(pageSize));
        if (selectedShopIdFilter) params.set("shop_id", selectedShopIdFilter);
        if (collectionFilters && collectionFilters.length > 0)
          params.set("collections", collectionFilters.join(","));
        if (currentCursor) params.set("cursor", currentCursor);
        const url = `${base}?${params.toString()}`;

        const response = await fetch(url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        const next_c = data?.next_cursor ?? null;
        setNextCursor(next_c);

        // Transform API response to match frontend Product interface
        const transformedProducts: Product[] = data.items.map((item: any) => {
          // Create product object first for context
          const product: Product = {
            id: item.product_id,
            title: item.title,
            variants: [],
            collections: Array.isArray(item.collections)
              ? item.collections.map((c: any) => ({
                  id: String(c.id ?? c.slug ?? c.name ?? "collection"),
                  name: String(c.name ?? c.slug ?? "Collection"),
                  color: String(c.color ?? "bg-gray-500"),
                }))
              : [],
            assets: [], // Will be populated below
          };

          // Create assets from the images array
          const assets: Asset[] = [];
          const productForNaming = {
            title: item.title || item.name || "Product",
          } as any;

          // Add all product images as assets
          if (item.images && Array.isArray(item.images)) {
            item.images.forEach((img: any) => {
              const asset: Asset = {
                id: `asset_${img.id}`,
                productId: item.product_id,
                url: img.src_url,
                type: "image" as const,
                filename: img.src_url.split("/").pop() || "image.jpg",
                displayName: "",
              };
              asset.displayName = generateDisplayName(asset, productForNaming);
              assets.push(asset);
            });
          }

          // If no images from the array, use the default_image_url
          if (assets.length === 0 && item.default_image_url) {
            const asset: Asset = {
              id: `asset_default_${item.product_id}`,
              productId: item.product_id,
              url: item.default_image_url,
              type: "image" as const,
              filename: item.default_image_url.split("/").pop() || "image.jpg",
              displayName: "",
            };
            asset.displayName = generateDisplayName(asset, productForNaming);
            assets.push(asset);
          }

          // Update product with assets
          product.assets = assets;
          return product;
        });

        setProducts(transformedProducts);
        // Cache current page and prefetch next
        pageCache.current.set(key, {
          products: transformedProducts,
          nextCursor: next_c,
        });
        if (next_c) {
          const nk = [
            `shop=${selectedShopIdFilter || ""}`,
            `collections=${(collectionFilters || []).slice().sort().join(",")}`,
            `limit=${pageSize}`,
            `cursor=${next_c}`,
          ].join("|");
          if (!pageCache.current.has(nk)) void prefetchPage(next_c);
        }

        // Initialize prompts for each product
        const initialPrompts: Record<string, PromptWithImages> = {};
        transformedProducts.forEach((product) => {
          initialPrompts[product.id] = createPromptFromText(
            `A professional product shot of a ${product.title.toLowerCase()}, high-resolution, on a clean white background.`
          );
        });
        setPrompts(initialPrompts);
      } catch (error) {
        console.error("Failed to fetch products:", error);
        setProductsError(
          error instanceof Error ? error.message : "Failed to load products"
        );

        // Fallback to hardcoded products if API fails
        setProducts(PRODUCTS);
        const fallbackPrompts: Record<string, PromptWithImages> = {};
        PRODUCTS.forEach((product) => {
          fallbackPrompts[product.id] = createPromptFromText(
            `A professional product shot of a ${product.title.toLowerCase()}, high-resolution, on a clean white background.`
          );
        });
        setPrompts(fallbackPrompts);
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, [
    selectedShopIdFilter,
    collectionFilters,
    pageIndex,
    pageHistory,
    pageSize,
    testingMode,
  ]);

  // Prefetch helper
  const prefetchPage = useCallback(
    async (cursor: string) => {
      try {
        const base = testingMode
          ? `http://localhost:8080/catalog/scraped-products`
          : `http://localhost:8080/catalog/products`;
        const params = new URLSearchParams();
        params.set("limit", String(pageSize));
        if (!testingMode && selectedShopIdFilter)
          params.set("shop_id", selectedShopIdFilter);
        if (!testingMode && collectionFilters && collectionFilters.length > 0)
          params.set("collections", collectionFilters.join(","));
        if (cursor) params.set("cursor", cursor);
        const url = `${base}?${params.toString()}`;
        const response = await fetch(url);
        if (!response.ok) return;
        const data = await response.json();
        const transformedProducts: Product[] = data.items.map((item: any) => {
          const assets: Asset[] = [];
          const productForNaming = {
            title: item.title || item.name || "Product",
          } as any;

          if (item.images && Array.isArray(item.images)) {
            item.images.forEach((img: any) => {
              const asset: Asset = {
                id: `asset_${img.id}`,
                productId: item.product_id,
                url: img.src_url,
                type: "image" as const,
                filename: img.src_url.split("/").pop() || "image.jpg",
                displayName: "",
              };
              asset.displayName = generateDisplayName(asset, productForNaming);
              assets.push(asset);
            });
          }
          if (assets.length === 0 && item.default_image_url) {
            const asset: Asset = {
              id: `asset_default_${item.product_id}`,
              productId: item.product_id,
              url: item.default_image_url,
              type: "image" as const,
              filename: item.default_image_url.split("/").pop() || "image.jpg",
              displayName: "",
            };
            asset.displayName = generateDisplayName(asset, productForNaming);
            assets.push(asset);
          }
          const collections = Array.isArray(item.collections)
            ? item.collections.map((c: any) => ({
                id: String(c.id ?? c.slug ?? c.name ?? "collection"),
                name: String(c.name ?? c.slug ?? "Collection"),
                color: String(c.color ?? "bg-gray-500"),
              }))
            : [];
          return {
            id: item.product_id,
            title: item.title,
            variants: [],
            collections,
            assets: assets,
          };
        });
        const next_c = data?.next_cursor ?? null;
        const nk = [
          `shop=${selectedShopIdFilter || ""}`,
          `collections=${(collectionFilters || []).slice().sort().join(",")}`,
          `limit=${pageSize}`,
          `cursor=${cursor || ""}`,
        ].join("|");
        pageCache.current.set(nk, {
          products: transformedProducts,
          nextCursor: next_c,
        });
      } catch {}
    },
    [selectedShopIdFilter, collectionFilters, pageSize]
  );

  // Reset pagination when shop filter changes
  // Reset pagination when collection filters change
  useEffect(() => {
    setPageHistory([""]);
    setPageIndex(0);
    pageCache.current.clear();
  }, [collectionFilters]);

  useEffect(() => {
    setPageHistory([""]);
    setPageIndex(0);
    pageCache.current.clear();
  }, [selectedShopIdFilter]);

  // Reset pagination when testing mode changes
  useEffect(() => {
    setPageHistory([""]);
    setPageIndex(0);
    pageCache.current.clear();
    allDataCacheRef.current.clear();
    setAllProductsForFilters(null);
    // Clear selections when switching modes
    setSelectedAssetIds(new Set());
    setSelectedProductIds(new Set());
    setActiveAsset(null);
  }, [testingMode]);

  const gotoNextPage = useCallback(() => {
    if (!nextCursor) return;
    setPageHistory((prev) => [...prev.slice(0, pageIndex + 1), nextCursor]);
    setPageIndex((i) => i + 1);
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [nextCursor, pageIndex]);

  const gotoPrevPage = useCallback(() => {
    if (pageIndex === 0) return;
    setPageIndex((i) => Math.max(0, i - 1));
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [pageIndex]);

  // Merge previously generated assets from the backend so results persist across reloads
  useEffect(() => {
    const merge = async () => {
      if (hasMergedGenerated) return;
      if (!products || products.length === 0) return;
      try {
        const resp = await fetch(
          "http://localhost:8080/assets/generated?limit=200"
        );
        if (!resp.ok) {
          setHasMergedGenerated(true);
          return;
        }
        const data = await resp.json();
        const items: Array<any> = Array.isArray(data?.items) ? data.items : [];
        if (items.length === 0) {
          setHasMergedGenerated(true);
          return;
        }
        // Build a simple object map rather than a Map to keep React state shape consistent
        const byProduct: Record<string, Asset[]> = {};
        for (const a of items) {
          const productId = a?.product_id as string | undefined;
          if (!productId || !a?.id || !a?.file_uri) continue;
          const asset: Asset = {
            id: a.id,
            productId,
            // Use preview for thumbnail if video; keep original file uri in fileUrl
            url: a.type === "video" ? a.preview_uri || a.file_uri : a.file_uri,
            type: (a.type as "image" | "video") || "image",
            filename: (a.file_uri?.split("/").pop() ||
              (a.type === "video"
                ? "generated.mp4"
                : "generated.jpg")) as string,
            fileUrl: a.file_uri || undefined,
            previewUrl: a.preview_uri || undefined,
            prompt: a.prompt || undefined,
            displayName: "",
          };
          // Generate display name for generated assets
          asset.displayName = generateDisplayName(asset);
          if (!byProduct[productId]) byProduct[productId] = [];
          byProduct[productId].push(asset);
        }
        if (Object.keys(byProduct).length === 0) {
          setHasMergedGenerated(true);
          return;
          {
            testingMode && (
              <div className="px-3 py-2 text-sm bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border-b border-orange-200 dark:border-orange-800">
                🧪 Testing Mode Active - Showing scraped product data (
                {testingStats?.total_products || 0} products from{" "}
                {testingStats?.domains?.length || 0} domains)
                <button
                  onClick={() => setTestingMode(false)}
                  className="ml-2 px-2 py-1 text-xs bg-orange-200 dark:bg-orange-800 rounded hover:bg-orange-300 dark:hover:bg-orange-700"
                >
                  Exit Testing
                </button>
              </div>
            );
          }
          {
            !testingMode && (
              <div className="px-3 py-2 text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 border-b border-blue-200 dark:border-blue-800">
                Production Mode - Showing database products
                <button
                  onClick={() => setTestingMode(true)}
                  className="ml-2 px-2 py-1 text-xs bg-blue-200 dark:bg-blue-800 rounded hover:bg-blue-300 dark:hover:bg-blue-700"
                >
                  🧪 Test Scraped Data
                </button>
              </div>
            );
          }
        }
        setProducts((prev: Product[]) =>
          prev.map((p: Product) => {
            const gen = byProduct[p.id] || [];
            if (gen.length === 0) return p;
            const existingIds = new Set(p.assets.map((x) => x.id));
            const toAdd = gen.filter((g) => !existingIds.has(g.id));
            if (toAdd.length === 0) return p;
            return { ...p, assets: [...toAdd, ...p.assets] };
          })
        );

        // Update generatedImages state for badge display
        setGeneratedImages(byProduct);
        setHasMergedGenerated(true);
      } catch {
        setHasMergedGenerated(true);
      }
    };
    merge();
  }, [products, hasMergedGenerated]);

  const handlePromptChange = useCallback(
    (productId: string, value: PromptWithImages) => {
      setPrompts((prev: Record<string, PromptWithImages>) => ({
        ...prev,
        [productId]: value,
      }));
    },
    []
  );

  const handleCopyPromptToAll = useCallback(
    (sourceProductId: string) => {
      const sourcePrompt = prompts[sourceProductId];
      if (sourcePrompt === undefined) return;

      setPrompts((prev: Record<string, PromptWithImages>) => {
        const newPrompts: Record<string, PromptWithImages> = { ...prev };
        products.forEach((p: Product) => {
          newPrompts[p.id] = sourcePrompt;
        });
        return newPrompts;
      });
    },
    [prompts, products]
  );

  const initialImageSettings: ImageSettings = {
    size: "1024x1024",
    guidance: 7.5,
    steps: 25,
    strength: 0.8,
    seed: Math.floor(Math.random() * 100000),
    upscale: true,
    safety: true,
    aspectRatio: "1:1",
    quality: "Standard",
  };

  const initialVideoSettings: VideoSettings = {
    duration: 4,
    fps: 24,
    resolution: "1080p",
    aspectRatio: "16:9",
    motionStrength: 5,
    seed: Math.floor(Math.random() * 100000),
    audio: false,
    quality: "Standard",
  };

  const [settings, setSettings] = useState<ImageSettings | VideoSettings>(
    initialImageSettings
  );

  const handleAssetSelect = useCallback(
    (asset: Asset, isMultiSelect: boolean) => {
      setSelectedAssetIds((prevSelectedAssets: Set<string>) => {
        let isToggleOff = false;
        const newAssetSet = new Set(prevSelectedAssets);

        // 1. Determine the new set of selected assets
        if (isMultiSelect) {
          if (newAssetSet.has(asset.id)) {
            newAssetSet.delete(asset.id);
            isToggleOff = true;
          } else {
            newAssetSet.add(asset.id);
            // In video mode, limit to max 1 selected image per row
            if (generationMode === "video" && (asset as any).type === "image") {
              const row = products.find(
                (p: Product) => p.id === asset.productId
              );
              const imgIds = (row?.assets || [])
                .filter((a: any) => a.type === "image")
                .map((a: any) => a.id);
              for (const id of imgIds) {
                if (id !== asset.id) newAssetSet.delete(id);
              }
            }
          }
        } else {
          const productAssets =
            products
              .find((p: Product) => p.id === asset.productId)
              ?.assets.map((a: Asset) => a.id) || [];
          const selectedInRow = [...prevSelectedAssets].filter((id) =>
            productAssets.includes(id)
          );

          for (const assetId of selectedInRow) {
            newAssetSet.delete(assetId);
          }

          if (!(selectedInRow.length === 1 && selectedInRow[0] === asset.id)) {
            newAssetSet.add(asset.id);
          } else {
            isToggleOff = true;
          }
        }

        setLastInteractedAsset({ asset, isToggleOff });

        // 2. Sync the product selection state based on the new asset set
        const productHasSelection = products
          .find((p) => p.id === asset.productId)
          ?.assets.some((a) => newAssetSet.has(a.id));

        setSelectedProductIds((prevSelectedProducts) => {
          const newProductsSet = new Set(prevSelectedProducts);
          if (productHasSelection) {
            newProductsSet.add(asset.productId);
          } else {
            newProductsSet.delete(asset.productId);
          }
          return newProductsSet;
        });

        // 3. Return the new asset set
        return newAssetSet;
      });
    },
    [products, generationMode]
  );

  const handleProductSelectionChange = useCallback(
    (productId: string, isChecked: boolean) => {
      const product = products.find((p) => p.id === productId);
      if (!product) return;

      // Update selected products set
      setSelectedProductIds((prev) => {
        const newSet = new Set(prev);
        if (isChecked) newSet.add(productId);
        else newSet.delete(productId);
        return newSet;
      });

      if (isChecked) {
        // Video mode: select at most 1 image in this row; Image mode: select all assets
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            const imageAssets = product.assets.filter(
              (a) => a.type === "image"
            );
            // Clear existing image selections in this row
            imageAssets.forEach((a) => newSet.delete(a.id));
            // Choose one image (prefer first)
            const toAdd = imageAssets[0];
            if (toAdd) newSet.add(toAdd.id);
          } else {
            product.assets.forEach((asset) => newSet.add(asset.id));
          }
          return newSet;
        });
        // Set the first relevant asset active if none is active yet
        if (!activeAsset && product.assets.length > 0) {
          if (generationMode === "video") {
            const firstImg = product.assets.find((a) => a.type === "image");
            if (firstImg) {
              setActiveAsset(firstImg);
              setLastInteractedAsset({ asset: firstImg, isToggleOff: false });
            }
          } else {
            setActiveAsset(product.assets[0]);
            setLastInteractedAsset({
              asset: product.assets[0],
              isToggleOff: false,
            });
          }
        }
      } else {
        // When unchecking the box, deselect all assets in that row.
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          product.assets.forEach((asset) => newSet.delete(asset.id));

          // Update active asset if it was in the cleared row
          if (
            activeAsset &&
            product.assets.some((a) => a.id === activeAsset.id)
          ) {
            const nextActiveAssetId = Array.from(newSet).find((id) =>
              products.flatMap((p) => p.assets).some((a) => a.id === id)
            );
            const nextActiveAsset = nextActiveAssetId
              ? products
                  .flatMap((p) => p.assets)
                  .find((a) => a.id === nextActiveAssetId)
              : null;
            setActiveAsset(nextActiveAsset || null);
          }
          return newSet;
        });
      }
    },
    [products, activeAsset]
  );

  const handleSelectAllProducts = useCallback(
    (isChecked: boolean) => {
      if (isChecked) {
        // Select all products
        const allProductIds = new Set(products.map((p) => p.id));
        setSelectedProductIds(allProductIds);

        // Select assets for all products (video: pick at most 1 image per row)
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            products.forEach((product) => {
              const firstImg = product.assets.find((a) => a.type === "image");
              if (firstImg) newSet.add(firstImg.id);
              // Ensure only one image per row remains selected
              const imageIds = product.assets
                .filter((a) => a.type === "image")
                .map((a) => a.id);
              imageIds.slice(1).forEach((id) => newSet.delete(id));
            });
          } else {
            products.forEach((product) => {
              product.assets.forEach((asset) => newSet.add(asset.id));
            });
          }
          // Set an active asset if none
          if (!activeAsset) {
            if (generationMode === "video") {
              const firstImgGlobal = products
                .flatMap((p) => p.assets)
                .find((a) => a.type === "image");
              if (firstImgGlobal) setActiveAsset(firstImgGlobal);
            } else {
              const firstAsset = products.flatMap((p) => p.assets)[0];
              if (firstAsset) setActiveAsset(firstAsset);
            }
          }
          return newSet;
        });
      } else {
        setSelectedProductIds(new Set());
        setSelectedAssetIds(new Set());
        setActiveAsset(null);
      }
    },
    [products, activeAsset]
  );

  const clearSelection = useCallback(() => {
    setSelectedAssetIds(new Set());
    setSelectedProductIds(new Set());
    setActiveAsset(null);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        clearSelection();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [clearSelection]);

  // Effect to manage activeAsset based on selections
  useEffect(() => {
    if (!lastInteractedAsset) return;

    const { asset, isToggleOff } = lastInteractedAsset;

    if (isToggleOff) {
      // If the asset that was toggled off was the active one, find a new active asset
      if (activeAsset?.id === asset.id) {
        const nextActiveAssetId = Array.from(selectedAssetIds)[0];
        const nextActiveAsset = nextActiveAssetId
          ? products
              .flatMap((p) => p.assets)
              .find((a) => a.id === nextActiveAssetId) || null
          : null;
        setActiveAsset(nextActiveAsset);
      }
    } else {
      // If an asset was selected, it becomes the active one
      setActiveAsset(asset);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAssetIds]);

  useEffect(() => {
    try {
      if (generationMode === "image") {
        const savedSettings = localStorage.getItem("lastSettings_image");
        const savedModel = localStorage.getItem("lastModel_image");
        setSettings(
          savedSettings
            ? { ...initialImageSettings, ...JSON.parse(savedSettings) }
            : initialImageSettings
        );
        const defaultImageModel = MODELS.find((m) => !m.id.includes("veo"))?.id;
        setSelectedModelId(savedModel || defaultImageModel || selectedModelId);
      } else {
        const savedSettings = localStorage.getItem("lastSettings_video");
        const savedModel = localStorage.getItem("lastModel_video");
        setSettings(
          savedSettings
            ? { ...initialVideoSettings, ...JSON.parse(savedSettings) }
            : initialVideoSettings
        );
        const defaultVideoModel = "veo-2.0-generate-001"; // prefer Veo 2 by default
        const availableVideoModel =
          MODELS.find((m) => m.id === defaultVideoModel)?.id ||
          MODELS.find((m) => m.id.includes("veo"))?.id;
        setSelectedModelId(
          savedModel || availableVideoModel || selectedModelId
        );
      }
    } catch (e) {
      // Fallback to defaults if localStorage parse fails
      if (generationMode === "image") {
        setSettings(initialImageSettings);
        const defaultImageModel = MODELS.find((m) => !m.id.includes("veo"))?.id;
        setSelectedModelId(defaultImageModel || selectedModelId);
      } else {
        setSettings(initialVideoSettings);
        const defaultVideoModel = "veo-2.0-generate-001";
        const availableVideoModel =
          MODELS.find((m) => m.id === defaultVideoModel)?.id ||
          MODELS.find((m) => m.id.includes("veo"))?.id;
        setSelectedModelId(availableVideoModel || selectedModelId);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [generationMode]);

  // Persist model selection per-mode
  useEffect(() => {
    try {
      if (generationMode === "image") {
        localStorage.setItem("lastModel_image", selectedModelId);
      } else {
        localStorage.setItem("lastModel_video", selectedModelId);
      }
    } catch {}
  }, [generationMode, selectedModelId]);

  // Persist settings per-mode
  useEffect(() => {
    try {
      const key =
        generationMode === "image"
          ? "lastSettings_image"
          : "lastSettings_video";
      localStorage.setItem(key, JSON.stringify(settings));
    } catch {}
  }, [generationMode, settings]);

  const handleGenerate = useCallback(async () => {
    const selectedCount = selectedProductIds.size;
    if (selectedCount === 0) return;

    // Enforce cap by mode (image: 5, video: 3)
    const modeCap = generationMode === "video" ? 3 : 5;
    if (selectedCount > modeCap) {
      return; // The bar will show overLimit message; don't start a batch
    }

    try {
      // Initiation UX with longer visibility
      setIsInitiating(true);
      // Build a detailed initiation message including all selected selectors
      try {
        const modelName =
          MODELS.find((m) => m.id === selectedModelId)?.name || selectedModelId;
        const s: any = settings;
        const isVideo = generationMode === "video";
        const count = Array.from(selectedProductIds).length;
        const base = `Batch started (${count} items) • Mode: ${generationMode} • Model: ${modelName}`;
        const sel = isVideo
          ? ` • AR: ${s.aspectRatio} • Res: ${s.resolution} • Dur: ${
              s.duration
            }s • FPS: ${s.fps} • Motion: ${s.motionStrength} • Quality: ${
              s.quality
            }${s.audio ? " • Audio:on" : ""} • Seed: ${s.seed}`
          : ` • Size: ${s.size} • AR: ${s.aspectRatio} • Guidance: ${
              s.guidance
            } • Steps: ${s.steps} • Strength: ${s.strength} • Quality: ${
              s.quality
            } • Seed: ${s.seed}${s.upscale ? " • Upscale:on" : ""}${
              s.safety ? " • Safety:on" : ""
            }`;
        let cols = "";
        try {
          const collList = (availableCollectionsApi ?? allCollections) || [];
          if (collectionFilters && collectionFilters.length > 0) {
            const names = collectionFilters
              .map(
                (id) =>
                  (collList as any[]).find((c: any) => c.id === id)?.name || id
              )
              .filter(Boolean);
            if (names.length > 0) cols = ` • Collections: ${names.join(", ")}`;
          }
        } catch {}

        // Add prompt and image IDs context (first item)
        let promptCtx = "";
        let imgCtx = "";
        try {
          const firstPid = Array.from(selectedProductIds)[0];
          if (firstPid) {
            const ptxt = String(prompts[firstPid]?.getText?.() ?? "").trim();
            const short = ptxt.length > 160 ? ptxt.slice(0, 157) + "…" : ptxt;
            if (short) promptCtx = ` • Prompt: "${short}"`;
            const product = products.find((p) => p.id === firstPid);
            const imageAssetIds = (product?.assets || [])
              .filter((a) => a.type === "image")
              .map((a) => a.id);
            const selectedInRow = Array.from(selectedAssetIds).filter((aid) =>
              imageAssetIds.includes(aid)
            );
            const preferAid =
              lastInteractedAsset?.asset?.productId === firstPid &&
              (lastInteractedAsset.asset as any)?.type === "image"
                ? lastInteractedAsset.asset.id
                : null;
            let chosenId: string | null = null;
            if (preferAid && imageAssetIds.includes(preferAid))
              chosenId = preferAid;
            else if (selectedInRow.length > 0) chosenId = selectedInRow[0];
            else {
              const firstImg = product?.assets.find((a) => a.type === "image");
              if (firstImg) chosenId = firstImg.id;
            }
            if (chosenId) imgCtx = ` • Images: ${chosenId}`;
          }
        } catch {}

        setInitiationMessage(
          `${base}${sel}${cols}${promptCtx}${imgCtx}. Track progress on the left.`
        );
      } catch {
        setInitiationMessage("Batch started. Track progress on the left.");
      }
      setTimeout(() => {
        setIsInitiating(false);
        setInitiationMessage(null);
      }, 6000);

      const ids = Array.from(selectedProductIds).slice(0, modeCap) as string[];
      const items = ids.map((productId: string) => {
        const product = products.find((p) => p.id === productId);
        // consider only image assets as references
        const imageAssetIds = (product?.assets || [])
          .filter((a) => a.type === "image")
          .map((a) => a.id);
        const selectedInRow = Array.from(selectedAssetIds).filter((aid) =>
          imageAssetIds.includes(aid)
        );
        const preferAid =
          lastInteractedAsset?.asset?.productId === productId &&
          (lastInteractedAsset.asset as any)?.type === "image"
            ? lastInteractedAsset.asset.id
            : null;

        const urls: string[] = (() => {
          // Get all available images for this product (product assets + generated images)
          const productGeneratedImages = generatedImages[productId] || [];
          const allImages = [
            ...(product?.assets.filter((a) => a.type === "image") || []),
            ...productGeneratedImages.filter((a) => a.type === "image"),
          ];

          // Prefer the last clicked image in this row
          if (preferAid && imageAssetIds.includes(preferAid)) {
            const u = allImages.find((a) => a.id === preferAid)?.url;
            return u ? [u] : [];
          }
          // Then prefer an explicitly selected image in this row (first one)
          if (selectedInRow.length > 0) {
            const u = allImages.find((a) => a.id === selectedInRow[0])?.url;
            return u ? [u] : [];
          }
          // Fallback to the first image in the row
          const firstImg = allImages.find((a) => a.type === "image");
          return firstImg ? [firstImg.url] : [];
        })();
        // Get prompt with attached images
        const promptData = prompts[productId] || createPromptFromText("");

        // Combine reference images from selection and attached images
        const attachedImageUrls = promptData
          .getAttachedImages()
          .map((img) => img.url);
        const allReferenceUrls = [...urls, ...attachedImageUrls];

        return {
          productId,
          prompt: promptData.getText(),
          referenceImageUrls: allReferenceUrls,
        } as {
          productId: string;
          prompt: string;
          referenceImageUrls?: string[];
        };
      });

      const start = await startBatch({
        mode: generationMode,
        model: selectedModelId,
        settings: settings as any,
        items,
      });
      if ((start as any)?.__rid || (start as any)?.request_id) {
        setLastRequestId((start as any).__rid || (start as any).request_id);
      }

      const batchId: string = start.batchId;
      // Initialize state for this batch
      // track in active batches (for aggregation) and keep single batch state for compatibility
      setActiveBatches((prev: Record<string, BatchState>) => ({
        ...prev,
        [batchId]: {
          batchId,
          total: items.length,
          completed: 0,
          failed: 0,
          status: "processing",
        },
      }));
      setGenerationBatch({
        batchId,
        total: items.length,
        completed: 0,
        failed: 0,
        status: "processing",
      });

      // Track placeholders and processed assets per batch
      const placeholderRequests = new Set<string>();
      const processedAssetIds = new Set<string>();

      const interval = window.setInterval(async () => {
        try {
          // 1) Status for counts and request IDs
          const status = await getBatchStatus(batchId);
          if ((status as any)?.__rid || (status as any)?.request_id) {
            setLastRequestId(
              (status as any).__rid || (status as any).request_id
            );
          }
          const { total, completed, failed, status: st, requests } = status;

          setActiveBatches((prev: Record<string, BatchState>) => ({
            ...prev,
            [batchId]: { batchId, total, completed, failed, status: st },
          }));
          setGenerationBatch({ batchId, total, completed, failed, status: st });

          // Surface per-item errors in a compact popover
          if (Array.isArray(requests)) {
            const failedReqs = requests.filter(
              (r: any) => r?.status === "failed" && r?.error
            );
            if (failedReqs.length > 0) {
              const sample = failedReqs
                .slice(0, 3)
                .map((r: any) => r.error)
                .join(" | ");
              const rid = (status as any)?.__rid;
              // Include selected selectors in error notification context
              const modelName =
                MODELS.find((m) => m.id === selectedModelId)?.name ||
                selectedModelId;
              const s: any = settings;
              const isVideo = generationMode === "video";
              let colsCtx = "";
              try {
                const collList =
                  (availableCollectionsApi ?? allCollections) || [];
                if (collectionFilters && collectionFilters.length > 0) {
                  const names = collectionFilters
                    .map(
                      (id) =>
                        (collList as any[]).find((c: any) => c.id === id)
                          ?.name || id
                    )
                    .filter(Boolean);
                  if (names.length > 0)
                    colsCtx = `, Collections ${names.join(", ")}`;
                }
              } catch {}

              // Add prompt and image IDs (first item) to error context
              let promptCtx = "";
              let imgCtx = "";
              try {
                const firstPid = Array.from(selectedProductIds)[0];
                if (firstPid) {
                  const ptxt = String(
                    prompts[firstPid]?.getText() ?? ""
                  ).trim();
                  const short =
                    ptxt.length > 160 ? ptxt.slice(0, 157) + "…" : ptxt;
                  if (short) promptCtx = ` • Prompt: "${short}"`;
                  const product = products.find((p) => p.id === firstPid);
                  const imageAssetIds = (product?.assets || [])
                    .filter((a) => a.type === "image")
                    .map((a) => a.id);
                  const selectedInRow = Array.from(selectedAssetIds).filter(
                    (aid) => imageAssetIds.includes(aid)
                  );
                  const preferAid =
                    lastInteractedAsset?.asset?.productId === firstPid &&
                    (lastInteractedAsset.asset as any)?.type === "image"
                      ? lastInteractedAsset.asset.id
                      : null;
                  let chosenId: string | null = null;
                  if (preferAid && imageAssetIds.includes(preferAid))
                    chosenId = preferAid;
                  else if (selectedInRow.length > 0)
                    chosenId = selectedInRow[0];
                  else {
                    const firstImg = product?.assets.find(
                      (a) => a.type === "image"
                    );
                    if (firstImg) chosenId = firstImg.id;
                  }
                  if (chosenId) imgCtx = ` • Images: ${chosenId}`;
                }
              } catch {}

              const context = isVideo
                ? ` • Context: Model ${modelName}, AR ${s.aspectRatio}, Res ${
                    s.resolution
                  }, Dur ${s.duration}s, FPS ${s.fps}, Motion ${
                    s.motionStrength
                  }, Quality ${s.quality}${s.audio ? ", Audio on" : ""}, Seed ${
                    s.seed
                  }${colsCtx}${promptCtx}${imgCtx}`
                : ` • Context: Model ${modelName}, Size ${s.size}, AR ${
                    s.aspectRatio
                  }, Guidance ${s.guidance}, Steps ${s.steps}, Strength ${
                    s.strength
                  }, Quality ${s.quality}, Seed ${s.seed}${
                    s.upscale ? ", Upscale" : ""
                  }${
                    s.safety ? ", Safety" : ""
                  }${colsCtx}${promptCtx}${imgCtx}`;
              setErrorMessage(
                `Some items failed (${failedReqs.length}). ${sample}${
                  rid ? ` (id: ${rid})` : ""
                }${context}`
              );
              // Mark their placeholders as failed to swap spinner -> red X
              setFailedPlaceholderIds((prev) => {
                const next = new Set(prev);
                for (const fr of failedReqs) {
                  if (fr?.id) next.add(`temp_${fr.id}`);
                }
                return next;
              });
            } else if (st === "completed") {
              // Clear errors when the batch completes successfully
              setErrorMessage(null);
            }
          }

          // Insert placeholders for any new requests
          if (Array.isArray(requests)) {
            for (const r of requests) {
              if (!r || !r.id || !r.product_id) continue;
              if (placeholderRequests.has(r.id)) continue;
              placeholderRequests.add(r.id);

              const placeholderAsset: Asset = {
                id: `temp_${r.id}`,
                productId: r.product_id,
                // 1x1 gray pixel PNG; reliable across browsers
                url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAc8AAnkB2nJxAQAAAABJRU5ErkJggg==",
                // Always use image type for placeholders so the loading UI matches exactly
                type: "image",
                filename: "Generating…",
              };

              setProducts((prev: Product[]) =>
                prev.map((p: Product) =>
                  p.id === r.product_id
                    ? { ...p, assets: [placeholderAsset, ...p.assets] }
                    : p
                )
              );
            }
          }

          // 2) Details for assets
          const details = await getBatchDetails(batchId);
          const reqMap = new Map<string, string>(); // request_id -> product_id
          const promptMap = new Map<string, string>(); // request_id -> prompt
          if (Array.isArray(details.requests)) {
            for (const rq of details.requests) {
              if (rq?.id && rq?.product_id) {
                reqMap.set(rq.id, rq.product_id);
                if (rq?.prompt) promptMap.set(rq.id, rq.prompt);
              }
            }
          }

          // Compute per-product request progress for this batch (totals, pending, etc.)
          if (Array.isArray(details.requests)) {
            const perProduct: Record<
              string,
              {
                total: number;
                completed: number;
                failed: number;
                pending: number;
              }
            > = {};
            for (const rq of details.requests) {
              const pid = rq?.product_id as string | undefined;
              if (!pid) continue;
              if (!perProduct[pid])
                perProduct[pid] = {
                  total: 0,
                  completed: 0,
                  failed: 0,
                  pending: 0,
                };
              perProduct[pid].total += 1;
              const st = String(rq?.status || "");
              if (st === "completed") perProduct[pid].completed += 1;
              else if (st === "failed") perProduct[pid].failed += 1;
            }
            for (const pid of Object.keys(perProduct)) {
              const r = perProduct[pid];
              r.pending = Math.max(0, r.total - r.completed - r.failed);
            }
            setRowProgressByBatch((prev) => ({
              ...prev,
              [batchId]: perProduct,
            }));
          }

          if (Array.isArray(details.assets)) {
            for (const a of details.assets) {
              if (!a?.id || !a?.source_request_id) continue;
              if (processedAssetIds.has(a.id)) continue;
              processedAssetIds.add(a.id);

              const reqId = a.source_request_id as string;
              const productId = reqMap.get(reqId);
              const prompt = promptMap.get(reqId);
              if (!productId) continue;

              // Replace placeholder with the real asset
              setProducts((prev: Product[]) =>
                prev.map((p: Product) => {
                  if (p.id !== productId) return p;
                  const placeholderId = `temp_${reqId}`;
                  const idx = p.assets.findIndex(
                    (as) => as.id === placeholderId
                  );
                  const real: Asset = {
                    id: a.id,
                    productId,
                    // Thumbnail uses preview for video; retain original file uri in fileUrl
                    url:
                      a.type === "video"
                        ? a.preview_uri || a.file_uri
                        : a.file_uri,
                    type: (a.type as "image" | "video") || "image",
                    filename: (a.file_uri?.split("/").pop() ||
                      (a.type === "video"
                        ? "generated.mp4"
                        : "generated.jpg")) as string,
                    fileUrl: (() => {
                      const direct = a.file_uri || "";
                      const isGoogle =
                        typeof direct === "string" &&
                        direct.includes("generativelanguage.googleapis.com");
                      return isGoogle
                        ? `/assets/${a.id}/stream`
                        : direct || undefined;
                    })(),
                    previewUrl: a.preview_uri || undefined,
                    prompt: prompt || undefined,
                    displayName: "",
                  };
                  // Generate display name for real-time generated assets
                  real.displayName = generateDisplayName(real, p);
                  if (idx >= 0) {
                    const newAssets = [...p.assets];
                    newAssets[idx] = real;
                    return { ...p, assets: newAssets };
                  }
                  return { ...p, assets: [real, ...p.assets] };
                })
              );

              // Update generatedImages state for badge display
              setGeneratedImages((prev) => {
                const productGen = prev[productId] || [];
                const existingIds = new Set(productGen.map((x) => x.id));
                if (!existingIds.has(a.id)) {
                  return {
                    ...prev,
                    [productId]: [
                      {
                        id: a.id,
                        productId,
                        url:
                          a.type === "video"
                            ? a.preview_uri || a.file_uri
                            : a.file_uri,
                        type: (a.type as "image" | "video") || "image",
                        filename: (a.file_uri?.split("/").pop() ||
                          (a.type === "video"
                            ? "generated.mp4"
                            : "generated.jpg")) as string,
                        fileUrl: a.file_uri || undefined,
                        previewUrl: a.preview_uri || undefined,
                        prompt: prompt || undefined,
                      },
                      ...productGen,
                    ],
                  };
                }
                return prev;
              });
            }
          }

          if (st === "completed" || st === "failed") {
            window.clearInterval(interval);
          }
        } catch (err: any) {
          console.error("Polling error", err);
          setErrorMessage(
            typeof err?.message === "string"
              ? `Polling failed: ${err.message}`
              : "Polling failed"
          );
          // Mark this batch as failed so UI spinners stop
          setActiveBatches((prev: Record<string, BatchState>) => ({
            ...prev,
            [batchId]: {
              ...(prev[batchId] || {
                batchId,
                total: 0,
                completed: 0,
                failed: 0,
                status: "failed",
              }),
              status: "failed",
            },
          }));
          setGenerationBatch((prev) =>
            prev && prev.batchId === batchId
              ? { ...prev, status: "failed" }
              : prev
          );
          // Any placeholders we inserted for this batch should briefly show red X
          setFailedPlaceholderIds((prev) => {
            const next = new Set(prev);
            placeholderRequests.forEach((rid) => next.add(`temp_${rid}`));
            return next;
          });
          window.clearInterval(interval);
        }
      }, 1200);
    } catch (e: any) {
      console.error("Failed to start batch", e);
      setErrorMessage(
        typeof e?.message === "string"
          ? `Couldn't start generation: ${e.message}`
          : "Couldn't start generation"
      );
    }
  }, [selectedProductIds, prompts, settings, generationMode, selectedModelId]);

  // Show loading state while fetching products
  if (isLoadingProducts) {
    return (
      <div className="flex flex-col h-screen font-sans text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-900">
        <Header
          theme={theme}
          setTheme={setTheme}
          testingMode={testingMode}
          onTestingModeChange={setTestingMode}
          testingStats={testingStats}
        />
        <main className="flex flex-1 items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent mx-auto mb-4"></div>
            <p className="text-lg font-medium">
              Loading products from database...
            </p>
          </div>
        </main>
        {isDev && (
          <>
            <div className="fixed bottom-4 right-4 z-50">
              <button
                className="px-3 py-2 rounded-md bg-gray-800 text-white shadow hover:bg-gray-700 text-xs"
                onClick={() => {
                  setDebugOpen(true);
                  loadLogs();
                }}
              >
                Debug
              </button>
            </div>
            {debugOpen && (
              <div className="fixed inset-0 z-50 bg-black/50 flex items-end sm:items-center justify-center">
                <div className="bg-white dark:bg-gray-900 w-full sm:w-[900px] max-h-[80vh] rounded-t-lg sm:rounded-lg shadow-lg flex flex-col">
                  <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                    <div className="text-sm font-semibold">Debug Console</div>
                    <div className="space-x-2">
                      <button
                        className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                        onClick={copyAllDebug}
                        title="Copy full debug transcript"
                      >
                        Copy full debug
                      </button>
                      <button
                        className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                        onClick={clearAllDebug}
                        title="Clear debug logs"
                      >
                        Clear
                      </button>
                      <button
                        className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                        onClick={() => setDebugOpen(false)}
                      >
                        Close
                      </button>
                    </div>
                  </div>
                  <div className="overflow-auto text-xs">
                    {usefulEvents.length === 0 ? (
                      <div className="p-4 text-gray-500">No debug events.</div>
                    ) : (
                      <div className="p-2">
                        {usefulEvents
                          .slice()
                          .reverse()
                          .map((ev, idx) => (
                            <details
                              key={idx}
                              className="mb-2 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50"
                            >
                              <summary className="cursor-pointer px-2 py-1 text-[11px] font-mono">
                                {[
                                  ev?.ts || ev?.time,
                                  ev?.kind,
                                  ev?.method,
                                  ev?.url,
                                ]
                                  .filter(Boolean)
                                  .join(" ")}
                                {typeof ev?.status === "number"
                                  ? ` -> ${ev.status}`
                                  : ""}
                                {typeof ev?.durationMs === "number"
                                  ? ` (${ev.durationMs}ms)`
                                  : ""}
                                {ev?.rid ? ` · rid=${ev.rid}` : ""}
                              </summary>
                              <div className="px-3 pb-2 text-[11px] font-mono">
                                {ev?.note && (
                                  <div className="mb-1">
                                    <strong>Note:</strong> {String(ev.note)}
                                  </div>
                                )}
                                {ev?.meta && (
                                  <pre className="whitespace-pre-wrap max-h-40 overflow-auto">
                                    {JSON.stringify(ev.meta, null, 2)}
                                  </pre>
                                )}
                                {!ev?.meta && (
                                  <pre className="whitespace-pre-wrap max-h-40 overflow-auto">
                                    {JSON.stringify(ev, null, 2)}
                                  </pre>
                                )}
                              </div>
                            </details>
                          ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen font-sans text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-900">
      <Header
        theme={theme}
        setTheme={setTheme}
        testingMode={testingMode}
        onTestingModeChange={setTestingMode}
        testingStats={testingStats}
      />
      <main className="flex flex-1 overflow-hidden">
        {/* Left pane contains product grid and controls */}
        <div className="w-2/3 flex flex-col border-r border-gray-200 dark:border-gray-700">
          {productsError && (
            <div className="px-3 py-2 text-sm bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-200 border-b border-amber-200 dark:border-amber-800">
              Failed to load products from API ({productsError}). Showing
              fallback data.
            </div>
          )}
          {testingMode && (
            <div className="px-3 py-2 text-sm bg-orange-100 text-orange-800 dark:bg-orange-900/50 dark:text-orange-200 border-b border-orange-200 dark:border-orange-800">
              🧪 Testing Mode Active - Showing scraped product data (
              {testingStats?.total_products || 0} products from{" "}
              {testingStats?.domains?.length || 0} domains)
              <button
                onClick={() => setTestingMode(false)}
                className="ml-2 px-2 py-1 text-xs bg-orange-200 dark:bg-orange-800 rounded hover:bg-orange-300 dark:hover:bg-orange-700"
              >
                Exit Testing
              </button>
            </div>
          )}
          {!testingMode && (
            <div className="px-3 py-2 text-sm bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200 border-b border-blue-200 dark:border-blue-800">
              Production Mode - Showing database products
              <button
                onClick={() => setTestingMode(true)}
                className="ml-2 px-2 py-1 text-xs bg-blue-200 dark:bg-blue-800 rounded hover:bg-blue-300 dark:hover:bg-blue-700"
              >
                🧪 Test Scraped Data
              </button>
            </div>
          )}
          <ProductGrid
            products={filteredProducts}
            selectedAssetIds={selectedAssetIds}
            onAssetSelect={handleAssetSelect}
            prompts={prompts}
            onPromptChange={handlePromptChange}
            onTabChange={setActiveMainTab}
            selectedProductIds={selectedProductIds}
            onProductSelectionChange={handleProductSelectionChange}
            onSelectAllProducts={handleSelectAllProducts}
            onCopyPromptToAll={handleCopyPromptToAll}
            generationBatch={(() => {
              const values = Object.values(activeBatches) as BatchState[];
              if (values.length === 0) return generationBatch;
              const total = values.reduce(
                (sum: number, b: BatchState) => sum + (b?.total || 0),
                0
              );
              const completed = values.reduce(
                (sum: number, b: BatchState) => sum + (b?.completed || 0),
                0
              );
              const failed = values.reduce(
                (sum: number, b: BatchState) => sum + (b?.failed || 0),
                0
              );
              const anyProcessing = values.some(
                (b: BatchState) => b && b.status === "processing"
              );
              const status = anyProcessing ? "processing" : "completed";
              return { batchId: "aggregate", total, completed, failed, status };
            })()}
            failedPlaceholderIds={failedPlaceholderIds}
            generatedImages={generatedImages}
            availableCollections={availableCollectionsApi ?? allCollections}
            collectionFilters={collectionFilters}
            onCollectionFiltersChange={setCollectionFilters}
            searchQuery={searchQuery}
            onSearchQueryChange={setSearchQuery}
            filterSelectedOnly={filterSelectedOnly}
            onFilterSelectedOnlyChange={setFilterSelectedOnly}
            filterHasGenerated={filterHasGenerated}
            onFilterHasGeneratedChange={setFilterHasGenerated}
            sortMode={sortMode}
            onSortModeChange={setSortMode}
            rowCountsByProduct={(() => {
              const pendingAgg: Record<string, number> = {};
              for (const bid of Object.keys(rowProgressByBatch)) {
                const per = rowProgressByBatch[bid] || {};
                for (const pid of Object.keys(per)) {
                  const r = per[pid];
                  pendingAgg[pid] = (pendingAgg[pid] || 0) + (r?.pending || 0);
                }
              }
              const out: Record<
                string,
                { completed: number; total: number; pending: number }
              > = {};
              const ids = new Set<string>([
                ...filteredProducts.map((p) => p.id),
                ...Object.keys(generatedImages || {}),
                ...Object.keys(pendingAgg || {}),
              ]);
              ids.forEach((pid) => {
                const completed = (generatedImages[pid] || []).length;
                const pending = pendingAgg[pid] || 0;
                out[pid] = { completed, pending, total: completed + pending };
              });
              return out;
            })()}
            productTotalCount={productTotalCount ?? undefined}
          />
        </div>

        {/* Right pane contains canvas */}
        <div className="w-1/3 min-w-[340px] flex flex-col bg-white dark:bg-gray-900">
          {/* Canvas area */}
          <div className="flex-1 flex flex-col bg-gray-200 dark:bg-gray-800/50 overflow-hidden">
            <MainTabs
              activeTab={activeMainTab}
              onTabChange={setActiveMainTab}
            />
            <div className="flex-1 p-4 lg:p-8 overflow-auto flex items-center justify-center">
              {activeMainTab === "canvas" && <PreviewPane />}
              {activeMainTab === "models" && (
                <PlaceholderContent title="Models" />
              )}
              {activeMainTab === "props" && (
                <PlaceholderContent title="Props" />
              )}
              {activeMainTab === "scenes" && (
                <ScenesPane
                  generationMode={generationMode}
                  onModeChange={setGenerationMode}
                />
              )}
              {activeMainTab === "brandbook" && <BrandbookPane />}
              {/* Pagination controls (bottom-left) */}
              <div className="fixed left-2 bottom-2 z-30">
                <div className="flex items-center gap-2 rounded-full border border-gray-200 dark:border-gray-700 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm text-xs px-2 py-1 shadow">
                  <button
                    onClick={gotoPrevPage}
                    disabled={
                      (searchQuery || "").trim().length > 0 || pageIndex === 0
                    }
                    className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
                    title="Previous page"
                    aria-label="Previous page"
                  >
                    <ArrowLeftIcon className="h-4 w-4 text-gray-700 dark:text-gray-200" />
                  </button>
                  <span
                    className="px-1.5 tabular-nums text-gray-700 dark:text-gray-100 font-medium"
                    aria-live="polite"
                  >
                    {(() => {
                      const isClientFilterActive =
                        (searchQuery || "").trim().length > 0 ||
                        filterSelectedOnly ||
                        filterHasGenerated;
                      if (isClientFilterActive) {
                        return `Results: ${filteredProducts.length}`;
                      }
                      const pageNum = pageIndex + 1;
                      const total = productTotalCount ?? null;
                      const totalPages = total
                        ? Math.max(1, Math.ceil(total / pageSize))
                        : null;
                      return totalPages
                        ? `Page ${pageNum}/${totalPages}`
                        : `Page ${pageNum}`;
                    })()}
                  </span>
                  <div className="w-14">
                    <CustomSelect
                      label="Per page"
                      value={String(pageSize)}
                      onChange={(val: string) => setPageSize(Number(val))}
                      options={[
                        { value: "10", label: "10" },
                        { value: "25", label: "25" },
                        { value: "50", label: "50" },
                        { value: "100", label: "100" },
                      ]}
                      className=""
                      popupPosition="top"
                      compact
                    />
                  </div>
                  <button
                    onClick={gotoNextPage}
                    disabled={
                      (searchQuery || "").trim().length > 0 ||
                      filterSelectedOnly ||
                      filterHasGenerated ||
                      !nextCursor
                    }
                    className="inline-flex items-center justify-center h-8 w-8 rounded-full bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 shadow hover:bg-white dark:hover:bg-gray-700 disabled:opacity-40 disabled:cursor-not-allowed"
                    title="Next page"
                    aria-label="Next page"
                  >
                    <ArrowRightIcon className="h-4 w-4 text-gray-700 dark:text-gray-200" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      <FloatingGenerateBar
        selectedCount={selectedProductIds.size}
        generationMode={generationMode}
        onModeChange={setGenerationMode}
        settings={settings}
        setSettings={setSettings}
        selectedModelId={selectedModelId}
        onModelChange={setSelectedModelId}
        onGenerate={handleGenerate}
        generationBatch={(() => {
          // Aggregate across active batches for the progress text and icon logic
          const values = Object.values(activeBatches) as BatchState[];
          if (values.length === 0) return generationBatch;
          const total = values.reduce(
            (sum: number, b: BatchState) => sum + (b?.total || 0),
            0
          );
          const completed = values.reduce(
            (sum: number, b: BatchState) => sum + (b?.completed || 0),
            0
          );
          const failed = values.reduce(
            (sum: number, b: BatchState) => sum + (b?.failed || 0),
            0
          );
          const anyProcessing = values.some(
            (b: BatchState) => b && b.status === "processing"
          );
          const status = anyProcessing ? "processing" : "completed";
          return { batchId: "aggregate", total, completed, failed, status };
        })()}
        isInitiating={isInitiating}
        initiationMessage={initiationMessage}
        overLimit={
          generationMode === "video"
            ? selectedProductIds.size > 3
            : selectedProductIds.size > 5
        }
        overLimitMessage={
          generationMode === "video"
            ? "Select up to 3 products for video."
            : "Select up to 5 products."
        }
        errorMessage={errorMessage}
        onDismissMessage={() => {
          // Clear the error message and tidy up failed UI elements in sync
          setErrorMessage(null);
          // Remove failed placeholders from the grid
          if (failedPlaceholderIds.size > 0) {
            setProducts((prev: Product[]) =>
              prev.map((p) => ({
                ...p,
                assets: p.assets.filter((a) => !failedPlaceholderIds.has(a.id)),
              }))
            );
            setFailedPlaceholderIds(new Set());
          }
          // Remove any failed batches from aggregation so progress UI disappears
          setActiveBatches((prev: Record<string, BatchState>) => {
            const next: Record<string, BatchState> = {};
            for (const [k, v] of Object.entries(prev)) {
              if (v?.status !== "failed") next[k] = v;
            }
            return next;
          });
          setGenerationBatch((prev) =>
            prev?.status === "failed" ? null : prev
          );
          setRowProgressByBatch((prev) => {
            const out: typeof prev = {} as any;
            for (const [bid, data] of Object.entries(prev)) {
              const b = activeBatches[bid];
              if (!b || b.status !== "failed") out[bid] = data;
            }
            return out;
          });
        }}
        onDismissInitiationMessage={() => setInitiationMessage(null)}
        lastRequestId={lastRequestId}
      />
      {isDev && (
        <>
          <div className="fixed bottom-4 right-4 z-50">
            <button
              className="px-3 py-2 rounded-md bg-gray-800 text-white shadow hover:bg-gray-700 text-xs"
              onClick={() => {
                setDebugOpen(true);
                loadLogs();
              }}
            >
              Debug
            </button>
          </div>
          {debugOpen && (
            <div className="fixed inset-0 z-50 bg-black/50 flex items-end sm:items-center justify-center">
              <div className="bg-white dark:bg-gray-900 w-full sm:w-[900px] max-h-[80vh] rounded-t-lg sm:rounded-lg shadow-lg flex flex-col">
                <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
                  <div className="text-sm font-semibold">Debug Console</div>
                  <div className="space-x-2">
                    <button
                      className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                      onClick={copyAllDebug}
                    >
                      Copy all
                    </button>
                    <button
                      className="px-3 py-1 text-xs rounded bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600"
                      onClick={() => setDebugOpen(false)}
                    >
                      Close
                    </button>
                  </div>
                </div>
                <div className="overflow-auto text-xs">
                  {usefulEvents.length === 0 ? (
                    <div className="p-4 text-gray-500">No debug events.</div>
                  ) : (
                    <div className="p-2">
                      {usefulEvents
                        .slice()
                        .reverse()
                        .map((ev, idx) => (
                          <details
                            key={idx}
                            className="mb-2 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50"
                          >
                            <summary className="cursor-pointer px-2 py-1 text-[11px] font-mono">
                              {[
                                ev?.ts || ev?.time,
                                ev?.kind,
                                ev?.method,
                                ev?.url,
                              ]
                                .filter(Boolean)
                                .join(" ")}
                              {typeof ev?.status === "number"
                                ? ` -> ${ev.status}`
                                : ""}
                              {typeof ev?.durationMs === "number"
                                ? ` (${ev.durationMs}ms)`
                                : ""}
                              {ev?.rid ? ` · rid=${ev.rid}` : ""}
                            </summary>
                            <div className="px-3 pb-2 text-[11px] font-mono">
                              {ev?.note && (
                                <div className="mb-1">
                                  <strong>Note:</strong> {String(ev.note)}
                                </div>
                              )}
                              {ev?.meta && (
                                <pre className="whitespace-pre-wrap max-h-40 overflow-auto">
                                  {JSON.stringify(ev.meta, null, 2)}
                                </pre>
                              )}
                              {!ev?.meta && (
                                <pre className="whitespace-pre-wrap max-h-40 overflow-auto">
                                  {JSON.stringify(ev, null, 2)}
                                </pre>
                              )}
                            </div>
                          </details>
                        ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default App;
