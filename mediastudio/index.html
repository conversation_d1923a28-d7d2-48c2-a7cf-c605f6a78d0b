<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Shopify Asset Studio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        darkMode: 'class',
        theme: {
          extend: {
            colors: {
              'primary': 'rgb(34 197 94)',
              'primary-hover': 'rgb(22 163 74)',
              'primary-focus': 'rgb(21 128 61)',
            }
          }
        }
      }
    </script>
    <style>
      /* Custom scrollbar for asset rows */
      .asset-scrollbar {
        scrollbar-width: thin;
        scrollbar-color: #a0aec0 #edf2f7; /* thumb track */
      }
      .dark .asset-scrollbar {
        scrollbar-color: #4a5568 #2d3748; /* thumb track */
      }
      .asset-scrollbar::-webkit-scrollbar {
        height: 6px;
      }
      .asset-scrollbar::-webkit-scrollbar-track {
        background: #edf2f7;
        border-radius: 10px;
      }
      .dark .asset-scrollbar::-webkit-scrollbar-track {
        background: #2d3748;
      }
      .asset-scrollbar::-webkit-scrollbar-thumb {
        background-color: #a0aec0;
        border-radius: 10px;
        border: 2px solid #edf2f7;
      }
      .dark .asset-scrollbar::-webkit-scrollbar-thumb {
        background-color: #4a5568;
        border-color: #2d3748;
      }
    </style>
    <script>
      // Set theme on initial load to prevent flash
      document.documentElement.classList.remove('dark');
    </script>
  <script type="importmap">
{
  "imports": {
    "react-dom/": "https://aistudiocdn.com/react-dom@^19.1.1/",
    "react/": "https://aistudiocdn.com/react@^19.1.1/",
    "react": "https://aistudiocdn.com/react@^19.1.1"
  }
}
</script>
</head>
  <body class="bg-gray-100 dark:bg-gray-900 antialiased">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>