import React, { useEffect, useMemo, useRef, useState } from "react";
import { Link } from "react-router-dom";
import { Header } from "../components/Header";
import { CustomSelect } from "../components/CustomSelect";
import { useTheme } from "../hooks/useTheme";

interface CollectionInfo {
  slug: string;
  title: string;
  count?: number;
  available_count?: number;
  scraped_count?: number;
}

interface JobStatus {
  job: { id: string; status: string; domain: string; shop_id: string };
  counts: Record<string, number>;
}

interface ScrapedProduct {
  id: string;
  title: string;
  handle: string;
  domain: string;
  shop_id: string;
  scraped_at: string;
  images: Array<{
    id: string;
    src_url: string;
    width?: number;
    height?: number;
    alt?: string;
  }>;
  variants: Array<{
    id: string;
    sku?: string;
    price: string;
    option1?: string;
    option2?: string;
    option3?: string;
  }>;
  raw_json: any;
}

const FetchPage: React.FC = () => {
  const [theme, setTheme] = useTheme();
  const [scrapedProducts, setScrapedProducts] = useState<ScrapedProduct[]>([]);

  // Scraper state
  const [domain, setDomain] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [collections, setCollections] = useState<CollectionInfo[]>([]);
  const [totalCollections, setTotalCollections] = useState<number | null>(null);
  const [selected, setSelected] = useState<Record<string, { selected: boolean }>>({});
  const [jobId, setJobId] = useState<string | null>(null);
  const [status, setStatus] = useState<JobStatus | null>(null);
  const [search, setSearch] = useState("");
  const pollRef = useRef<number | null>(null);
  const PAGE_SIZE = 25;
  const [perCollectionLimit, setPerCollectionLimit] = useState<number>(50);
  const [error, setError] = useState<string | null>(null);

  // Domain file-driven flow
  const [domains, setDomains] = useState<string[]>([]);
  const [selectedDomain, setSelectedDomain] = useState<string>("");

  // Debug logging for collections calls and UI clicks
  type DebugHeaders = Record<string, string>;
  interface DebugEntry {
    id: string;
    time: string;
    action: string;
    request: { url: string; method: string; headers: DebugHeaders; body?: string };
    response: {
      status: number;
      statusText: string;
      headers: DebugHeaders;
      bodyPreview: string;
      durationMs: number;
      requestId?: string;
    };
    error?: string;
  }
  const [debugOpen, setDebugOpen] = useState(false);
  const [debugLogs, setDebugLogs] = useState<DebugEntry[]>([]);
  const headersToObject = (h: Headers | Record<string, string>): DebugHeaders =>
    h instanceof Headers ? Object.fromEntries(h.entries()) : (h || {});
  const makeCurl = (req: DebugEntry["request"]) => {
    let cmd = `curl -i -X ${req.method} '${req.url}'`;
    for (const [k, v] of Object.entries(req.headers || {}))
      cmd += ` -H ${JSON.stringify(`${k}: ${v}`)}`;
    if (req.body) cmd += ` --data ${JSON.stringify(req.body)}`;
    return cmd;
  };
  const addDebug = (e: DebugEntry) =>
    setDebugLogs((prev) => [{ ...e }, ...prev].slice(0, 50));

  // Build a single text blob for all debug entries (for easy copy)
  const buildDebugBundle = () => {
    const lines: string[] = [];
    for (const log of debugLogs.slice().reverse()) {
      lines.push(`=== ${log.time} :: ${log.action} ===`);
      lines.push(`REQUEST: ${log.request.method} ${log.request.url}`);
      lines.push(`Request headers: ${JSON.stringify(log.request.headers, null, 2)}`);
      if (log.request.body) lines.push(`Request body: ${log.request.body}`);
      lines.push(
        `RESPONSE: ${log.response.status} ${log.response.statusText} — ${log.response.durationMs}ms` +
          (log.response.requestId ? ` — reqId: ${log.response.requestId}` : "")
      );
      lines.push(`Response headers: ${JSON.stringify(log.response.headers, null, 2)}`);
      if (log.response.bodyPreview) {
        lines.push(`Response body (preview):`);
        lines.push(log.response.bodyPreview);
      }
      if (log.error) lines.push(`ERROR: ${log.error}`);
      // cURL for reproduction
      lines.push("");
      lines.push("cURL:");
      lines.push(makeCurl(log.request));
      lines.push("");
    }
    return lines.join("\n");
  };

  // User-facing notice for success info
  const [notice, setNotice] = useState<string | null>(null);

  // Initial load of available domains from server (no auto-loading on selection)
  useEffect(() => {
    const loadDomains = async () => {
      try {
        const r = await fetch("http://localhost:8080/scraper/domains");
        const d = await r.json();
        setDomains((d.domains || []).map((x: any) => x.domain));
      } catch {
        // ignore
      }
    };
    loadDomains();
  }, []);

  const getDropdownOptions = () => {
    const domainOptions = domains.map((d) => ({
      value: d,
      label: d,
      type: "domain" as const,
      icon: "🛍️",
    }));
    const addNew = [
      {
        value: "__add_new__",
        label: "Add new domain",
        type: "action" as const,
        icon: "➕",
      },
    ];
    return [...domainOptions, ...addNew];
  };

  // Fetch collections, using optional override for domain to avoid relying on async state timing
  // refreshOverride=true will append &refresh=1 to force recomputing counts server-side
  const loadCollections = async (
    reset: boolean = false,
    domainOverride?: string,
    refreshOverride?: boolean
  ) => {
    const d = (domainOverride ?? domain).trim();
    if (!d) {
      setError("Select a domain first");
      return;
    }
    setError(null);
    setNotice(null);
    setLoading(true);
    try {
      const offset = reset ? 0 : collections.length;

      // Prefill from saved collections on reset to improve UX and avoid redundant recomputation
      if (reset) {
        try {
          const rSaved = await fetch(
            `http://localhost:8080/scraper/collections-saved?domain=${encodeURIComponent(d)}`
          );
          if (rSaved.ok) {
            const saved = await rSaved.json();
            if (Array.isArray(saved.collections) && saved.collections.length > 0) {
              const next = saved.collections as CollectionInfo[];
              setCollections(next);
              const init: Record<string, { selected: boolean }> = {};
              for (const c of next) init[c.slug] = { selected: false };
              setSelected(init);
              if (typeof saved.total === "number") setTotalCollections(saved.total);
              setDomain(d);
              setNotice(`Loaded ${next.length} saved collections from ${d}`);
            }
          }
        } catch {}
      }

      const url = `http://localhost:8080/scraper/collections?domain=${encodeURIComponent(
        d
      )}&limit=${PAGE_SIZE}&offset=${offset}&fast=1&debug=1${refreshOverride ? "&refresh=1" : ""}`;

      // Instrumented network call with full debug capture
      const startedAt = performance.now();
      const reqHeaders: Record<string, string> = { Accept: "application/json" };
      let res: Response | null = null;
      let status = 0;
      let statusText = "";
      let resHeaders: Record<string, string> = {};
      let text = "";
      let data: any = {};
      let errMsg: string | undefined;
      let durationMs = 0;

      try {
        res = await fetch(url, { headers: reqHeaders });
        status = res.status;
        statusText = res.statusText || "";
        resHeaders = headersToObject(res.headers);
        text = await res.text();
        try {
          data = JSON.parse(text);
        } catch {
          data = {};
        }
      } catch (e: any) {
        errMsg = String(e?.message || e);
        throw e;
      } finally {
        durationMs = Math.round(performance.now() - startedAt);
        addDebug({
          id: Math.random().toString(36).slice(2),
          time: new Date().toISOString(),
          action: "fetch_collections",
          request: { url, method: "GET", headers: reqHeaders },
          response: {
            status,
            statusText,
            headers: resHeaders,
            bodyPreview: (text || "").slice(0, 4000),
            durationMs,
            requestId:
              (resHeaders["x-request-id"] as string) ||
              (resHeaders["X-Request-Id"] as unknown as string),
          },
          error: errMsg,
        });
      }

      if (!(status >= 200 && status < 300)) {
        const msg =
          (data?.error?.message as string) || `Request failed (${status})`;
        const hint = data?.error?.hint ? ` — ${data.error.hint}` : "";
        setError(`${msg}${hint}`);
        return;
      }

      // Track total from backend (fast-mode estimate)
      if (typeof data?.total === "number") {
        setTotalCollections(data.total);
      }

      if (reset) {
        const next = data.collections || [];
        setCollections(next);
        const init: Record<string, { selected: boolean }> = {};
        for (const c of next) init[c.slug] = { selected: false };
        setSelected(init);
        // keep state domain aligned for subsequent operations
        setDomain(d);
        setNotice(
          `Fetched ${next.length}${data?.total != null ? ` of ${data.total}` : ""} collections • ${status} ${statusText} • ${durationMs} ms`
        );
      } else {
        const more: CollectionInfo[] = data.collections || [];
        const prevSlugs = new Set(collections.map((x) => x.slug));
        const uniqueMore = more.filter((m) => !prevSlugs.has(m.slug));
        const newTotalFetched = collections.length + uniqueMore.length;

        setCollections((prev) => {
          const seen = new Set(prev.map((x) => x.slug));
          const merged = [...prev, ...more.filter((m) => !seen.has(m.slug))];
          return merged;
        });
        setSelected((prev) => {
          const next = { ...prev } as Record<string, { selected: boolean }>;
          for (const c of more) if (!next[c.slug]) next[c.slug] = { selected: false };
          return next;
        });
        setNotice(
          uniqueMore.length > 0
            ? `Loaded ${uniqueMore.length} more collections (fetched ${newTotalFetched}${data?.total != null ? ` of ${data.total}` : ""}) • ${status} ${statusText} • ${durationMs} ms`
            : `No more collections to load • ${status} ${statusText} • ${durationMs} ms`
        );
      }
    } catch (e: any) {
      console.error(e);
      setError(String(e?.message || e) || "Unexpected error");
    } finally {
      setLoading(false);
    }
  };

  const startJob = async () => {
    const d = domain.trim();
    if (!d) {
      setError("Select a domain first");
      return;
    }
    const selections = Object.entries(
      selected as Record<string, { selected: boolean }>
    )
      .filter(([_, v]) => v.selected)
      .map(([slug]) => ({ slug }));
    if (!selections.length) return;

    const res = await fetch("http://localhost:8080/scraper/jobs", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        domain: d,
        selections,
        per_collection_limit: Number.isFinite(perCollectionLimit)
          ? perCollectionLimit
          : undefined,
      }),
    });
    const data = await res.json();
    setJobId(data.job_id);

    // Start polling
    if (pollRef.current) window.clearInterval(pollRef.current);
    pollRef.current = window.setInterval(async () => {
      if (!data.job_id) return;
      const r = await fetch(
        `http://localhost:8080/scraper/jobs/${data.job_id}/status`
      );
      const s = await r.json();
      setStatus(s);
      if (
        s?.job?.status === "completed" ||
        s?.job?.status === "failed" ||
        s?.job?.status === "canceled"
      ) {
        if (pollRef.current) window.clearInterval(pollRef.current);
        pollRef.current = null;
        // Reload scraped data after job completion
        const response = await fetch(
          `http://localhost:8080/scraper/data?domain=${encodeURIComponent(d)}`
        );
        if (response.ok) {
          const data = await response.json();
          setScrapedProducts(data);
        }
      }
    }, 1000);
  };

  const pauseJob = async () => {
    if (!jobId) return;
    await fetch(`http://localhost:8080/scraper/jobs/${jobId}/pause`, {
      method: "POST",
    });
  };
  const resumeJob = async () => {
    if (!jobId) return;
    await fetch(`http://localhost:8080/scraper/jobs/${jobId}/resume`, {
      method: "POST",
    });
  };
  const cancelJob = async () => {
    if (!jobId) return;
    await fetch(`http://localhost:8080/scraper/jobs/${jobId}/cancel`, {
      method: "POST",
    });
  };

  const totals = useMemo(() => {
    const c = status?.counts || {};
    const total = Object.values(c).reduce(
      (a: number, b: any) => a + (Number(b) || 0),
      0
    );
    const done = (Number(c["done"]) || 0) + (Number(c["skipped"]) || 0);
    return { total, done };
  }, [status]);

  const filteredCollections = useMemo(() => {
    const q = search.trim().toLowerCase();
    if (!q) return collections;
    return collections.filter(
      (c) => c.title.toLowerCase().includes(q) || c.slug.toLowerCase().includes(q)
    );
  }, [collections, search]);

  const canFetchMore =
    !!selectedDomain &&
    !loading &&
    (totalCollections == null || collections.length < totalCollections);

  const nextFetchButtonLabel = loading
    ? "Loading…"
    : collections.length > 0
    ? `Fetch ${PAGE_SIZE} more`
    : `Fetch ${PAGE_SIZE} collections`;

  return (
    <div className="flex flex-col h-screen font-sans text-gray-800 dark:text-gray-200 bg-gray-100 dark:bg-gray-900">
      <Header theme={theme} setTheme={setTheme} />
      <main className="flex flex-1 overflow-hidden">
        {/* Left pane - Scraper Controls */}
        <div className="w-1/3 min-w-[400px] flex flex-col border-r border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 overflow-y-auto">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-xl font-semibold">Product Scraper</h1>
              <Link
                to="/"
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 rounded"
              >
                ← Back to Main App
              </Link>
            </div>
          </div>

          <div className="flex-1 min-h-0 p-6 space-y-4">
            {error && (
              <div className="text-xs flex items-start justify-between gap-3 px-3 py-2 rounded-lg bg-red-100 text-red-800 dark:bg-red-900/40 dark:text-red-200">
                <span className="leading-snug">{error}</span>
                <button
                  className="text-red-700/70 hover:text-red-900 dark:text-red-300/80 dark:hover:text-red-200"
                  onClick={() => setError(null)}
                  aria-label="Dismiss error"
                >
                  ✕
                </button>
              </div>
            )}

            {notice && (
              <div className="text-xs px-3 py-2 rounded-lg bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-200">
                {notice}
              </div>
            )}

            {status && (
              <div className="text-xs text-gray-600 dark:text-gray-300 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <div>
                  Status: <span className="font-semibold">{status.job.status}</span>
                </div>
                <div>Progress: {totals.done} / {totals.total}</div>
                <div className="mt-2 grid grid-cols-2 gap-1">
                  {Object.entries(status.counts).map(([k, v]) => (
                    <div key={k} className="flex justify-between">
                      <span>{k}</span>
                      <span className="font-mono">{v}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Network debug panel */}
            <div className="flex items-center justify-between mb-2">
              <div className="text-xs text-gray-500">Network debug</div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => {
                    const all = buildDebugBundle();
                    if (all) navigator.clipboard.writeText(all);
                  }}
                  className="h-7 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-xs"
                  type="button"
                  title="Copy entire debug transcript"
                >
                  Copy full debug
                </button>
                <button
                  onClick={() => setDebugOpen((v) => !v)}
                  className="h-7 px-2 rounded bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-xs"
                  aria-pressed={debugOpen}
                  type="button"
                  title="Show/hide network debug logs"
                >
                  {debugOpen ? "Hide debug" : "Show debug"}
                </button>
              </div>
            </div>
            {debugOpen && (
              <div className="mb-3 p-2 rounded border border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 max-h-56 overflow-auto">
                {debugLogs.length === 0 ? (
                  <div className="text-xs text-gray-500">
                    No debug entries yet. Click actions to see logs.
                  </div>
                ) : (
                  debugLogs.map((log) => (
                    <details key={log.id} className="mb-2">
                      <summary className="cursor-pointer text-xs">
                        [{new Date(log.time).toLocaleTimeString()}] {log.action} — {log.response.status} {log.response.statusText}
                        {log.error ? <span className="text-red-600"> — {log.error}</span> : null}
                      </summary>
                      <div className="mt-1 pl-3">
                        <div className="text-[11px] font-mono break-words">
                          <div className="mb-1">
                            <strong>Request:</strong> {log.request.method} {log.request.url}
                          </div>
                          <pre className="whitespace-pre-wrap mb-1">Headers: {JSON.stringify(log.request.headers, null, 2)}</pre>
                          {log.request.body && (
                            <pre className="whitespace-pre-wrap mb-1">Body: {log.request.body}</pre>
                          )}
                          <div className="mb-1">
                            <strong>Response:</strong> {log.response.status} {log.response.statusText} — {log.response.durationMs}ms
                            {log.response.requestId ? <> — reqId: {log.response.requestId}</> : null}
                          </div>
                          <pre className="whitespace-pre-wrap mb-1">
Resp headers: {JSON.stringify(log.response.headers, null, 2)}
                          </pre>
                          <pre className="whitespace-pre-wrap max-h-40 overflow-auto">
{log.response.bodyPreview}
                          </pre>
                          <div className="mt-1">
                            <button
                              type="button"
                              className="text-[11px] px-2 py-1 rounded bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600"
                              onClick={() => navigator.clipboard.writeText(makeCurl(log.request))}
                              title="Copy as cURL"
                            >
                              Copy as cURL
                            </button>
                          </div>
                        </div>
                      </div>
                    </details>
                  ))
                )}
              </div>
            )}

            <CustomSelect
              label="Domain"
              value={selectedDomain}
              onChange={async (val) => {
                if (val === "__add_new__") {
                  const input = window.prompt("Enter new domain (e.g. example.com)");
                  if (input && input.trim()) {
                    const domainInput = input
                      .trim()
                      .replace(/^https?:\/\//, "")
                      .replace(/\/+$/, "");
                    try {
                      await fetch("http://localhost:8080/scraper/domains", {
                        method: "POST",
                        headers: { "Content-Type": "application/json" },
                        body: JSON.stringify({ domain: domainInput }),
                      });
                      setSelectedDomain(domainInput);
                      const r = await fetch("http://localhost:8080/scraper/domains");
                      const d = await r.json();
                      setDomains((d.domains || []).map((x: any) => x.domain));
                    } catch {
                      // ignore
                    }
                  }
                } else {
                  // no auto-loading on selection
                  setSelectedDomain(val);
                }
              }}
              options={getDropdownOptions()}
              className="mb-1"
              popupPosition="bottom"
            />

            <div className="flex gap-2 mb-2">
              <button
                onClick={async () => {
                  if (!selectedDomain) return;
                  try {
                    const response = await fetch(
                      `http://localhost:8080/scraper/data?domain=${encodeURIComponent(
                        selectedDomain
                      )}`
                    );
                    if (response.ok) {
                      const data = await response.json();
                      setScrapedProducts(data);
                      setDomain(selectedDomain);
                      setNotice(`Loaded ${data.length} products from ${selectedDomain}`);
                    }
                  } catch {
                    // ignore
                  }
                }}
                disabled={!selectedDomain}
                className="h-9 px-3 rounded-lg bg-gray-900 text-white disabled:opacity-50 text-sm"
                title="Load scraped products for the selected domain"
              >
                Load scraped products
              </button>
              <button
                onClick={async () => {
                  if (!selectedDomain) return;

                  // Compute offset based on current state and selected domain
                  const nextOffset =
                    selectedDomain === domain ? collections.length : 0;
                  const shouldReset =
                    selectedDomain !== domain || nextOffset === 0;

                  // Explicit debug entry for click context with effective offset
                  addDebug({
                    id: Math.random().toString(36).slice(2),
                    time: new Date().toISOString(),
                    action: "ui_click_fetch_25_collections",
                    request: {
                      url: `http://localhost:8080/scraper/collections?domain=${encodeURIComponent(
                        selectedDomain
                      )}&limit=${PAGE_SIZE}&offset=${shouldReset ? 0 : nextOffset}&fast=1&debug=1`,
                      method: "GET",
                      headers: { Accept: "application/json" },
                    },
                    response: {
                      status: 0,
                      statusText: "",
                      headers: {},
                      bodyPreview: "",
                      durationMs: 0,
                    },
                  });

                  await loadCollections(shouldReset, selectedDomain);
                }}
                disabled={!canFetchMore}
                aria-busy={loading ? "true" : "false"}
                className="h-9 px-3 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-sm disabled:opacity-50"
                title="Fetch 25 collections from the selected domain"
              >
                {nextFetchButtonLabel}
              </button>
              <button
                onClick={async () => {
                  if (!selectedDomain) return;
                  // Force refresh counts and persist to JSON; resets to page 0
                  addDebug({
                    id: Math.random().toString(36).slice(2),
                    time: new Date().toISOString(),
                    action: "ui_click_refresh_collections",
                    request: {
                      url: `http://localhost:8080/scraper/collections?domain=${encodeURIComponent(
                        selectedDomain
                      )}&limit=${PAGE_SIZE}&offset=0&fast=1&debug=1&refresh=1`,
                      method: "GET",
                      headers: { Accept: "application/json" },
                    },
                    response: { status: 0, statusText: "", headers: {}, bodyPreview: "", durationMs: 0 },
                  });
                  await loadCollections(true, selectedDomain, true);
                }}
                disabled={!selectedDomain || loading}
                className="h-9 px-3 rounded-lg bg-amber-100 hover:bg-amber-200 dark:bg-amber-900/40 dark:hover:bg-amber-800/40 text-amber-900 dark:text-amber-200 text-sm disabled:opacity-50"
                title="Recompute per-collection counts and persist to JSON (first page)"
              >
                Refresh collections
              </button>
            </div>

            <div className="space-y-1">
              <div className="text-sm font-medium">Fetch products</div>
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  min={1}
                  value={perCollectionLimit}
                  onChange={(e) =>
                    setPerCollectionLimit(Math.max(1, Number(e.target.value) || 1))
                  }
                  className="h-9 w-28 px-2 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors text-sm"
                  placeholder="per collection"
                  title="Maximum products to fetch per collection"
                />
                <button
                  onClick={startJob}
                  className="h-9 px-3 rounded-lg bg-gray-900 text-white disabled:opacity-50 text-sm"
                  title={`Fetch up to ${perCollectionLimit} products per selected collection`}
                >
                  Fetch products
                </button>
              </div>
              <div className="flex gap-2">
                <button
                  onClick={pauseJob}
                  className="h-9 px-3 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-sm"
                  title="Pause the active scrape job"
                >
                  Pause
                </button>
                <button
                  onClick={resumeJob}
                  className="h-9 px-3 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-sm"
                  title="Resume a paused scrape job"
                >
                  Resume
                </button>
                <button
                  onClick={cancelJob}
                  className="h-9 px-3 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 text-sm"
                  title="Cancel the active scrape job"
                >
                  Cancel
                </button>
              </div>
              <div className="text-xs text-gray-500">
                Will attempt to fetch up to {perCollectionLimit} products per selected collection.
              </div>
            </div>

            <div className="flex flex-col min-h-0">
              <div className="flex items-center justify-between mb-2">
                <div className="text-sm font-medium">Collections</div>
                <div className="text-xs text-gray-500">
                  {filteredCollections.length} shown • fetched {collections.length}
                  {totalCollections != null ? ` of ${totalCollections}` : ""}{totalCollections != null && collections.length >= totalCollections ? " • complete" : ""}
                </div>
              </div>
              <input
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="w-full h-9 px-3 rounded-lg bg-gray-100 hover:bg-gray-200 dark:bg-gray-700/50 dark:hover:bg-gray-600/50 transition-colors text-sm mb-2"
                placeholder="Search collections…"
              />
              <div className="flex-1 min-h-0 space-y-2 overflow-y-auto pr-1 pb-6">
                {filteredCollections.map((c) => (
                  <div key={c.slug} className="flex items-center gap-3">
                    <input
                      id={`col_${c.slug}`}
                      type="checkbox"
                      checked={!!selected[c.slug]?.selected}
                      onChange={(e) =>
                        setSelected((s) => ({
                          ...s,
                          [c.slug]: { ...s[c.slug], selected: e.target.checked },
                        }))
                      }
                      className="h-4 w-4"
                    />
                    <label htmlFor={`col_${c.slug}`} className="flex-1 cursor-pointer">
                      <div className="text-sm font-medium text-gray-800 dark:text-gray-200">
                        {c.title}
                      </div>
                      <div className="text-xs text-gray-500">
                        /{c.slug}
                        {(c.scraped_count != null || c.available_count != null || c.count != null) ? (
                          <> • {c.scraped_count ?? 0}/{c.available_count ?? c.count ?? 0} scraped</>
                        ) : null}
                      </div>
                    </label>
                  </div>
                ))}
                {collections.length === 0 && (
                  <div className="text-xs text-gray-500">Load a domain to see collections.</div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right pane - Product Preview */}
        <div className="flex-1 flex flex-col bg-white dark:bg-gray-900">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold">Scraped Products Preview</h2>
            <p className="text-sm text-gray-500 mt-1">
              View products that have been scraped and saved locally
            </p>
          </div>

          <div className="flex-1 overflow-auto p-6">
            {scrapedProducts.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-medium mb-2">No Products Yet</h3>
                  <p className="text-sm">
                    Use the controls to add a domain, load scraped products, or fetch collections.
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {scrapedProducts.map((product) => (
                  <div
                    key={product.id}
                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
                  >
                    {product.images && product.images.length > 0 && product.images[0].src_url ? (
                      <div className="aspect-square bg-gray-100 dark:bg-gray-700">
                        <img
                          src={product.images[0].src_url}
                          alt={product.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            (e.currentTarget as HTMLImageElement).style.display = "none";
                          }}
                        />
                      </div>
                    ) : (
                      <div className="aspect-square bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
                        <div className="text-center text-gray-400">
                          <div className="text-2xl mb-1">📷</div>
                          <div className="text-xs">No image</div>
                        </div>
                      </div>
                    )}
                    <div className="p-4">
                      <h3 className="font-medium text-sm line-clamp-2 mb-2">
                        {product.title}
                      </h3>
                      <div className="text-xs text-gray-500 space-y-1">
                        <div>Handle: {product.handle}</div>
                        <div>Domain: {product.domain}</div>
                        <div>Scraped: {new Date(product.scraped_at).toLocaleDateString()}</div>
                        <div>{product.images.length} images</div>
                        <div>{product.variants.length} variants</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default FetchPage;