const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080';

async function doFetch(path: string, init?: RequestInit) {
  const res = await fetch(`${BASE_URL}${path}`, {
    ...init,
    headers: {
      'Content-Type': 'application/json',
      'X-Env': 'dev',
      ...(init?.headers || {}),
    },
  });
  if (!res.ok) {
    const text = await res.text();
    throw new Error(`HTTP ${res.status}: ${text}`);
  }
  const contentType = res.headers.get('content-type') || '';
  return contentType.includes('application/json') ? res.json() : res.text();
}

export async function listProducts(limit?: number, cursor?: { updated_at: string; stable_id: string }) {
  const params = new URLSearchParams();
  if (limit) params.set('limit', String(limit));
  if (cursor) params.set('cursor', `${cursor.updated_at}|${cursor.stable_id}`);
  return doFetch(`/catalog/products?${params.toString()}`);
}

export async function listVariants(product_id: string) {
  return doFetch(`/catalog/products/${product_id}/variants`);
}

export async function generate(
  body: {
    workspace_id: string;
    mode: 'image' | 'video';
    aspect_ratio: string;
    quality: string;
    model: string;
    items: Array<{ product_id: string; variant_id?: string | null; prompt: string; params?: Record<string, unknown> }>;
  },
  idemKey?: string
) {
  return doFetch(`/generate`, {
    method: 'POST',
    body: JSON.stringify(body),
    headers: {
      ...(idemKey ? { 'Idempotency-Key': idemKey } : {}),
    },
  });
}

export async function getBatch(batch_id: string) {
  return doFetch(`/batches/${batch_id}`);
}

