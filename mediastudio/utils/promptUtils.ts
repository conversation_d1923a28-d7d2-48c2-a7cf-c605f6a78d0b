import type { PromptWithImages, PromptSegment, AttachedImage } from "../types";

// Create a new empty prompt
export function createEmptyPrompt(): PromptWithImages {
  return {
    segments: [{ type: "text", content: "" }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text" ? segment.content : `@${segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

// Create a prompt from plain text
export function createPromptFromText(text: string): PromptWithImages {
  return {
    segments: [{ type: "text", content: text }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text" ? segment.content : `@${segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

// Insert an @mention at a specific position in the text
export function insertMentionAtPosition(
  prompt: PromptWithImages,
  position: number,
  image: AttachedImage
): PromptWithImages {
  const newSegments: PromptSegment[] = [];
  let currentPos = 0;
  let inserted = false;

  for (const segment of prompt.segments) {
    if (segment.type === "text") {
      const segmentLength = segment.content.length;

      if (!inserted && currentPos + segmentLength >= position) {
        // Split this text segment to insert the mention
        const splitPos = position - currentPos;
        const beforeText = segment.content.slice(0, splitPos);
        const afterText = segment.content.slice(splitPos);

        if (beforeText) {
          newSegments.push({ type: "text", content: beforeText });
        }

        newSegments.push({
          type: "mention",
          content: image.displayName || image.filename,
          image,
        });

        if (afterText) {
          newSegments.push({ type: "text", content: afterText });
        }

        inserted = true;
      } else {
        newSegments.push(segment);
      }

      currentPos += segmentLength;
    } else {
      // Mention segment - count as length of @filename
      newSegments.push(segment);
      currentPos += segment.content.length + 1; // +1 for @
    }
  }

  // If we haven't inserted yet, append at the end
  if (!inserted) {
    // Add a space before the mention if the last segment doesn't end with whitespace
    const lastSegment = newSegments[newSegments.length - 1];
    if (
      lastSegment &&
      lastSegment.type === "text" &&
      lastSegment.content &&
      !lastSegment.content.endsWith(" ")
    ) {
      lastSegment.content += " ";
    }

    newSegments.push({
      type: "mention",
      content: image.displayName || image.filename,
      image,
    });
  }

  return {
    segments: newSegments,
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text" ? segment.content : `@${segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

// Remove a mention by image asset ID
export function removeMention(
  prompt: PromptWithImages,
  assetId: string
): PromptWithImages {
  // Safety check
  if (!prompt || !prompt.segments || !assetId) {
    return prompt || createEmptyPrompt();
  }

  const newSegments: PromptSegment[] = [];

  for (let i = 0; i < prompt.segments.length; i++) {
    const segment = prompt.segments[i];

    if (segment.type === "mention" && segment.image?.assetId === assetId) {
      // Skip this mention segment
      continue;
    }

    newSegments.push(segment);
  }

  // Merge adjacent text segments
  const mergedSegments: PromptSegment[] = [];
  for (const segment of newSegments) {
    const lastSegment = mergedSegments[mergedSegments.length - 1];

    if (segment.type === "text" && lastSegment && lastSegment.type === "text") {
      lastSegment.content += segment.content;
    } else {
      mergedSegments.push(segment);
    }
  }

  return {
    segments:
      mergedSegments.length > 0
        ? mergedSegments
        : [{ type: "text", content: "" }],
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text" ? segment.content : `@${segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}

// Update text content while preserving mentions
export function updatePromptText(
  prompt: PromptWithImages,
  newText: string,
  cursorPosition: number
): PromptWithImages {
  // For now, we'll implement a simple approach that tries to preserve mentions
  // This is a complex problem that would ideally use a more sophisticated diff algorithm

  // If the new text contains @mentions in the same positions, try to preserve them
  const mentions = prompt.getAttachedImages();
  const segments: PromptSegment[] = [];

  // Simple implementation: if text contains @filename patterns, convert them back to mentions
  let remainingText = newText;
  let currentPos = 0;

  for (const mention of mentions) {
    const mentionPattern = `@${mention.filename}`;
    const mentionIndex = remainingText.indexOf(mentionPattern);

    if (mentionIndex !== -1) {
      // Add text before mention
      if (mentionIndex > 0) {
        segments.push({
          type: "text",
          content: remainingText.slice(0, mentionIndex),
        });
      }

      // Add mention
      segments.push({
        type: "mention",
        content: mention.filename,
        image: mention,
      });

      // Continue with remaining text
      remainingText = remainingText.slice(mentionIndex + mentionPattern.length);
    }
  }

  // Add any remaining text
  if (remainingText) {
    segments.push({ type: "text", content: remainingText });
  }

  // If no segments were created, create a text segment with the full text
  if (segments.length === 0) {
    segments.push({ type: "text", content: newText });
  }

  return {
    segments,
    getText() {
      return this.segments
        .map((segment) =>
          segment.type === "text" ? segment.content : `@${segment.content}`
        )
        .join("");
    },
    getAttachedImages() {
      return this.segments
        .filter((segment) => segment.type === "mention" && segment.image)
        .map((segment) => segment.image!);
    },
  };
}
