import { describe, it, expect } from "vitest";
import { generateDisplayName } from "./assetNaming";
import type { Asset, Product } from "../types";

describe("assetNaming", () => {
  describe("generateDisplayName", () => {
    it("should generate display name for source assets with product context", () => {
      const product: Product = {
        id: "prod1",
        title: "1155 Extra High Waist Leggings in Schwarz",
        variants: [],
        assets: [],
      };

      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/img-8155.jpg",
        type: "image",
        filename: "img-8155.jpg",
      };

      const displayName = generateDisplayName(asset, product, 30); // Use longer maxLength
      expect(displayName).toBe("1155 Extra High Waist_8155");
    });

    it("should truncate long display names", () => {
      const product: Product = {
        id: "prod1",
        title: "Very Long Product Name That Should Be Truncated",
        variants: [],
        assets: [],
      };

      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/very-long-filename-that-should-be-truncated.jpg",
        type: "image",
        filename: "very-long-filename-that-should-be-truncated.jpg",
      };

      const displayName = generateDisplayName(asset, product, 15);
      expect(displayName.length).toBeLessThanOrEqual(15);
      expect(displayName).toMatch(/\.\.$/); // Should end with '..'
    });

    it("should generate display name for generated assets using prompt", () => {
      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/generated.jpg",
        type: "image",
        filename: "generated.jpg",
        prompt: "A professional product shot of leggings on white background",
      };

      const displayName = generateDisplayName(asset, undefined, 30); // Use longer maxLength
      expect(displayName).toBe("Product Leggings (Gen)");
    });

    it("should handle assets without product context", () => {
      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/img-8155.jpg",
        type: "image",
        filename: "img-8155.jpg",
      };

      const displayName = generateDisplayName(asset);
      expect(displayName).toBe("8155");
    });

    it("should handle video assets", () => {
      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/video.mp4",
        type: "video",
        filename: "generated.mp4",
        prompt: "A spinning product video",
      };

      const displayName = generateDisplayName(asset, undefined, 30); // Use longer maxLength
      expect(displayName).toBe("Spinning Product Video (Gen)");
    });

    it("should clean up filenames properly", () => {
      const asset: Asset = {
        id: "asset1",
        productId: "prod1",
        url: "https://example.com/img-test_file-123.jpg",
        type: "image",
        filename: "img-test_file-123.jpg",
      };

      const displayName = generateDisplayName(asset);
      expect(displayName).toBe("test file 123");
    });
  });
});
