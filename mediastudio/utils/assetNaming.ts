import type { Asset, Product } from "../types";

/**
 * Generate a user-friendly display name for an asset
 * @param asset The asset to generate a name for
 * @param product The product this asset belongs to (for context)
 * @param maxLength Maximum length for the display name (default: 20)
 * @returns User-friendly display name
 */
export function generateDisplayName(
  asset: Asset, 
  product?: Product, 
  maxLength: number = 20
): string {
  // For generated assets: Use prompt-based naming
  if (asset.prompt) {
    const summary = summarizePrompt(asset.prompt);
    return truncateWithEllipsis(`${summary} (Gen)`, maxLength);
  }
  
  // For source assets: "Product Name_filename"
  if (product) {
    const productName = truncateProductName(product.title);
    const filename = cleanFilename(asset.filename);
    const combined = `${productName}_${filename}`;
    return truncateWithEllipsis(combined, maxLength);
  }
  
  // Fallback
  return truncateWithEllipsis(cleanFilename(asset.filename), maxLength);
}

/**
 * Truncate product name to keep meaningful parts
 * Example: "1155 Extra High Waist Leggings in Schwarz" -> "1155 High Waist Leggings"
 */
function truncateProductName(title: string): string {
  const words = title.split(' ');
  if (words.length <= 4) return title;
  
  // Keep first word (usually product code) + next 2-3 meaningful words
  const meaningful = words.filter(w => 
    !['in', 'with', 'and', 'the', 'of'].includes(w.toLowerCase())
  );
  return meaningful.slice(0, 4).join(' ');
}

/**
 * Clean up filename for display
 * Example: "img-8155.jpg" -> "8155"
 */
function cleanFilename(filename: string): string {
  return filename
    .replace(/\.(jpg|jpeg|png|mp4|webp)$/i, '') // Remove extension
    .replace(/[-_]/g, ' ') // Replace dashes/underscores with spaces
    .replace(/^img-?/i, '') // Remove "img-" prefix
    .trim();
}

/**
 * Summarize a generation prompt into 2-3 key words
 * Example: "A professional product shot of leggings on white background" -> "Product Leggings White"
 */
function summarizePrompt(prompt: string): string {
  // Extract key descriptive words
  const words = prompt
    .toLowerCase()
    .replace(/[^\w\s]/g, ' ') // Remove punctuation
    .split(/\s+/)
    .filter(w => w.length > 2); // Filter out short words
  
  // Remove common stop words
  const stopWords = [
    'the', 'and', 'with', 'for', 'professional', 'shot', 'image', 'photo',
    'high', 'quality', 'resolution', 'clean', 'white', 'background', 'studio'
  ];
  const meaningful = words.filter(w => !stopWords.includes(w));
  
  // Take first 2-3 meaningful words and capitalize
  return meaningful.slice(0, 3).map(w => 
    w.charAt(0).toUpperCase() + w.slice(1)
  ).join(' ');
}

/**
 * Truncate text with ellipsis if it exceeds maxLength
 */
function truncateWithEllipsis(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength - 2) + '..';
}

/**
 * Generate display name specifically for drag data
 * This is a convenience function for the drag/drop system
 */
export function generateDragDisplayName(
  asset: Asset,
  product?: Product
): string {
  return generateDisplayName(asset, product, 25); // Slightly longer for drag tooltips
}
