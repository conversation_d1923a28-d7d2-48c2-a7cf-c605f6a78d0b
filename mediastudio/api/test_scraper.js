// Simple test for Fashion Nova HTML scraping
async function testFashionNovaScraping() {
  console.log('Testing HTML scraping for Fashion Nova...');

  try {
    // Test the collection page URL
    const collectionUrl = 'https://www.fashionnova.com/collections/halloween';
    console.log(`Fetching: ${collectionUrl}`);

    const response = await fetch(collectionUrl, {
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0 Safari/537.36',
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'accept-language': 'en-US,en;q=0.9',
        'referer': 'https://www.fashionnova.com/'
      }
    });

    if (!response.ok) {
      console.error(`Failed to fetch: ${response.status} ${response.statusText}`);
      return;
    }

    const html = await response.text();
    console.log(`Fetched HTML length: ${html.length} characters`);

    // Test regex patterns
    const productLinkPatterns = [
      /href=["']\/products\/([^"'\?#]+)["']/gi,
      /href=["']([^"']*\/products\/[^"'\?#]+)["']/gi,
      /data-product-handle=["']([^"']+)["']/gi,
      /data-handle=["']([^"']+)["']/gi,
      /href=["']\/collections\/[^\/]+\/products\/([^"'\?#]+)["']/gi,
      /href=["']([^"']*\/collections\/[^\/]+\/products\/[^"'\?#]+)["']/gi,
      /\/products\/([^\/\?#"'\s]+)/gi,
      /"handle"\s*:\s*"([^"]+)"/gi,
      /"product_handle"\s*:\s*"([^"]+)"/gi,
    ];

    const handles = new Set();

    for (const pattern of productLinkPatterns) {
      let match;
      let patternMatches = 0;
      while ((match = pattern.exec(html)) !== null) {
        let handle = match[1];

        // Clean up the handle
        if (handle.includes('/products/')) {
          handle = handle.split('/products/')[1].split('/')[0].split('?')[0].split('#')[0];
        } else if (handle.includes('/collections/')) {
          const parts = handle.split('/products/');
          if (parts.length > 1) {
            handle = parts[1].split('/')[0].split('?')[0].split('#')[0];
          }
        }

        handle = handle.replace(/^[\/]+|[\/\?\#].*$/g, '');

        if (handle && handle.length > 2 && handle.length < 100 && /^[a-zA-Z0-9_-]+$/.test(handle)) {
          handles.add(handle);
          patternMatches++;
        }
      }
      if (patternMatches > 0) {
        console.log(`Pattern ${pattern.source} found ${patternMatches} matches`);
      }
    }

    console.log(`Total unique handles found: ${handles.size}`);
    console.log('Sample handles:', Array.from(handles).slice(0, 10));

  } catch (error) {
    console.error('Error:', error);
  }
}

testFashionNovaScraping();