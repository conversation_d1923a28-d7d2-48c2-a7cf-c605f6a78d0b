import { readdirSync, readFileSync } from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { query } from "./db";

async function waitForDb(timeoutMs = 60000) {
  const start = Date.now();
  // Try a simple query until it succeeds or timeout
  while (Date.now() - start < timeoutMs) {
    try {
      await query("SELECT 1");
      return;
    } catch (e) {
      await new Promise((r) => setTimeout(r, 1000));
    }
  }
  throw new Error("Database not reachable within timeout");
}

async function ensureMigrationsTable() {
  await query(`CREATE SCHEMA IF NOT EXISTS app;`);
  await query(
    `CREATE TABLE IF NOT EXISTS app.schema_migrations (id text primary key, applied_at timestamptz default now())`
  );
}

async function appliedMigrations(): Promise<Set<string>> {
  const res = await query<{ id: string }>(
    `SELECT id FROM app.schema_migrations`
  );
  return new Set(res.rows.map((r) => r.id));
}

async function applyMigration(id: string, sql: string) {
  console.log(`Applying migration ${id}...`);
  await query("BEGIN");
  try {
    await query(sql);
    await query("INSERT INTO app.schema_migrations (id) VALUES ($1)", [id]);
    await query("COMMIT");
    console.log(`Applied ${id}`);
  } catch (e) {
    await query("ROLLBACK");
    console.error(`Failed ${id}:`, e);
    process.exit(1);
  }
}

async function main() {
  // ESM-safe __dirname
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  const dir = path.resolve(__dirname, "../db/migrations");

  await waitForDb();
  await ensureMigrationsTable();
  const done = await appliedMigrations();
  const files = readdirSync(dir)
    .filter((f) => f.endsWith(".sql"))
    .sort();
  for (const file of files) {
    if (done.has(file)) continue;
    const sql = readFileSync(path.join(dir, file), "utf8");
    await applyMigration(file, sql);
  }
  console.log("Migrations complete.");
}

main();
