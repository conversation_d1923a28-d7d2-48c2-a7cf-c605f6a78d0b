import Fastify from "fastify";
import dotenv from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import { randomUUID } from "crypto";
import { catalogRoutes } from "./routes/catalog";
import { generateRoutes } from "./routes/generate";
import { generateDirectRoutes } from "./routes/generateDirect";
import { scraperRoutes } from "./routes/scraper";
import { scrapedTestingRoutes } from "./routes/scraped-testing";
import { pingDb } from "./db";
import { logEvent, getEvents, clearEvents } from "./utils/debugLog";

// Try loading .env.local (repo root) first, then fallback to default .env
try {
  const __filename = fileURLToPath(import.meta.url);
  const __dirname = path.dirname(__filename);
  // repo root is two levels up from src/server.ts
  const rootEnvLocal = path.resolve(__dirname, "../../.env.local");
  dotenv.config({ path: rootEnvLocal });
} catch {}

dotenv.config();

const PORT = Number(process.env.PORT || 8080);
const NODE_ENV = process.env.NODE_ENV || "development";

// Increase body limit to allow reference images or larger params in generation requests
const app = Fastify({ logger: false, bodyLimit: 50 * 1024 * 1024 });

// Request timing + concise access log + request id
app.addHook("onRequest", async (req) => {
  // mark start time
  (req as any)._start = process.hrtime.bigint();
  // propagate or create request id
  const incoming =
    (req.headers["x-request-id"] as string | undefined) || undefined;
  const safe =
    incoming && /^[A-Za-z0-9._-]{1,64}$/.test(incoming) ? incoming : undefined;
  (req as any)._rid = safe || randomUUID();
  try {
    logEvent({
      source: "server",
      kind: "request",
      rid: (req as any)._rid,
      method: req.method,
      url: req.url,
      meta: {
        ip:
          (req.headers["x-forwarded-for"] as string) ||
          (req.socket as any)?.remoteAddress,
        ua: req.headers["user-agent"],
      },
    });
  } catch {}
});

app.addHook("onResponse", async (req, reply) => {
  const start = (req as any)._start as bigint | undefined;
  const rid = (req as any)._rid as string | undefined;
  const durMs = start
    ? Number((process.hrtime.bigint() - start) / 1000000n)
    : undefined;
  const len = reply.getHeader("content-length");
  const ct = reply.getHeader("content-type");
  const code = reply.statusCode;
  const msStr = typeof durMs === "number" ? `${durMs}ms` : "-";
  const lenStr =
    typeof len === "string" || typeof len === "number" ? String(len) : "-";
  const ctStr = typeof ct === "string" ? ct.split(";")[0] : "-";
  // ensure request id is visible to clients
  if (rid) reply.header("X-Request-Id", rid);
  const line = `[${new Date().toISOString()}] rid=${rid || "-"} ${req.method} ${
    req.url
  } -> ${code} ${msStr} ${lenStr} ${ctStr}`;
  if (code >= 500) {
    console.error(line);
  } else if (code >= 400) {
    console.warn(line);
  } else {
    console.log(line);
  }
  try {
    logEvent({
      source: "server",
      kind: "response",
      rid,
      method: req.method,
      url: req.url,
      status: code,
      durationMs: typeof durMs === "number" ? durMs : undefined,
      meta: { contentType: ctStr, length: lenStr },
    });
  } catch {}
});

// Minimal CORS for dev so the frontend at :5173 can call the API
if (NODE_ENV === "development") {
  app.options("*", async (req, reply) => {
    reply
      .header("Access-Control-Allow-Origin", "*")
      .header(
        "Access-Control-Allow-Methods",
        "GET,POST,PUT,PATCH,DELETE,OPTIONS"
      )
      .header("Access-Control-Allow-Headers", "Content-Type, X-Env")
      .code(204)
      .send();
  });
  app.addHook("onSend", async (req, reply) => {
    reply.header("Access-Control-Allow-Origin", "*");
    // Expose X-Request-Id so browser JS and DevTools can read it
    reply.header("Access-Control-Expose-Headers", "X-Request-Id");
  });
}

// Simple security header check (MVP) - disabled in development
app.addHook("onRequest", async (req, reply) => {
  if (NODE_ENV === "development") {
    return;
  }
  const hdr = req.headers["x-env"];
  if (hdr !== "dev") {
    reply.code(401).send({
      error: {
        code: "unauthorized",
        message: "missing or invalid X-Env header",
      },
    });
  }
});

// Centralized error handler with clean logging
app.setErrorHandler(async (err, req, reply) => {
  const code = (err as any)?.statusCode || 500;
  const rid = (req as any)?._rid as string | undefined;
  const line = `[${new Date().toISOString()}] ERROR rid=${rid || "-"} ${
    req.method
  } ${req.url} -> ${code} ${err.message}`;
  console.error(line);
  if (NODE_ENV !== "production" && err.stack) {
    console.error(err.stack);
  }
  // Ensure JSON error shape
  if (!reply.sent) {
    reply.code(code).send({
      error: {
        code: "internal_error",
        message: err.message,
        request_id: rid,
      },
    });
  }
});

app.get("/health", async () => {
  const dbOk = await pingDb();
  return { api: "ok", db: dbOk ? "ok" : "down" } as const;
});

// Dev-only debug env echo
if (NODE_ENV === "development") {
  app.get("/debug/env", async () => {
    return {
      VEO_ENABLE_REAL: process.env.VEO_ENABLE_REAL || null,
      HAS_GEMINI_API_KEY: Boolean(process.env.GEMINI_API_KEY),
    } as const;
  });
  // Dev-only debug logs endpoint
  app.get("/__debug/logs", async () => ({ events: getEvents() }));
  app.delete("/__debug/logs", async () => {
    clearEvents();
    return { ok: true } as const;
  });
}

app.register(catalogRoutes);
app.register(generateRoutes);
app.register(generateDirectRoutes);
app.register(scraperRoutes);
app.register(scrapedTestingRoutes);

app
  .listen({ port: PORT, host: "0.0.0.0" })
  .then(() => {
    console.log(`API listening on http://localhost:${PORT} (${NODE_ENV})`);
  })
  .catch((err) => {
    console.error(err);
    process.exit(1);
  });
