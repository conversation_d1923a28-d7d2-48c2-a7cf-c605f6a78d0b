import { z } from "zod";
import { mode, aspectRatio, quality, uuid } from "./common";

export const generateItem = z.object({
  product_id: uuid,
  variant_id: z.string().uuid().nullable().optional(),
  prompt: z.string().min(1),
  params: z.record(z.any()).default({}),
});

export const generateBody = z.object({
  workspace_id: uuid,
  mode,
  aspect_ratio: aspectRatio,
  quality,
  model: z.string().min(1),
  items: z.array(generateItem).min(1),
});

export const generateResponse = z.object({
  batch_id: uuid,
  request_ids: z.array(uuid),
});

export const batchResponse = z.object({
  batch: z.object({
    id: uuid,
    status: z.string(),
    requested_count: z.number(),
    mode: z.string(),
    aspect_ratio: z.string(),
    quality: z.string(),
    model: z.string(),
    created_at: z.string(),
  }),
  requests: z.array(
    z.object({
      id: uuid,
      product_id: uuid,
      variant_id: z.string().uuid().nullable(),
      prompt: z.string(),
      status: z.string(),
      started_at: z.string().nullable(),
      finished_at: z.string().nullable(),
      error: z.string().nullable(),
    })
  ),
  assets: z.array(
    z.object({
      id: uuid,
      type: z.string(),
      file_uri: z.string(),
      preview_uri: z.string().nullable(),
      sha256: z.string(),
      width: z.number().nullable(),
      height: z.number().nullable(),
      duration_ms: z.number().nullable(),
      source_request_id: uuid,
    })
  ),
});

export const assetResponse = z.object({
  id: uuid,
  type: z.string(),
  file_uri: z.string(),
  preview_uri: z.string().nullable(),
  sha256: z.string(),
  width: z.number().nullable(),
  height: z.number().nullable(),
  duration_ms: z.number().nullable(),
  source_request_id: uuid,
  prompt: z.string().optional(), // Generation prompt for the asset
});
