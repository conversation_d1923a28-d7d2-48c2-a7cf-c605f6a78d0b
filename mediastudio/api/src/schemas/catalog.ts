import { z } from 'zod';

export const productsQuery = z.object({
  limit: z.coerce.number().int().min(1).max(200).default(50),
  cursor: z.string().optional(), // ISO date or UUID string
});

export const productSummary = z.object({
  product_id: z.string().uuid(),
  title: z.string(),
  handle: z.string(),
  status: z.string(),
  default_image_url: z.string(),
});

export const productsResponse = z.object({
  items: z.array(productSummary),
  next_cursor: z.string().nullable(),
});

export const variantsResponse = z.object({
  items: z.array(
    z.object({
      variant_id: z.string().uuid(),
      product_id: z.string().uuid(),
      sku: z.string(),
      price: z.string(), // numeric comes back as string
      option1: z.string().nullable(),
      option2: z.string().nullable(),
      option3: z.string().nullable(),
    })
  ),
});

