import { PoolClient } from "pg";

function ensureHttps(url: string): string {
  if (!url) return url;
  if (url.startsWith("//")) return "https:" + url;
  if (url.startsWith("http://")) return url.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(url)) return "https://" + url.replace(/^\/+/, "");
  return url;
}

function centsToNumber(price: any): number {
  if (price == null) return 0;
  const n = Number(price);
  if (!Number.isFinite(n)) return 0;
  if (n > 1000 && Number.isInteger(n)) return Math.round((n / 100) * 100) / 100;
  return Math.round(n * 100) / 100;
}

export async function upsertProductWithRelations(
  client: PoolClient,
  shopId: string,
  productJson: any
) {
  // Normalize fields from product.js shape
  const prodId = Number(productJson.id);
  const title = String(productJson.title || "");
  const handle = String(productJson.handle || "");
  const external_gid = `gid://shopify/Product/${prodId}`;
  const external_rest_id = prodId;

  // Product upsert
  const existing = await client.query(
    `SELECT id, default_image_id FROM app.products WHERE external_gid = $1`,
    [external_gid]
  );

  let productId: string;
  let productInserted = false;
  if (existing.rowCount > 0) {
    productId = (existing.rows[0] as any).id;
    await client.query(
      `UPDATE app.products
       SET shop_id = $2, handle = $3, title = $4, status = 'ACTIVE', raw_json = $5, updated_at = now()
       WHERE id = $1`,
      [productId, shopId, handle, title, productJson]
    );
  } else {
    const ins = await client.query(
      `INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
       VALUES ($1, $2, $3, $4, $5, 'ACTIVE', $6)
       RETURNING id`,
      [shopId, external_gid, external_rest_id, handle, title, productJson]
    );
    productId = (ins.rows[0] as any).id;
    productInserted = true;
  }

  // Variants upsert
  let variantsInserted = 0;
  let variantsUpdated = 0;
  const variants: any[] = Array.isArray(productJson.variants) ? productJson.variants : [];
  for (const v of variants) {
    const vId = Number(v.id);
    if (!Number.isFinite(vId)) continue;
    const v_gid = `gid://shopify/ProductVariant/${vId}`;
    const sku = (v.sku && String(v.sku).trim()) || null;
    const priceNum = centsToNumber(v.price);

    const exV = await client.query(`SELECT id FROM app.variants WHERE external_gid = $1`, [v_gid]);
    if (exV.rowCount > 0) {
      await client.query(
        `UPDATE app.variants
         SET product_id = $2, sku = $3, price = $4, option1 = $5, option2 = $6, option3 = $7, raw_json = $8, updated_at = now()
         WHERE external_gid = $1`,
        [
          v_gid,
          productId,
          sku,
          priceNum,
          v.option1 ?? null,
          v.option2 ?? null,
          v.option3 ?? null,
          v,
        ]
      );
      variantsUpdated++;
    } else {
      await client.query(
        `INSERT INTO app.variants (product_id, external_gid, external_rest_id, sku, price, option1, option2, option3, raw_json)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          productId,
          v_gid,
          vId,
          sku,
          priceNum,
          v.option1 ?? null,
          v.option2 ?? null,
          v.option3 ?? null,
          v,
        ]
      );
      variantsInserted++;
    }
  }

  // Images upsert
  let imagesInserted = 0;
  let imagesUpdated = 0;
  const images: any[] = Array.isArray(productJson.images) ? productJson.images : [];
  let defaultImageId = existing.rows?.[0]?.default_image_id || null;

  const normalizedImages = images
    .map((img: any) => {
      if (typeof img === "string") return { src: ensureHttps(img) };
      return {
        id: img.id ?? null,
        src: ensureHttps(img.src || img.url || ""),
        width: img.width ?? null,
        height: img.height ?? null,
        alt: img.alt ?? null,
        raw: img,
      };
    })
    .filter((i: any) => i.src);

  const seen = new Set<string>();
  const uniqueImgs = normalizedImages.filter((i: any) => {
    if (seen.has(i.src)) return false;
    seen.add(i.src);
    return true;
  });

  for (const img of uniqueImgs) {
    const exI = await client.query(
      `SELECT id FROM app.product_images WHERE product_id = $1 AND src_url = $2`,
      [productId, img.src]
    );
    if (exI.rowCount > 0) {
      await client.query(
        `UPDATE app.product_images
         SET external_gid = $3, width = $4, height = $5, alt = $6, raw_json = $7, updated_at = now()
         WHERE product_id = $1 AND src_url = $2`,
        [
          productId,
          img.src,
          img.id ? `gid://shopify/ProductImage/${img.id}` : null,
          img.width ?? null,
          img.height ?? null,
          img.alt ?? null,
          img.raw ?? img,
        ]
      );
      imagesUpdated++;
    } else {
      const insI = await client.query(
        `INSERT INTO app.product_images (product_id, external_gid, src_url, width, height, alt, raw_json)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id`,
        [
          productId,
          img.id ? `gid://shopify/ProductImage/${img.id}` : null,
          img.src,
          img.width ?? null,
          img.height ?? null,
          img.alt ?? null,
          img.raw ?? img,
        ]
      );
      const newImgId = (insI.rows[0] as any).id;
      imagesInserted++;
      if (!defaultImageId) {
        await client.query(
          `UPDATE app.products SET default_image_id = $2 WHERE id = $1 AND default_image_id IS NULL`,
          [productId, newImgId]
        );
        defaultImageId = newImgId;
      }
    }
  }

  return {
    productInserted,
    variantsInserted,
    variantsUpdated,
    imagesInserted,
    imagesUpdated,
  };
}

