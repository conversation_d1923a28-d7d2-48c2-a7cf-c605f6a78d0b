import type { ImageSettings, VideoSettings } from "../../../types";

import { logEvent } from "../utils/debugLog";

// Use REST for native image generation with gemini-2.5-flash-image-preview
function getGeminiApiKey(): string {
  return process.env.GEMINI_API_KEY || "";
}
const GEMINI_IMAGE_MODEL = "gemini-2.5-flash-image-preview";
const GEMINI_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/${GEMINI_IMAGE_MODEL}:generateContent`;

async function fetchAsBase64(
  url: string
): Promise<{ base64: string; mimeType: string }> {
  // Support data URLs directly
  if (url.startsWith("data:")) {
    // e.g., data:image/png;base64,AAAA...
    const m = url.match(/^data:([^;]+);base64,(.*)$/);
    if (!m) throw new Error("Invalid data URL");
    const mimeType = m[1] || "image/png";
    const base64 = m[2] || "";
    return { base64, mimeType };
  }
  const res = await fetch(url);
  if (!res.ok)
    throw new Error(`Failed to fetch reference image: ${res.status}`);
  const mimeType = res.headers.get("content-type") || "image/jpeg";
  const ab = await res.arrayBuffer();
  const buf = Buffer.from(ab);
  const base64 = buf.toString("base64");
  return { base64, mimeType };
}

export const generateImage = async (
  prompt: string,
  settings: ImageSettings,
  referenceImageUrls?: string[]
): Promise<{ url: string }> => {
  try {
    const API_KEY = getGeminiApiKey();
    if (!API_KEY) throw new Error("GEMINI_API_KEY is not set");

    // Build contents parts: text + optional inline images
    const parts: any[] = [];

    // Encourage target aspect/size in prompt (model doesn't take explicit size params)
    const aspect = settings.aspectRatio || "1:1";
    const size = settings.size || "1024x1024";

    const promptText = [
      prompt,
      `\nOutput requirements:`,
      `- Aspect ratio: ${aspect}`,
      `- Output size: ${size}`,
      `- Style: Professional product photography, clean ecommerce composition`,
    ].join(" ");

    parts.push({ text: promptText });

    const urls = Array.isArray(referenceImageUrls)
      ? referenceImageUrls.slice(0, 4) // be conservative
      : [];
    if (urls.length > 0) {
      try {
        const fetched = await Promise.all(
          urls.map(async (u) => {
            const { base64, mimeType } = await fetchAsBase64(u);
            return { mimeType, base64 };
          })
        );
        for (const f of fetched) {
          parts.push({
            inline_data: { mime_type: f.mimeType, data: f.base64 },
          });
        }
      } catch (e) {
        console.warn("One or more reference images failed to load:", e);
      }
    }

    const body = { contents: [{ parts }] } as any;

    const resp = await fetch(
      GEMINI_ENDPOINT + `?key=${encodeURIComponent(API_KEY)}`,
      {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      }
    );

    if (!resp.ok) {
      const text = await resp.text();
      throw new Error(`Gemini API error ${resp.status}: ${text}`);
    }

    const data: any = await resp.json();
    const candidates = data?.candidates || [];
    if (!candidates.length)
      throw new Error("No candidates returned from Gemini");
    const partsOut = candidates[0]?.content?.parts || [];

    // Look for inline data (snake_case or camelCase depending on client)
    let inline: { data?: string; mime_type?: string } | undefined;
    for (const p of partsOut) {
      if (p?.inline_data?.data) {
        inline = p.inline_data;
        break;
      }
      if (p?.inlineData?.data) {
        inline = {
          data: p.inlineData.data,
          mime_type: p.inlineData.mimeType,
        } as any;
        break;
      }
    }

    if (!inline?.data)
      throw new Error("Gemini did not return inline image data");
    const mime = inline.mime_type || "image/png";
    const dataUrl = `data:${mime};base64,${inline.data}`;

    return { url: dataUrl };
  } catch (error) {
    console.error("Error generating image:", error);
    throw new Error(
      error instanceof Error ? error.message : "Failed to generate image"
    );
  }
};

export const generateVideo = async (
  prompt: string,
  settings: VideoSettings,
  referenceImageUrl?: string,
  modelId?: string
): Promise<{
  url: string;
  preview_url?: string;
  width?: number;
  height?: number;
  duration_ms?: number;
}> => {
  try {
    console.log(
      "Generating video with prompt:",
      prompt,
      "settings:",
      settings,
      "reference:",
      referenceImageUrl,
      "model:",
      modelId
    );

    // Default to mock implementation unless explicitly enabled for real calls
    const REAL_VIDEO_ENABLED = process.env.VEO_ENABLE_REAL === "1";
    console.log("Video generation mode:", REAL_VIDEO_ENABLED ? "REAL" : "MOCK");

    // Compute simple dimensions from settings
    const isPortrait = (settings.aspectRatio || "16:9") === "9:16";
    const res = (settings.resolution || "720p").toLowerCase();
    const base = res === "1080p" ? 1080 : 720;
    const width = isPortrait
      ? (base === 1080 ? 608 : 405) * 2
      : base === 1080
      ? 1920
      : 1280; // conservative defaults
    const height = isPortrait ? (base === 1080 ? 1920 : 1280) : base;
    const duration_ms = Math.max(1, Math.min(8, settings.duration || 5)) * 1000;

    if (!REAL_VIDEO_ENABLED) {
      // Mock path: return a tiny public sample and a 1x1 PNG as poster to avoid <img> errors
      const poster1x1 =
        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAc8AAnkB2nJxAQAAAABJRU5ErkJggg==";
      const sample5s = "https://samplelib.com/lib/preview/mp4/sample-5s.mp4";
      return {
        url: sample5s,
        preview_url: poster1x1,
        width,
        height,
        duration_ms,
      };
    }

    // Real path: Use Gemini API (Generative Language v1beta) generateContent
    const API_KEY = getGeminiApiKey();
    if (!API_KEY) throw new Error("GEMINI_API_KEY is not set");

    // Choose model conservatively
    const model =
      modelId && modelId.trim().length > 0 ? modelId : "veo-2.0-generate-001";
    const BASE_URL = "https://generativelanguage.googleapis.com/v1beta";

    // Build contents parts: text + optional inline image (Gemini API format)
    const parts: any[] = [];
    let imageMimeForLog: string | undefined;

    // Add text prompt part
    parts.push({ text: prompt });

    // Add image part if provided
    if (referenceImageUrl && typeof referenceImageUrl === "string") {
      logEvent({
        source: "server",
        kind: "response",
        note: `Processing reference image URL: ${referenceImageUrl.substring(
          0,
          100
        )}...`,
      });

      try {
        let ref = referenceImageUrl.trim();
        // Resolve relative URLs (e.g., "/assets/:id/stream") to absolute
        if (ref.startsWith("/")) {
          const origin =
            process.env.PUBLIC_ORIGIN ||
            process.env.BACKEND_ORIGIN ||
            "http://localhost:8080";
          ref = `${origin}${ref}`;
        }

        if (ref.startsWith("http://") || ref.startsWith("https://")) {
          // Fetch and convert to base64 (Gemini format)
          const { base64, mimeType } = await fetchAsBase64(ref);
          const mt = (mimeType || "").toLowerCase();
          // Veo accepts only JPEG/PNG for reference image; reject others clearly
          const normalized = mt === "image/jpg" ? "image/jpeg" : mt;
          if (normalized !== "image/jpeg" && normalized !== "image/png") {
            throw new Error(
              `Reference image MIME type '${mimeType}' not supported by Veo. Use JPEG or PNG. URL=${referenceImageUrl}`
            );
          }
          imageMimeForLog = normalized;
          // Use Gemini API format for inline image data
          parts.push({
            inline_data: { 
              mime_type: normalized, 
              data: base64 
            }
          });
        } else if (ref.startsWith("data:")) {
          const m = ref.match(/^data:([^;]+);base64,(.*)$/);
          if (m) {
            let mimeType = (m[1] || "image/png").toLowerCase();
            mimeType = mimeType === "image/jpg" ? "image/jpeg" : mimeType;
            if (mimeType !== "image/jpeg" && mimeType !== "image/png") {
              throw new Error(
                `Reference image MIME type '${m[1]}' not supported by Veo. Use JPEG or PNG.`
              );
            }
            const base64 = m[2] || "";
            imageMimeForLog = mimeType;
            // Use Gemini API format for inline image data
            parts.push({
              inline_data: { 
                mime_type: mimeType, 
                data: base64 
              }
            });
          }
        }

        if (parts.length > 1) {
          logEvent({
            source: "server",
            kind: "response",
            note: `Successfully processed reference image, MIME: ${imageMimeForLog}`,
          });
        }
      } catch (e) {
        console.warn(
          "Failed to include reference image; proceeding without it",
          e
        );
        // If a reference image was provided but not included, fail fast so the UI surfaces the error
        const msg = e instanceof Error ? e.message : String(e);
        throw new Error(msg || "Failed to include reference image");
      }
    }

    // Build instances with prompt and optional image (corrected format)
    let imagePart: any = undefined;
    if (parts.length > 1) {
      // Convert the parts-based image format to instance format
      const imageParts = parts.filter(p => p.inline_data);
      if (imageParts.length > 0) {
        imagePart = imageParts[0]; // Take first image
      }
    }

    const instances: any[] = [
      {
        prompt,
        ...(imagePart || {}),
      },
    ];

    // Build parameters with safe coercions (nested in 'parameters' object)
    const params: Record<string, any> = {};
    const ar = settings.aspectRatio || "16:9";
    params.aspectRatio = ar;

    // Person generation policy
    const personGenFromSettings = (settings as any)?.personGeneration as
      | "allow_adult"
      | "dont_allow"
      | "disallow"
      | undefined;
    // Be conservative: omit by default. Only include allow_adult for non-preview Veo 3 when explicitly requested.
    if (
      personGenFromSettings === "allow_adult" &&
      model.startsWith("veo-3.0") &&
      !/preview/i.test(model)
    ) {
      params.personGeneration = "allow_adult";
    }
    // For Veo 2.x and Veo 3 preview: omit personGeneration entirely

    // Resolution handling
    const isV3 = model.startsWith("veo-3.0");
    const isPreview = /preview/i.test(model);
    if (isV3 && !isPreview) {
      if (ar === "9:16") {
        params.resolution = "720p"; // safe default; 1080p vertical typically unsupported
      } else {
        const reqRes = String(settings.resolution || "720p").toLowerCase();
        params.resolution = reqRes === "1080p" ? "1080p" : "720p";
      }
    } else if (model.startsWith("veo-2.0")) {
      // Veo 2 does not accept a resolution parameter; omit it entirely
    } else {
      // Veo 3 preview endpoints don't accept 'resolution' - omit it
    }

    if ((settings as any).negativePrompt)
      params.negativePrompt = (settings as any).negativePrompt;

    // Duration: Only Veo 2.x supports durationSeconds; omit for Veo 3.x
    if (typeof (settings as any).duration === "number") {
      const d = Math.floor((settings as any).duration);
      if (Number.isFinite(d) && model.startsWith("veo-2.0")) {
        params.durationSeconds = Math.max(1, Math.min(60, d));
      }
    }

    // Debug trace: verify prompt and image/reference are included
    try {
      console.info("[gemini] generateVideo", {
        model,
        promptPreview: typeof prompt === "string" ? prompt.slice(0, 120) : "",
        hasImage: !!imagePart,
        imageMime: imageMimeForLog,
        params,
        instancesStructure: JSON.stringify(instances, null, 2).slice(0, 500),
      });
    } catch {}
    
    try {
      logEvent({
        source: "external",
        kind: "external-request",
        method: "POST",
        url: `${BASE_URL}/models/${encodeURIComponent(
          model
        )}:predictLongRunning`,
        note: "veo start",
        meta: { model, hasImage: !!imagePart, params },
      });
    } catch {}

    const startOnce = async (inst: any[]) => {
      return await fetch(
        `${BASE_URL}/models/${encodeURIComponent(model)}:predictLongRunning`,
        {
          method: "POST",
          headers: {
            "x-goog-api-key": API_KEY,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ instances: inst, parameters: params }),
        }
      );
    };

    let startResp = await startOnce(instances);
    if (!startResp.ok) {
      let txt = await startResp.text();

      // Retry path 1: resolution not supported by this model -> drop it and retry
      const resUnsupported =
        startResp.status === 400 &&
        /(isn'?t|not)\s+supported/i.test(txt) &&
        /resolution/i.test(txt);
      if (resUnsupported) {
        console.warn(
          "Veo model rejected 'resolution'; retrying without resolution param"
        );
        try {
          delete (params as any).resolution;
        } catch {}
        const retryResp1 = await startOnce(instances);
        if (retryResp1.ok) {
          startResp = retryResp1;
        } else {
          txt = await retryResp1.text();
          // fall through to next retry check using updated txt
          startResp = retryResp1;
        }

        // Retry path 1b: durationSeconds not supported by this model -> drop it and retry
        const durUnsupported =
          startResp.status === 400 &&
          /(isn'?t|not)\s+supported/i.test(txt) &&
          /durationSeconds/i.test(txt);
        if (durUnsupported) {
          console.warn(
            "Veo model rejected 'durationSeconds'; retrying without durationSeconds param"
          );
          try {
            delete (params as any).durationSeconds;
          } catch {}
          const retryResp1b = await startOnce(instances);
          if (retryResp1b.ok) {
            startResp = retryResp1b;
          } else {
            txt = await retryResp1b.text();
            // fall through to next retry check using updated txt
            startResp = retryResp1b;
          }
        }
      }

      if (!startResp.ok) {
        // Retry path 2: personGeneration/allow_adult not supported -> drop it and retry
        const txtNow = txt || (await startResp.text().catch(() => ""));
        const pgUnsupported =
          startResp.status === 400 &&
          /(isn'?t|not)\s+supported/i.test(txtNow) &&
          (/personGeneration/i.test(txtNow) || /allow_adult/i.test(txtNow));
        if (pgUnsupported) {
          console.warn(
            "Veo model rejected 'personGeneration'; retrying without personGeneration param"
          );
          try {
            delete (params as any).personGeneration;
          } catch {}
          const retryResp2a = await startOnce(instances);
          if (!retryResp2a.ok) {
            const txt2 = await retryResp2a.text();
            let msg2a = `Veo start error ${retryResp2a.status}: ${txt2}`;
            try {
              const j = JSON.parse(txt2);
              const m = j?.error?.message || j?.message;
              if (typeof m === "string" && m.trim()) msg2a = m.trim();
            } catch {}
            throw new Error(msg2a);
          }
          startResp = retryResp2a;
        } else {
          let msg = `Veo start error ${startResp.status}: ${txtNow}`;
          try {
            const j = JSON.parse(txtNow);
            const m = j?.error?.message || j?.message;
            if (typeof m === "string" && m.trim()) msg = m.trim();
          } catch {}
          throw new Error(msg);
        }
      }
    }

    try {
      logEvent({
        source: "external",
        kind: "external-response",
        method: "POST",
        url: `${BASE_URL}/models/${encodeURIComponent(
          model
        )}:predictLongRunning`,
        status: startResp.status,
        note: "veo start response",
      });
    } catch {}

    const op = (await startResp.json()) as any; // { name, done?, response? }
    const opName: string | undefined = op?.name;
    if (!opName) throw new Error("Missing operation name from Veo response");

    // Poll operation until done
    const maxPollMs = 5 * 60 * 1000; // 5 minutes cap
    const startTs = Date.now();
    let last: any = op;
    while (!last?.done) {
      if (Date.now() - startTs > maxPollMs)
        throw new Error("Veo operation timeout");
      await new Promise((r) =>
        setTimeout(
          r,
          Number(
            process.env.VIDEO_POLL_INTERVAL_MS ||
              (process.env.NODE_ENV === "test" ? 10 : 5000)
          )
        )
      );
      const opUrl = /^https?:\/\//.test(opName)
        ? opName
        : `${BASE_URL}/${opName}`; // do not encodeURIComponent, opName contains path segments
      const poll = await fetch(opUrl, {
        headers: { "x-goog-api-key": API_KEY },
      });
      if (!poll.ok) {
        const t = await poll.text();
        throw new Error(`Veo poll error ${poll.status}: ${t}`);
      }
      last = await poll.json();
    }

    // Extract video URI (robust against schema drift)
    const response: any = last?.response ?? last?.result ?? last;
    let videoUri: string | undefined;
    // Known structures from docs and SDKs (multiple variants observed)
    videoUri =
      response?.generateVideoResponse?.generatedSamples?.[0]?.video?.uri ||
      response?.generatedSamples?.[0]?.video?.uri ||
      response?.generatedVideos?.[0]?.video?.uri ||
      response?.generated_videos?.[0]?.video?.uri ||
      response?.videos?.[0]?.uri ||
      response?.outputs?.[0]?.uri ||
      response?.video?.uri ||
      response?.media?.[0]?.uri ||
      response?.assets?.[0]?.uri ||
      response?.generatedMedia?.[0]?.uri;

    if (!videoUri) {
      // Attempt inline video bytes (some responses return base64 instead of a URL)
      const inlineTry =
        response?.generateVideoResponse?.generatedSamples?.[0]?.video ||
        response?.generatedSamples?.[0]?.video ||
        response?.generatedVideos?.[0]?.video ||
        undefined;
      const inlineB64: string | undefined =
        inlineTry?.bytesBase64 ||
        inlineTry?.inlineData?.data ||
        inlineTry?.inline_data?.data;
      const inlineMime: string =
        inlineTry?.mimeType || inlineTry?.mime_type || "video/mp4";
      if (inlineB64 && inlineB64.length > 1000) {
        videoUri = `data:${inlineMime};base64,${inlineB64}`;
      }
    }

    if (!videoUri) {
      // Last-resort: deep search for a plausible video URL
      const uris: string[] = [];
      const seen = new Set<any>();
      const walk = (o: any) => {
        if (!o || typeof o !== "object" || seen.has(o)) return;
        seen.add(o);
        for (const [kRaw, v] of Object.entries(o)) {
          const k = String(kRaw).toLowerCase();
          if (v && typeof v === "object") {
            walk(v);
          } else if (
            typeof v === "string" &&
            (k.includes("uri") || k.includes("url")) &&
            /^(https?:)?\/\//.test(v)
          ) {
            uris.push(v);
          }
        }
      };
      try {
        // Prefer to search within generateVideoResponse if present
        if (response?.generateVideoResponse)
          walk(response.generateVideoResponse);
        walk(response);
      } catch {}
      // Prefer .mp4 or obvious video endpoints
      videoUri = uris.find((u) => /\.mp4(\?|$)/i.test(u)) || uris[0];
    }

    if (!videoUri) {
      // Dev aid: surface top-level keys in error to speed up debugging from UI
      let keysNote = "";
      try {
        const keys =
          response && typeof response === "object" ? Object.keys(response) : [];
        console.warn(
          "Veo done but no video URI found. Top-level response keys:",
          keys
        );
        if (Array.isArray(keys) && keys.length > 0) {
          keysNote = ` keys=[${keys.slice(0, 8).join(",")}...]`;
        }
      } catch {}
      throw new Error(`No video URI in Veo response${keysNote}`);
    }

    // Use 1x1 PNG as preview to avoid <img> errors; can be upgraded to a real poster later
    const poster1x1 =
      "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAc8AAnkB2nJxAQAAAABJRU5ErkJggg==";

    try {
      logEvent({
        source: "external",
        kind: "external-response",
        method: "GET",
        url: typeof opName === "string" ? opName : undefined,
        note: "veo done",
        meta: {
          hasUrl: Boolean(videoUri),
          urlSample:
            typeof videoUri === "string" ? videoUri.slice(0, 120) : undefined,
        },
      });
    } catch {}

    return {
      url: videoUri,
      preview_url: poster1x1,
      width,
      height,
      duration_ms,
    };
  } catch (error) {
    console.error("Error generating video:", error);
    try {
      const msg = error instanceof Error ? error.message : String(error);
      logEvent({
        source: "external",
        kind: "external-error",
        note: "veo pipeline error",
        meta: { message: msg },
      });
    } catch {}
    throw new Error(
      error instanceof Error ? error.message : "Failed to generate video"
    );
  }
};
