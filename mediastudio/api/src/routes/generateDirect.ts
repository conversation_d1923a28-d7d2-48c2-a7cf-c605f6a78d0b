import { FastifyInstance } from "fastify";
import { z } from "zod";
import { generateImage, generateVideo } from "../services/geminiService";
import { query, transaction } from "../db";
import { logEvent } from "../utils/debugLog.js";
import crypto from "crypto";
import { randomUUID } from "crypto";

const generateItemSchema = z.object({
  productId: z.string(),
  prompt: z.string().min(1),
  // Accept http(s) and data: URLs (frontend may pass inline data URIs)
  referenceImageUrls: z.array(z.string()).optional(),
});

const generateBatchSchema = z.object({
  items: z.array(generateItemSchema).min(1).max(5), // Max 5 products
  // Allow flexible settings for both image and video modes
  settings: z.record(z.any()),
  mode: z.enum(["image", "video"]),
  model: z.string(),
});

export async function generateDirectRoutes(app: FastifyInstance) {
  // POST /api/generate/batch
  app.post("/api/generate/batch", async (req, reply) => {
    try {
      const { items, settings, mode, model } = generateBatchSchema.parse(
        req.body
      );

      // Debug log the incoming batch request
      logEvent({
        source: "server",
        kind: "response",
        note: `Batch request received - Mode: ${mode}, Items: ${
          items.length
        }, First item has reference images: ${
          items[0]?.referenceImageUrls ? "YES" : "NO"
        }`,
      });

      // Create a batch record
      const batchId = randomUUID();
      const workspaceId = randomUUID(); // TODO: Get from auth context

      const { requestIds } = await transaction(async (client) => {
        // Insert batch
        await client.query(
          `INSERT INTO app.batches (id, workspace_id, mode, aspect_ratio, quality, model, requested_count, status)
           VALUES ($1, $2, $3, $4, $5, $6, $7, 'processing')`,
          [
            batchId,
            workspaceId,
            mode,
            settings.aspectRatio,
            settings.quality,
            model,
            items.length,
          ]
        );

        // Insert generation requests for each item
        const requestIds: string[] = [];
        for (const item of items) {
          const requestId = randomUUID();
          await client.query(
            `INSERT INTO app.generation_requests (id, batch_id, product_id, prompt, params_json, status)
             VALUES ($1, $2, $3, $4, $5, 'processing')`,
            [
              requestId,
              batchId,
              item.productId,
              item.prompt,
              JSON.stringify({
                referenceImageUrls: item.referenceImageUrls,
                settings,
              }),
            ]
          );
          requestIds.push(requestId);
        }

        return { requestIds };
      });

      // Start processing the batch asynchronously
      processBatch(batchId, items, settings, mode, requestIds, model).catch(
        console.error
      );

      const rid = (req as any)?._rid;
      return reply.send({
        success: true,
        batchId,
        request_id: rid,
        message: `Started processing ${items.length} ${mode}(s)`,
      });
    } catch (error) {
      console.error("Batch generation error:", error);
      return reply.code(500).send({
        success: false,
        error: "Failed to start batch generation",
      });
    }
  });

  // GET /api/generate/batch/:batchId/status
  app.get("/api/generate/batch/:batchId/status", async (req, reply) => {
    try {
      const { batchId } = req.params as { batchId: string };

      const batchResult = await query(
        `SELECT status, requested_count FROM app.batches WHERE id = $1`,
        [batchId]
      );

      if (batchResult.rowCount === 0) {
        return reply.code(404).send({ error: "Batch not found" });
      }

      const requestsResult = await query(
        `SELECT id, product_id, status, error, prompt FROM app.generation_requests WHERE batch_id = $1`,
        [batchId]
      );

      const completed = requestsResult.rows.filter(
        (r: any) => r.status === "completed"
      ).length;
      const failed = requestsResult.rows.filter(
        (r: any) => r.status === "failed"
      ).length;

      const rid = (req as any)?._rid;
      return reply.send({
        batchId,
        status: batchResult.rows[0].status,
        total: batchResult.rows[0].requested_count,
        completed,
        failed,
        requests: requestsResult.rows,
        request_id: rid,
      });
    } catch (error) {
      console.error("Status check error:", error);
      return reply.code(500).send({ error: "Failed to get batch status" });
    }
  });
}

// Process batch asynchronously
async function sleep(ms: number) {
  return new Promise((res) => setTimeout(res, ms));
}

function parseRetryDelaySeconds(errMsg: string): number | null {
  try {
    // Look for "retryDelay": "29s" in JSON fragment
    const m = errMsg.match(/"retryDelay"\s*:\s*"([^"]+)"/);
    if (m && m[1]) {
      const val = m[1].trim();
      if (val.endsWith("s"))
        return Math.max(1, parseInt(val.replace(/s$/, ""), 10));
      if (val.endsWith("ms"))
        return Math.max(
          1,
          Math.ceil(parseInt(val.replace(/ms$/, ""), 10) / 1000)
        );
    }
  } catch {}
  return null;
}

async function processBatch(
  batchId: string,
  items: Array<{
    productId: string;
    prompt: string;
    referenceImageUrls?: string[];
  }>,
  settings: any,
  mode: "image" | "video",
  requestIds: string[],
  model: string
) {
  try {
    for (const [index, item] of items.entries()) {
      const requestId = requestIds[index];

      try {
        // Update request status to processing
        await query(
          `UPDATE app.generation_requests SET status = 'processing', started_at = NOW() WHERE id = $1`,
          [requestId]
        );

        let result: { url: string } | undefined;
        const maxAttempts = 3;
        let attempt = 0;
        let lastErr: any = null;

        while (attempt < maxAttempts) {
          attempt++;
          try {
            if (mode === "image") {
              result = await generateImage(
                item.prompt,
                settings,
                item.referenceImageUrls
              );
            } else {
              const referenceImageUrl = (item as any).referenceImageUrls?.[0];
              logEvent({
                source: "server",
                kind: "response",
                note: `Calling generateVideo - Prompt: "${
                  item.prompt
                }", Reference URL: ${
                  referenceImageUrl
                    ? referenceImageUrl.substring(0, 50) + "..."
                    : "NONE"
                }`,
              });
              result = (await generateVideo(
                item.prompt,
                settings,
                referenceImageUrl,
                model
              )) as any;
            }
            break; // success
          } catch (e: any) {
            lastErr = e;
            const msg = String(e?.message || e);
            if (msg.includes("429")) {
              const retrySec = parseRetryDelaySeconds(msg) ?? 30;
              const backoff = retrySec * Math.pow(2, attempt - 1);
              console.warn(
                `Rate limited on attempt ${attempt}/${maxAttempts}. Retrying in ${backoff}s...`
              );
              await sleep(backoff * 1000);
              continue; // retry
            }
            // non-retryable
            throw e;
          }
        }

        if (!result) throw lastErr || new Error("Generation failed");

        // Store the generated asset (data URL or hosted URL) in app.assets
        const assetId = randomUUID();
        const workspaceId = randomUUID(); // TODO: Get from auth context

        await query(
          `INSERT INTO app.assets (id, workspace_id, type, file_uri, preview_uri, sha256, width, height, duration_ms, source_request_id)
           VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)`,
          [
            assetId,
            workspaceId,
            mode,
            (result as any).url,
            mode === "video" ? (result as any).preview_url ?? null : null,
            // Include requestId in hash to avoid unique sha collisions across identical outputs
            crypto
              .createHash("sha256")
              .update(String((result as any).url || "") + "|" + requestId)
              .digest("hex"),
            mode === "video" ? (result as any).width ?? null : null,
            mode === "video" ? (result as any).height ?? null : null,
            mode === "video" ? (result as any).duration_ms ?? null : null,
            requestId,
          ]
        );

        // Update request status to completed
        await query(
          `UPDATE app.generation_requests SET status = 'completed', finished_at = NOW() WHERE id = $1`,
          [requestId]
        );
      } catch (error) {
        console.error(`Error processing item ${index}:`, error);

        // Update request status to failed
        await query(
          `UPDATE app.generation_requests SET status = 'failed', error = $1, finished_at = NOW() WHERE id = $2`,
          [error instanceof Error ? error.message : "Unknown error", requestId]
        );
      }
    }

    // Update batch status
    await query(`UPDATE app.batches SET status = 'completed' WHERE id = $1`, [
      batchId,
    ]);
  } catch (error) {
    console.error("Batch processing error:", error);
    await query(`UPDATE app.batches SET status = 'failed' WHERE id = $1`, [
      batchId,
    ]);
  }
}
