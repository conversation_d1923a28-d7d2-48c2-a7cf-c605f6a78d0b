import { FastifyInstance } from "fastify";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { randomUUID } from "crypto";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the data directory containing scraped JSON files
const DATA_DIR = path.resolve(__dirname, '../../../data');

interface ScrapedProduct {
  id: string;
  title: string;
  handle: string;
  domain: string;
  shop_id: string;
  scraped_at: string;
  images: Array<{
    id?: string;
    src_url: string;
  }>;
  variants: Array<{
    id: string;
    sku: string;
    price: string;
    option1: string | null;
    option2: string | null;
    option3: string | null;
  }>;
  raw_json: any;
}

interface ScrapedData {
  products: ScrapedProduct[];
  collections?: Array<{
    slug: string;
    title: string;
    count: number;
  }>;
}

// Transform scraped product to main app format
function transformScrapedProduct(product: ScrapedProduct): any {
  // Generate collections based on domain and product type
  const collections = deriveCollectionsFromProduct(product);
  
  // Transform images to expected format
  const images = product.images.map((img, index) => ({
    id: img.id || `scraped_img_${product.id}_${index}`,
    src_url: ensureHttps(img.src_url),
    width: null,
    height: null,
    alt: product.title,
    created_at: product.scraped_at
  }));

  return {
    product_id: `test_${product.id}`, // Prefix with 'test_' to distinguish
    title: product.title,
    handle: product.handle,
    status: "ACTIVE",
    default_image_url: images[0]?.src_url || "",
    images,
    collections,
    updated_at: product.scraped_at,
    stable_id: `test_${product.id}`
  };
}

// Derive collections from product data
function deriveCollectionsFromProduct(product: ScrapedProduct): Array<{id: string; name: string; color: string}> {
  const collections: Array<{id: string; name: string; color: string}> = [];
  
  // Domain-based collection
  const domain = product.domain.replace(/^www\./, '');
  collections.push({
    id: domain,
    name: domain.split('.')[0].toUpperCase(),
    color: "bg-blue-500"
  });

  // Type-based collection from raw_json if available
  if (product.raw_json?.type) {
    const type = product.raw_json.type.toLowerCase();
    collections.push({
      id: type,
      name: product.raw_json.type,
      color: getTypeColor(type)
    });
  }

  // Tag-based collections if available
  if (Array.isArray(product.raw_json?.tags)) {
    product.raw_json.tags.slice(0, 2).forEach((tag: string) => {
      collections.push({
        id: tag.toLowerCase().replace(/\s+/g, '-'),
        name: tag,
        color: getTagColor(tag.toLowerCase())
      });
    });
  }

  return collections;
}

function getTypeColor(type: string): string {
  switch (type) {
    case 'brewpack': return 'bg-amber-500';
    case 'replacementpart': return 'bg-gray-500';
    case 'hardware': return 'bg-green-500';
    case 'subscription': return 'bg-purple-500';
    default: return 'bg-blue-500';
  }
}

function getTagColor(tag: string): string {
  switch (tag) {
    case 'accessories': return 'bg-cyan-500';
    case 'brew gear': return 'bg-orange-500';
    case 'consumable': return 'bg-red-500';
    case 'brewpack': return 'bg-amber-500';
    default: return 'bg-indigo-500';
  }
}

function ensureHttps(url: string): string {
  if (!url) return url;
  if (url.startsWith("//")) return "https:" + url;
  if (url.startsWith("http://")) return url.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(url)) return "https://" + url.replace(/^\/+/, "");
  return url;
}

// Load all scraped JSON files
function loadAllScrapedData(): ScrapedProduct[] {
  const allProducts: ScrapedProduct[] = [];
  
  try {
    if (!fs.existsSync(DATA_DIR)) {
      console.warn(`Data directory not found: ${DATA_DIR}`);
      return allProducts;
    }

    const files = fs.readdirSync(DATA_DIR).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
      try {
        const filePath = path.join(DATA_DIR, file);
        const content = fs.readFileSync(filePath, 'utf-8');
        const data: ScrapedData = JSON.parse(content);
        
        if (Array.isArray(data.products)) {
          allProducts.push(...data.products);
        }
      } catch (error) {
        console.warn(`Failed to parse ${file}:`, error);
        // Continue with other files
      }
    }
  } catch (error) {
    console.error('Error loading scraped data:', error);
  }

  return allProducts;
}

function parseCursor(cursor?: string): { ts: string; id: string } | null {
  if (!cursor) return null;
  const parts = cursor.split("|");
  if (parts.length !== 2) return null;
  const [ts, id] = parts;
  const isTs = !isNaN(Date.parse(ts));
  // For scraped data, we use test_ prefixed IDs
  const isValidId = id.startsWith('test_') && id.length > 5;
  if (!isTs || !isValidId) return null;
  return { ts: new Date(ts).toISOString(), id };
}

export async function scrapedTestingRoutes(app: FastifyInstance) {
  // GET /catalog/scraped-products - Returns scraped data in same format as /catalog/products
  app.get("/catalog/scraped-products", async (req, reply) => {
    const q = (req.query as any) || {};
    const limitRaw = q.limit;
    const cursorRaw = q.cursor as string | undefined;

    let limit = Number(limitRaw ?? 50);
    if (!Number.isFinite(limit) || limit < 1) limit = 50;
    if (limit > 200) limit = 200;

    const cur = parseCursor(cursorRaw);
    if (cursorRaw && !cur) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "malformed cursor" } });
    }

    try {
      // Load all scraped products
      const scrapedProducts = loadAllScrapedData();
      
      if (scrapedProducts.length === 0) {
        return reply.send({ 
          items: [], 
          next_cursor: null,
          meta: { 
            source: 'scraped_files',
            total_files: 0,
            message: 'No scraped data found in data/ directory'
          }
        });
      }

      // Transform to main app format
      const transformed = scrapedProducts.map(transformScrapedProduct);
      
      // Sort by scraped date (most recent first) and apply cursor pagination
      let sorted = transformed.sort((a, b) => {
        const dateA = new Date(a.updated_at).getTime();
        const dateB = new Date(b.updated_at).getTime();
        if (dateA !== dateB) return dateB - dateA; // Most recent first
        return b.product_id.localeCompare(a.product_id); // Secondary sort by ID
      });

      // Apply cursor filtering
      if (cur) {
        const cursorDate = new Date(cur.ts).getTime();
        sorted = sorted.filter(item => {
          const itemDate = new Date(item.updated_at).getTime();
          return itemDate < cursorDate || 
            (itemDate === cursorDate && item.product_id < cur.id);
        });
      }

      // Pagination
      const page = sorted.slice(0, limit + 1);
      const hasMore = page.length > limit;
      const items = page.slice(0, limit);

      // Generate next cursor
      const next_cursor = hasMore && items.length > 0
        ? `${items[items.length - 1].updated_at}|${items[items.length - 1].product_id}`
        : null;

      return reply.send({
        items,
        next_cursor,
        meta: {
          source: 'scraped_files',
          total_products: transformed.length,
          domains: Array.from(new Set(scrapedProducts.map(p => p.domain))),
          message: `Loaded ${transformed.length} products from ${scrapedProducts.length} scraped records`
        }
      });

    } catch (error) {
      console.error('Error serving scraped products:', error);
      return reply
        .code(500)
        .send({ 
          error: { 
            code: "internal_error", 
            message: "Failed to load scraped products" 
          } 
        });
    }
  });

  // GET /catalog/scraped-products/count - Count of scraped products
  app.get("/catalog/scraped-products/count", async (req, reply) => {
    try {
      const scrapedProducts = loadAllScrapedData();
      return reply.send({ 
        count: scrapedProducts.length,
        meta: {
          source: 'scraped_files',
          domains: Array.from(new Set(scrapedProducts.map(p => p.domain)))
        }
      });
    } catch (error) {
      console.error('Error counting scraped products:', error);
      return reply.send({ count: 0 });
    }
  });

  // GET /catalog/scraped-collections - Available collections from scraped data
  app.get("/catalog/scraped-collections", async (req, reply) => {
    try {
      const scrapedProducts = loadAllScrapedData();
      const collectionMap = new Map<string, {id: string; name: string; color: string}>();
      
      scrapedProducts.forEach(product => {
        const collections = deriveCollectionsFromProduct(product);
        collections.forEach(col => {
          if (!collectionMap.has(col.id)) {
            collectionMap.set(col.id, col);
          }
        });
      });

      const items = Array.from(collectionMap.values()).sort((a, b) => 
        a.name.localeCompare(b.name)
      );

      return reply.send({ items });
    } catch (error) {
      console.error('Error loading scraped collections:', error);
      return reply.send({ items: [] });
    }
  });

  // GET /catalog/scraped-stats - Statistics about scraped data
  app.get("/catalog/scraped-stats", async (req, reply) => {
    try {
      const scrapedProducts = loadAllScrapedData();
      
      // Calculate statistics
      const domains = Array.from(new Set(scrapedProducts.map(p => p.domain)));
      const types = Array.from(new Set(scrapedProducts.map(p => p.raw_json?.type).filter(Boolean)));
      const totalImages = scrapedProducts.reduce((sum, p) => sum + (p.images?.length || 0), 0);
      const totalVariants = scrapedProducts.reduce((sum, p) => sum + (p.variants?.length || 0), 0);
      
      const domainStats = domains.map(domain => ({
        domain,
        count: scrapedProducts.filter(p => p.domain === domain).length
      }));

      return reply.send({
        total_products: scrapedProducts.length,
        total_images: totalImages,
        total_variants: totalVariants,
        domains: domainStats,
        types,
        last_scraped: scrapedProducts.length > 0 
          ? new Date(Math.max(...scrapedProducts.map(p => new Date(p.scraped_at).getTime()))).toISOString()
          : null
      });
    } catch (error) {
      console.error('Error getting scraped stats:', error);
      return reply.send({ 
        total_products: 0,
        total_images: 0,
        total_variants: 0,
        domains: [],
        types: []
      });
    }
  });
}