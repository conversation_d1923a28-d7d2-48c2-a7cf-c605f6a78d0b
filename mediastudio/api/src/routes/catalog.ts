import { FastifyInstance } from "fastify";
import { query } from "../db";
import { randomUUID } from "crypto";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Function to derive collection from handle
function deriveCollectionFromHandle(handle: string): string | undefined {
  const lowerHandle = handle.toLowerCase();
  if (lowerHandle.includes("leggings") || lowerHandle.includes("yoga-hose"))
    return "leggings";
  if (lowerHandle.includes("tanzschuhe")) return "tanzschuhe-fur-frauen";
  if (lowerHandle.includes("sport-bra") || lowerHandle.includes("yoga-t-shirt"))
    return "top-sports-bras";
  return undefined;
}

// Collection colors for consistent display
const collectionColors: Record<string, string> = {
  leggings: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
  "tanzschuhe-fur-frauen":
    "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200",
  "top-sport-bras":
    "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
  "top-sports-bras":
    "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200",
};

function ensureHttps(url: string): string {
  if (!url) return url;
  if (url.startsWith("//")) return "https:" + url;
  if (url.startsWith("http://")) return url.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(url)) return "https://" + url.replace(/^\/+/, "");
  return url;
}

function extractHandlesFromCollectionHtml(html: string): string[] {
  const handles = new Set<string>();
  const re = /href=["']([^"']+)["']/gi;
  let m: RegExpExecArray | null;
  while ((m = re.exec(html))) {
    const href = m[1];
    const mm = href.match(/\/products\/([^\/?#"']+)/);
    if (mm && mm[1]) handles.add(mm[1]);
  }
  return Array.from(handles);
}

async function fetchOgImage(url: string): Promise<string | null> {
  try {
    const res = await fetch(url, {
      headers: { "user-agent": "MediaStudioBot/0.1" } as any,
    });
    if (res.status !== 200) return null;
    const html = await res.text();
    const m = html.match(
      /<meta[^>]+property=["']og:image(?::secure_url)?["'][^>]*content=["']([^"']+)["'][^>]*>/i
    );
    return m ? ensureHttps(m[1]) : null;
  } catch {
    return null;
  }
}

async function fetchProductJs(
  shopDomain: string,
  handle: string
): Promise<{ title: string; image?: string } | null> {
  const urls = [`https://${shopDomain}/products/${handle}.js`];
  for (const u of urls) {
    try {
      const res = await fetch(u, {
        headers: { "user-agent": "MediaStudioBot/0.1" } as any,
      });
      if (res.status === 200) {
        const data: any = await res.json();
        if (data && data.title) {
          const img =
            Array.isArray(data.images) && data.images.length > 0
              ? ensureHttps(String(data.images[0]))
              : undefined;
          return { title: String(data.title), image: img };
        }
      }
    } catch {}
  }
  return null;
}

async function fetchFallbackFromCollections(): Promise<
  Array<{
    product_id: string;
    title: string;
    handle: string;
    status: string;
    default_image_url: string;
  }>
> {
  const collections = [
    "https://www.dancingqueens.ch/collections/leggings",
    "https://www.dancingqueens.ch/collections/tanzschuhe-fur-frauen",
    "https://www.dancingqueens.ch/collections/top-sport-bhs",
  ];
  const items: Array<{
    product_id: string;
    title: string;
    handle: string;
    status: string;
    default_image_url: string;
  }> = [];
  for (const coll of collections) {
    try {
      const u = new URL(coll);
      const shop = u.host;
      const res = await fetch(coll, {
        headers: { "user-agent": "MediaStudioBot/0.1" } as any,
      });
      if (res.status !== 200) continue;
      const html = await res.text();
      const handles = extractHandlesFromCollectionHtml(html);
      for (const handle of handles) {
        // Limit to a reasonable number to avoid heavy scraping
        if (items.length >= 60) break;
        const pj = await fetchProductJs(shop, handle);
        let title = pj?.title || handle;
        let image = pj?.image;
        if (!image) {
          const og = await fetchOgImage(`https://${shop}/products/${handle}`);
          if (og) image = og;
        }
        items.push({
          product_id: randomUUID(),
          title,
          handle,
          status: "ACTIVE",
          default_image_url: image || "",
        });
      }
      if (items.length >= 60) break;
    } catch {}
  }
  return items;
}

function parseCursor(cursor?: string): { ts: string; id: string } | null {
  if (!cursor) return null;
  const parts = cursor.split("|");
  if (parts.length !== 2) return null;
  const [ts, id] = parts;
  const isTs = !isNaN(Date.parse(ts));
  const isUuid = /^[0-9a-fA-F-]{36}$/.test(id);
  if (!isTs || !isUuid) return null;
  return { ts: new Date(ts).toISOString(), id };
}

export async function catalogRoutes(app: FastifyInstance) {
  // GET /catalog/products
  app.get("/catalog/products", async (req, reply) => {
    const q = (req.query as any) || {};
    const limitRaw = q.limit;
    const cursorRaw = q.cursor as string | undefined;
    const shopIdRaw = q.shop_id as string | undefined;

    let limit = Number(limitRaw ?? 50);
    if (!Number.isFinite(limit) || limit < 1) limit = 50;
    if (limit > 200) limit = 200;

    const cur = parseCursor(cursorRaw);
    if (cursorRaw && !cur) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "malformed cursor" } });
    }

    const params: any[] = [];
    const conds: string[] = [];
    if (cur) {
      conds.push(
        "(cp.updated_at, cp.product_id) < ($1::timestamptz, $2::uuid)"
      );
      params.push(cur.ts, cur.id);
    }

    let shopFilter = "";
    if (shopIdRaw) {
      const isUuid = /^[0-9a-fA-F-]{36}$/.test(shopIdRaw);
      if (!isUuid) {
        return reply
          .code(400)
          .send({ error: { code: "bad_request", message: "invalid shop_id" } });
      }
      conds.push(`p.id IS NOT NULL AND p.shop_id = $${params.length + 1}`);
      params.push(shopIdRaw);
      shopFilter = "LEFT JOIN app.products p ON p.id = cp.product_id";
    }

    // Collection filter (server-side) using handle patterns to align with deriveCollectionFromHandle
    const collectionsRaw = (q.collections as string | undefined)?.trim();
    if (collectionsRaw) {
      const slugs = collectionsRaw
        .split(",")
        .map((s) => s.trim().toLowerCase())
        .filter(Boolean);
      const pat: string[] = [];
      for (const s of slugs) {
        if (s === "leggings") {
          pat.push("lower(cp.handle) LIKE '%leggings%'");
          pat.push("lower(cp.handle) LIKE '%yoga-hose%'");
        } else if (s === "tanzschuhe-fur-frauen") {
          pat.push("lower(cp.handle) LIKE '%tanzschuhe%'");
        } else if (s === "top-sports-bras" || s === "top-sport-bhs") {
          pat.push("lower(cp.handle) LIKE '%sport-bra%'");
          pat.push("lower(cp.handle) LIKE '%yoga-t-shirt%'");
        }
      }
      if (pat.length > 0) {
        conds.push(`(${pat.join(" OR ")})`);
      }
    }

    const whereClause = conds.length ? `WHERE ${conds.join(" AND ")}` : "";

    const sql = `
      SELECT cp.product_id,
             cp.title,
             cp.handle,
             cp.status,
             COALESCE(NULLIF(cp.default_image_url, ''), (
               SELECT pi2.src_url FROM app.product_images pi2 WHERE pi2.product_id = cp.product_id ORDER BY pi2.created_at ASC LIMIT 1
             ), '') AS default_image_url,
             cp.updated_at,
             cp.stable_id,
             COALESCE(
               json_agg(
                 json_build_object(
                   'id', pi.id,
                   'src_url', pi.src_url,
                   'width', pi.width,
                   'height', pi.height,
                   'alt', pi.alt,
                   'created_at', pi.created_at
                 )
               ) FILTER (WHERE pi.id IS NOT NULL),
               '[]'::json
             ) AS images
      FROM app.catalog_products_v cp
      ${shopFilter}
      LEFT JOIN app.product_images pi ON pi.product_id = cp.product_id
      ${whereClause}
      GROUP BY cp.product_id, cp.title, cp.handle, cp.status, cp.default_image_url, cp.updated_at, cp.stable_id
      ORDER BY cp.updated_at DESC, cp.product_id DESC
      LIMIT $${params.length + 1}
    `;
    params.push(limit + 1);

    let { rows } = await query(sql, params);
    let hasMore = rows.length > limit;
    let slice = rows.slice(0, limit);

    // If DB has nothing, construct a fallback list directly from the three collections
    if (slice.length === 0) {
      const fallback = await fetchFallbackFromCollections();
      rows = fallback as any;
      hasMore = false;
      slice = rows.slice(0, limit);
    }

    // Map DB rows to API response shape, and attach collections (derived from handle) when available
    const items = slice.map((r: any) => {
      // Derive a single collection slug from the handle
      const slug = r.handle ? deriveCollectionFromHandle(r.handle) : undefined;

      // Map slug to a readable name and a color class for the dot indicator
      const slugToDotColor: Record<string, string> = {
        leggings: "bg-emerald-600",
        "tanzschuhe-fur-frauen": "bg-purple-600",
        "top-sport-bhs": "bg-pink-600",
        "top-sports-bras": "bg-pink-600",
      };

      const slugToDisplayName: Record<string, string> = {
        leggings: "Leggings",
        "tanzschuhe-fur-frauen": "Tanzschuhe für Frauen",
        "top-sport-bhs": "Top / Sport BHs",
        "top-sports-bras": "Top / Sport BHs",
      };
      const toDisplayName = (s: string) =>
        slugToDisplayName[s] ||
        s
          .split("-")
          .map((w) => (w ? w[0].toUpperCase() + w.slice(1) : w))
          .join(" ");

      const collections = slug
        ? [
            {
              id: slug,
              name: toDisplayName(slug),
              color: slugToDotColor[slug] || "bg-gray-500",
            },
          ]
        : [];

      return {
        product_id: r.product_id,
        title: r.title,
        handle: r.handle,
        status: r.status,
        default_image_url: r.default_image_url,
        images: Array.isArray(r.images)
          ? r.images
          : r.images
          ? JSON.parse(r.images)
          : [],
        collections,
      };
    });

    const last = slice[slice.length - 1];
    const next_cursor =
      hasMore && last
        ? `${new Date(last.updated_at).toISOString()}|${last.product_id}`
        : null;

    return reply.send({ items, next_cursor });
  });

  // GET /catalog/products/count - total number of products (optionally by shop and collections)
  app.get("/catalog/products/count", async (req, reply) => {
    const q = (req.query as any) || {};
    const shopIdRaw = q.shop_id as string | undefined;
    const collectionsRaw = (q.collections as string | undefined)?.trim();

    const params: any[] = [];
    let joinShop = "";
    const conds: string[] = [];

    if (shopIdRaw) {
      const isUuid = /^[0-9a-fA-F-]{36}$/.test(shopIdRaw);
      if (!isUuid) {
        return reply
          .code(400)
          .send({ error: { code: "bad_request", message: "invalid shop_id" } });
      }
      joinShop = "LEFT JOIN app.products p ON p.id = cp.product_id";
      conds.push(`p.id IS NOT NULL AND p.shop_id = $${params.length + 1}`);
      params.push(shopIdRaw);
    }

    if (collectionsRaw) {
      const slugs = collectionsRaw
        .split(",")
        .map((s) => s.trim().toLowerCase())
        .filter(Boolean);
      const pat: string[] = [];
      for (const s of slugs) {
        if (s === "leggings") {
          pat.push("lower(cp.handle) LIKE '%leggings%'");
          pat.push("lower(cp.handle) LIKE '%yoga-hose%'");
        } else if (s === "tanzschuhe-fur-frauen") {
          pat.push("lower(cp.handle) LIKE '%tanzschuhe%'");
        } else if (s === "top-sports-bras" || s === "top-sport-bhs") {
          pat.push("lower(cp.handle) LIKE '%sport-bra%'");
          pat.push("lower(cp.handle) LIKE '%yoga-t-shirt%'");
        }
      }
      if (pat.length > 0) {
        conds.push(`(${pat.join(" OR ")})`);
      }
    }

    const where = conds.length ? `WHERE ${conds.join(" AND ")}` : "";

    const sql = `
      SELECT COUNT(*)::int AS c
      FROM app.catalog_products_v cp
      ${joinShop}
      ${where}
    `;

    const { rows } = await query<{ c: number }>(sql, params);
    return reply.send({ count: rows[0]?.c ?? 0 });
  });

  // GET /catalog/collections - distinct derived collections across all products (optionally by shop)
  app.get("/catalog/collections", async (req, reply) => {
    const q = (req.query as any) || {};
    const shopIdRaw = q.shop_id as string | undefined;

    const params: any[] = [];
    let joinShop = "";
    let where = "";
    if (shopIdRaw) {
      const isUuid = /^[0-9a-fA-F-]{36}$/.test(shopIdRaw);
      if (!isUuid) {
        return reply
          .code(400)
          .send({ error: { code: "bad_request", message: "invalid shop_id" } });
      }
      joinShop = "LEFT JOIN app.products p ON p.id = cp.product_id";
      where = "WHERE p.id IS NOT NULL AND p.shop_id = $1";
      params.push(shopIdRaw);
    }

    const sql = `
      SELECT DISTINCT cp.handle
      FROM app.catalog_products_v cp
      ${joinShop}
      ${where}
      LIMIT 5000
    `;

    const { rows } = await query<{ handle: string }>(sql, params);

    // Derive slugs and map to display name + dot color
    const slugToDotColor: Record<string, string> = {
      leggings: "bg-emerald-600",
      "tanzschuhe-fur-frauen": "bg-purple-600",
      "top-sport-bhs": "bg-pink-600",
      "top-sports-bras": "bg-pink-600",
    };
    const slugToDisplayName: Record<string, string> = {
      leggings: "Leggings",
      "tanzschuhe-fur-frauen": "Tanzschuhe für Frauen",
      "top-sport-bhs": "Top / Sport BHs",
      "top-sports-bras": "Top / Sport BHs",
    };
    const toDisplayName = (s: string) =>
      slugToDisplayName[s] ||
      s
        .split("-")
        .map((w) => (w ? w[0].toUpperCase() + w.slice(1) : w))
        .join(" ");

    const slugs = new Set<string>();
    for (const r of rows) {
      const slug = r.handle ? deriveCollectionFromHandle(r.handle) : undefined;
      if (slug) slugs.add(slug);
    }

    const items = Array.from(slugs).map((slug) => ({
      id: slug,
      name: toDisplayName(slug),
      color: slugToDotColor[slug] || "bg-gray-500",
    }));

    return reply.send({ items });
  });

  // GET /catalog/products/:product_id/images
  app.get("/catalog/products/:product_id/images", async (req, reply) => {
    const product_id = (req.params as any).product_id as string;
    if (!product_id || !product_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply.code(400).send({
        error: { code: "bad_request", message: "invalid product_id" },
      });
    }

    const sql = `
      SELECT pi.id, pi.src_url, pi.width, pi.height, pi.alt, pi.created_at
      FROM app.product_images pi
      WHERE pi.product_id = $1
      ORDER BY pi.created_at ASC
    `;
    const { rows } = await query(sql, [product_id]);
    return reply.send({ items: rows });
  });

  // GET /catalog/products/:product_id/variants
  app.get("/catalog/products/:product_id/variants", async (req, reply) => {
    const product_id = (req.params as any).product_id as string;
    if (!product_id || !product_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply.code(400).send({
        error: { code: "bad_request", message: "invalid product_id" },
      });
    }

    const sql = `
      SELECT v.id as variant_id, v.product_id, v.sku, v.price::text as price, v.option1, v.option2, v.option3
      FROM app.variants v
      WHERE v.product_id = $1
      ORDER BY v.updated_at DESC, v.id DESC
    `;
    const { rows } = await query(sql, [product_id]);
    return reply.send({ items: rows });
  });

  // GET /catalog/sku-count -> total variants count (SKUs)
  app.get("/catalog/sku-count", async (_req, reply) => {
    try {
      const { rows } = await query<{ c: number }>(
        "SELECT COUNT(*)::int AS c FROM app.variants"
      );
      return reply.send({ count: rows[0]?.c ?? 0 });
    } catch (e: any) {
      // If variants table doesn't exist yet, return 0 to avoid breaking the UI
      const msg = String(e?.message || e || "");
      if (/relation\s+\"?app\.variants\"?\s+does not exist/i.test(msg)) {
        return reply.send({ count: 0 });
      }
      throw e;
    }
  });
}
