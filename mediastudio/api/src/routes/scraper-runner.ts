import { query } from "../db";
import { upsertProductWithRelations } from "../services/scraperDb";
import fs from "fs";
import path from "path";
import { SCRAPER_DATA_DIR, getDomainDataPath, fetchProduct } from "./scraper-utils";

// In-memory runners (per-process). Resume is triggered by /resume.
export const runners = new Map<string, { stop: () => void }>();

export async function startRunner(jobId: string, domain: string, shopId: string) {
  let stopped = false;
  const stop = () => {
    stopped = true;
  };
  runners.set(jobId, { stop });

  const concurrency = 5;
  const scrapedProducts: any[] = [];

  const tick = async () => {
    if (stopped) return;
    // Pull a batch of queued items and process
    const batch = await query<{
      id: string;
      handle: string;
      collection_slug: string;
    }>(
      `UPDATE app.scrape_job_items i
       SET status = 'running', updated_at = now()
       WHERE id IN (
         SELECT id FROM app.scrape_job_items
         WHERE job_id = $1 AND status IN ('queued','failed')
         ORDER BY updated_at ASC
         LIMIT $2
       )
       RETURNING id, handle, collection_slug`,
      [jobId, concurrency]
    );

    if (batch.rowCount === 0) {
      // No more work: mark job complete if nothing left running
      const remaining = await query(
        `SELECT 1 FROM app.scrape_job_items WHERE job_id = $1 AND status IN ('queued','running','failed') LIMIT 1`,
        [jobId]
      );
      if (remaining.rowCount === 0) {
        await query(
          `UPDATE app.scrape_jobs SET status = 'completed', finished_at = now() WHERE id = $1 AND status <> 'canceled'`,
          [jobId]
        );

        // Save scraped products to domain-specific JSON file
        try {
          // Ensure directory exists
          if (!fs.existsSync(SCRAPER_DATA_DIR)) {
            fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
          }

          const domainDataPath = getDomainDataPath(domain);

          // Read existing data for this domain (supports flat array legacy and structured object)
          let existingParsed: any = [];
          if (fs.existsSync(domainDataPath)) {
            const existingContent = fs.readFileSync(domainDataPath, "utf8");
            existingParsed = JSON.parse(existingContent || "[]");
          }

          // Normalize to structured object
          let existingObj: any;
          if (Array.isArray(existingParsed)) {
            existingObj = {
              products: existingParsed,
              collections: [] as any[],
            };
          } else if (existingParsed && typeof existingParsed === "object") {
            existingObj = existingParsed;
            if (!Array.isArray(existingObj.products)) existingObj.products = [];
            if (!Array.isArray(existingObj.collections))
              existingObj.collections = [];
          } else {
            existingObj = { products: [], collections: [] as any[] };
          }

          // Merge new data (avoid duplicates by handle)
          const existingProducts: any[] = Array.isArray(existingObj.products)
            ? existingObj.products
            : [];
          const existingHandles = new Set(
            existingProducts.map((p: any) => p?.handle).filter(Boolean)
          );
          const newProducts = scrapedProducts.filter(
            (p) => !existingHandles.has(p.handle)
          );
          const mergedProducts = [...existingProducts, ...newProducts];

          // Write to domain-specific file as structured object { products, collections }
          try {
            existingObj.products = mergedProducts;
            fs.writeFileSync(
              domainDataPath,
              JSON.stringify(existingObj, null, 2)
            );
          } catch {
            // fallback to flat array if any issue
            fs.writeFileSync(
              domainDataPath,
              JSON.stringify(mergedProducts, null, 2)
            );
          }
          console.log(
            `Saved ${newProducts.length} new products for ${domain} to ${domainDataPath}`
          );
        } catch (error) {
          console.error("Failed to save scraped data to JSON:", error);
        }

        runners.delete(jobId);
        return;
      }
      // Otherwise, wait and retry
      setTimeout(tick, 1000);
      return;
    }

    await Promise.all(
      batch.rows.map(async (row: any) => {
        try {
          const pdata = await fetchProduct(domain, row.handle);
          if (!pdata) throw new Error("no product data");

          // Store product data for JSON export
          scrapedProducts.push({
            id:
              pdata.id?.toString() || `scraped_${Date.now()}_${Math.random()}`,
            title: pdata.title || "",
            handle: pdata.handle || "",
            domain: domain,
            shop_id: shopId,
            scraped_at: new Date().toISOString(),
            // retain collection context of this scrape for per-collection scraped counts
            collections: row.collection_slug ? [row.collection_slug] : [],
            images: Array.isArray(pdata.images)
              ? pdata.images
                  .map((img: any) => ({
                    id: img.id?.toString() || "",
                    src_url: img.src || "",
                    width: img.width,
                    height: img.height,
                    alt: img.alt,
                  }))
                  .filter((img) => img.src_url)
              : [],
            variants: Array.isArray(pdata.variants)
              ? pdata.variants.map((v: any) => ({
                  id: v.id?.toString() || "",
                  sku: v.sku,
                  price: v.price?.toString() || "0",
                  option1: v.option1,
                  option2: v.option2,
                  option3: v.option3,
                }))
              : [],
            raw_json: { ...pdata, shop_id: shopId, domain: domain },
          });

          await query(
            `UPDATE app.scrape_job_items SET status = 'done', attempts = attempts + 1, updated_at = now(), error = NULL WHERE id = $1`,
            [row.id]
          );
        } catch (err: any) {
          await query(
            `UPDATE app.scrape_job_items SET status = 'failed', attempts = attempts + 1, updated_at = now(), error = $2 WHERE id = $1`,
            [row.id, String(err?.message || err)]
          );
        }
      })
    );

    setTimeout(tick, 50);
  };

  setTimeout(tick, 10);
  return stop;
}