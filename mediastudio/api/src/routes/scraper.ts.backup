import { FastifyInstance } from "fastify";
import { query, withClient } from "../db";
import { upsertProductWithRelations } from "../services/scraperDb";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const SCRAPER_DATA_DIR = path.join(__dirname, "../../../data");

// Helper function to get domain-specific data file path
function getDomainDataPath(domain: string): string {
  const sanitizedDomain = domain.replace(/[^a-zA-Z0-9.-]/g, "_");
  return path.join(SCRAPER_DATA_DIR, `${sanitizedDomain}.json`);
}

const DEFAULT_HEADERS = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0 Safari/537.36",
  accept: "application/json, text/plain, */*",
  "accept-language": "en-US,en;q=0.9",
} as const;

async function ensureShop(
  domain: string
): Promise<{ id: string; domain: string }> {
  // Upsert by domain
  const sel = await query<{ id: string }>(
    `SELECT id FROM app.shops WHERE domain = $1`,
    [domain]
  );
  if (sel.rowCount && sel.rows[0]) return { id: sel.rows[0].id, domain };
  const ins = await query<{ id: string }>(
    `INSERT INTO app.shops (domain) VALUES ($1) ON CONFLICT (domain) DO UPDATE SET domain = excluded.domain RETURNING id`,
    [domain]
  );
  return { id: ins.rows[0].id, domain };
}

function ensureHttps(u: string): string {
  if (!u) return u;
  if (u.startsWith("//")) return "https:" + u;
  if (u.startsWith("http://")) return u.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(u)) return "https://" + u.replace(/^\/+/, "");
  return u;
}

async function tryFetchJson(url: string): Promise<any | null> {
  try {
    const u = new URL(url);
    const res = await fetch(url, {
      headers: { ...DEFAULT_HEADERS, referer: `${u.origin}/` },
    });
    if (res.ok) {
      const contentType = res.headers.get('content-type') || '';
      if (contentType.includes('application/json')) {
        return await res.json();
      } else {
        // If it's not JSON, get the text and return it as a string
        // This allows us to detect HTML responses
        const text = await res.text();
        return text;
      }
    }
  } catch {}
  return null;
}

async function detectWebflow(domain: string): Promise<boolean> {
  try {
    const res = await fetch(`https://${domain}/`, {
      headers: DEFAULT_HEADERS,
    });
    if (res.ok) {
      const html = await res.text();
      return html.includes('data-wf-site') || html.includes('cdn.prod.website-files.com');
    }
  } catch {}
  return false;
}

async function scrapeCollectionPageHTML(domain: string, slug: string, limit?: number): Promise<string[]> {
  const handles = new Set<string>();

  try {
    // Try to fetch the collection page
    const collectionUrl = `https://${domain}/collections/${slug}`;
    const res = await fetch(collectionUrl, {
      headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
    });

    if (!res.ok) {
      console.warn(`Failed to fetch collection page: ${collectionUrl}`);
      return [];
    }

    const html = await res.text();

    // Look for product links in the HTML
    // Common patterns for Shopify product links
    const productLinkPatterns = [
      /href=["']\/products\/([^"']+)["']/gi,
      /href=["']([^"']*\/products\/[^"']+)["']/gi,
      /data-product-handle=["']([^"']+)["']/gi,
      /data-handle=["']([^"']+)["']/gi
    ];

    for (const pattern of productLinkPatterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        let handle = match[1];

        // Clean up the handle if it contains full URL
        if (handle.includes('/products/')) {
          handle = handle.split('/products/')[1].split('/')[0].split('?')[0];
        }

        if (handle && handle.length > 0 && !handle.includes('/') && !handle.includes('?')) {
          handles.add(handle);
          if (limit && handles.size >= limit) {
            return Array.from(handles);
          }
        }
      }
    }

    // Also try to find JSON-LD structured data
    const jsonLdMatch = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]+)<\/script>/gi);
    if (jsonLdMatch) {
      for (const script of jsonLdMatch) {
        try {
          const jsonContent = script.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
          const data = JSON.parse(jsonContent);
          if (data && data['@type'] === 'Product' && data.sku) {
            handles.add(data.sku);
            if (limit && handles.size >= limit) {
              return Array.from(handles);
            }
          }
          // Handle CollectionPage with itemListElement
          if (data && data['@type'] === 'CollectionPage' && data.mainEntity && data.mainEntity.itemListElement) {
            for (const item of data.mainEntity.itemListElement) {
              if (item && item.url) {
                const url = item.url;
                const match = url.match(/\/products\/([^\/?#]+)/);
                if (match && match[1]) {
                  handles.add(match[1]);
                  if (limit && handles.size >= limit) {
                    return Array.from(handles);
                  }
                }
              }
            }
          }
        } catch {}
      }
    }

    console.log(`Found ${handles.size} products in collection ${slug} via HTML scraping`);
  } catch (error) {
    console.warn(`Error scraping collection page for ${slug}:`, error);
  }

  return Array.from(handles);
}

async function scrapeProductPageHTML(domain: string, handle: string): Promise<any | null> {
  try {
    // Try to fetch the product page
    const productUrl = `https://${domain}/products/${handle}`;
    const res = await fetch(productUrl, {
      headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
    });

    if (!res.ok) {
      console.warn(`Failed to fetch product page: ${productUrl}`);
      return null;
    }

    const html = await res.text();

    // Extract product data from HTML using regex patterns
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|.*$/, '').trim() : handle.replace(/-/g, ' ');

    // Extract description - look for various meta tags and content
    let description = '';
    const descPatterns = [
      /<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i,
      /<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']+)["']/i,
      /<div[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)<\/div>/i
    ];

    for (const pattern of descPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        description = match[1].trim();
        break;
      }
    }

    // Extract images with better filtering for modern Shopify stores
    const images: any[] = [];
    const imgMatches = html.match(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi) || [];

    for (const imgMatch of imgMatches) {
      const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
      const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
      const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

      if (srcMatch && srcMatch[1]) {
        const src = ensureHttps(srcMatch[1]);
        const alt = altMatch ? altMatch[1] : '';
        const classes = classMatch ? classMatch[1] : '';

        // Skip navigation images, icons, logos, and small images
        if (src.includes('icon') || src.includes('logo') || src.includes('favicon') ||
            classes.includes('nav') || classes.includes('menu') || classes.includes('social') ||
            classes.includes('navbar') || classes.includes('navigation') ||
            src.includes('small') || src.includes('thumbnail') ||
            alt.includes('navigation') || alt.includes('menu') ||
            // Skip images that are clearly from other products
            (!src.toLowerCase().includes(handle.toLowerCase()) &&
             !alt.toLowerCase().includes(handle.toLowerCase()) &&
             !classes.includes('hero') && !classes.includes('main') && !classes.includes('primary') &&
             !classes.includes('large') && !classes.includes('full') &&
             !src.includes('hero') && !src.includes('main'))) {
          continue;
        }

        // Prioritize images that are likely product images
        const isProductImage = (
          // Hero images
          classes.includes('hero') || src.includes('hero') ||
          // Main product images
          classes.includes('main') || classes.includes('primary') ||
          // Large images
          classes.includes('large') || classes.includes('full') ||
          // Product-specific images (check if filename contains product keywords)
          src.toLowerCase().includes(handle.toLowerCase()) ||
          alt.toLowerCase().includes(handle.toLowerCase()) ||
          // Images in main content areas
          !classes.includes('nav') && !classes.includes('footer') && !classes.includes('header')
        );

        if (isProductImage) {
          images.push({
            src,
            alt: alt || title,
            width: 800, // default
            height: 600, // default
          });
        }
      }
    }

    // If no images found with the above criteria, fall back to all non-navigation images
    if (images.length === 0) {
      for (const imgMatch of imgMatches) {
        const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
        const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
        const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

        if (srcMatch && srcMatch[1]) {
          const src = ensureHttps(srcMatch[1]);
          const alt = altMatch ? altMatch[1] : '';
          const classes = classMatch ? classMatch[1] : '';

          // Skip only the most obvious navigation/UI images
          if (!classes.includes('nav') && !classes.includes('menu') && !classes.includes('social') &&
              !src.includes('icon') && !src.includes('logo') && !src.includes('favicon')) {
            images.push({
              src,
              alt: alt || title,
              width: 800,
              height: 600,
            });
          }
        }
      }
    }

    // Extract price - look for various price patterns
    let price = '0';
    const pricePatterns = [
      /<span[^>]*class="[^"]*price[^"]*"[^>]*>([^<]+)<\/span>/gi,
      /<div[^>]*class="[^"]*price[^"]*"[^>]*>([^<]+)<\/div>/gi,
      /<meta[^>]*property=["']product:price:amount["'][^>]*content=["']([^"']+)["']/gi,
      /\$(\d+(?:\.\d{2})?)/g,
      /€(\d+(?:[.,]\d{2})?)/g
    ];

    for (const pattern of pricePatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        price = match[1].replace(/[^\d.]/g, '');
        break;
      }
    }

    return {
      id: handle,
      title,
      handle,
      description,
      images,
      variants: [{
        id: `variant_${handle}`,
        price,
        option1: 'Default',
      }],
      available: true,
      product_type: 'Product',
      tags: [],
      vendor: domain,
    };
  } catch (error) {
    console.warn(`Error scraping product page for ${handle}:`, error);
    return null;
  }
}

async function discoverCollections(
  domain: string,
  opts?: { limit?: number; offset?: number; fast?: boolean }
): Promise<Array<{ slug: string; title: string; count: number }>> {
  // Strategy: prefer /collections.json (fast), optionally slice/paginate; skip per-collection counts in fast mode
  const base = `https://${domain}`;
  const data = await tryFetchJson(`${base}/collections.json?limit=250`);
  const all: Array<{ slug: string; title: string }> = [];

  // Check if we got HTML instead of JSON (modern Shopify stores)
  if (data && typeof data === 'string' && data.includes('<html')) {
    console.warn(`Got HTML response for ${base}/collections.json, trying alternative methods`);
  } else if (data && Array.isArray(data.collections)) {
    for (const c of data.collections) {
      const slug = String(c.handle || c.id || "");
      const title = String(c.title || slug);
      all.push({ slug, title });
    }
    const offset = Math.max(0, opts?.offset ?? 0);
    const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
    const slice = all.slice(offset, offset + limit);

    if (opts?.fast !== false) {
      // Fast path: no extra network to compute counts
      return slice.map((c) => ({ ...c, count: 0 }));
    }

    // Slow path: optionally compute a lightweight count estimate only for the slice
    const out: Array<{ slug: string; title: string; count: number }> = [];
    for (const c of slice) {
      let count = 0;
      try {
        const first = await tryFetchJson(
          `${base}/collections/${c.slug}/products.json?limit=1`
        );
        if (first && Array.isArray(first.products)) {
          count = first.products.length; // not total, but signals non-empty
        }
      } catch {}
      out.push({ slug: c.slug, title: c.title, count });
    }
    return out;
  }

  // Fallback: parse /collections HTML links and paginate locally
  try {
    const res = await fetch(`${base}/collections`, {
      headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
    });
    if (res.ok) {
      const html = await res.text();
      const re = /href=["']([^"']+)["']/gi;
      const seen = new Set<string>();
      let m: RegExpExecArray | null;
      while ((m = re.exec(html))) {
        const href = m[1];
        const mm = href.match(/\/collections\/([^\/?#"']+)/);
        if (mm && mm[1]) {
          const slug = mm[1];
          if (!seen.has(slug)) {
            seen.add(slug);
            all.push({ slug, title: slug.replace(/-/g, " ") });
          }
        }
      }
      if (all.length > 0) {
        const offset = Math.max(0, opts?.offset ?? 0);
        const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
        const slice = all.slice(offset, offset + limit);
        return slice.map((c) => ({ ...c, count: 0 }));
      }
    }
  } catch {}

  // Final fallback: parse sitemap for collections
  try {
    const sitemapUrls = [
      `${base}/sitemap_collections_1.xml`,
      `${base}/sitemap.xml`,
    ];
    const seen = new Set<string>();
    for (const sm of sitemapUrls) {
      try {
        const res = await fetch(sm, {
          headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
        });
        if (!res.ok) continue;
        const xml = await res.text();
        const locRe = /<loc>([^<]+)<\/loc>/gi;
        let m: RegExpExecArray | null;
        while ((m = locRe.exec(xml))) {
          const loc = m[1];
          const mm = loc.match(/\/collections\/([^\/?#"']+)/);
          if (mm && mm[1] && !seen.has(mm[1])) {
            seen.add(mm[1]);
            all.push({ slug: mm[1], title: mm[1].replace(/-/g, " ") });
          }
        }
        if (all.length > 0) break;
      } catch {}
    }
    if (all.length > 0) {
      const offset = Math.max(0, opts?.offset ?? 0);
      const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
      const slice = all.slice(offset, offset + limit);
      return slice.map((c) => ({ ...c, count: 0 }));
    }
  } catch {}

  // Webflow fallback: detect Webflow and parse main page for product links
  try {
    const isWebflow = await detectWebflow(domain);
    if (isWebflow) {
      const res = await fetch(`${base}/`, {
        headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
      });
      if (res.ok) {
        const html = await res.text();
        const seen = new Set<string>();
        // Look for product-like links in navigation or main content
        const productRe = /href=["']([^"']+)["']/gi;
        let m: RegExpExecArray | null;
        while ((m = productRe.exec(html))) {
          const href = m[1];
          // Match product-like paths (e.g., /hybride-warmtepomp, /all-electric)
          const mm = href.match(/^\/([a-z0-9-]+(?:-[a-z0-9-]+)*)$/);
          if (mm && mm[1] && !seen.has(mm[1]) && mm[1].length > 3) { // avoid short paths
            seen.add(mm[1]);
            all.push({ slug: mm[1], title: mm[1].replace(/-/g, " ") });
          }
        }
        if (all.length > 0) {
          const offset = Math.max(0, opts?.offset ?? 0);
          const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
          const slice = all.slice(offset, offset + limit);
          return slice.map((c) => ({ ...c, count: 0 }));
        }
      }
    }
  } catch {}

  return [];
}

async function fetchCollectionHandles(
  domain: string,
  slug: string,
  limitPerCollection?: number
): Promise<string[]> {
  const base = `https://${domain}`;
  const handles = new Set<string>();

  // Check if it's a Webflow site
  const isWebflow = await detectWebflow(domain);

  if (isWebflow) {
    // For Webflow sites like quatt.io, each "collection" is actually a single product page
    // So we treat the slug itself as a product handle
    handles.add(slug);
    if (limitPerCollection && handles.size >= limitPerCollection)
      return Array.from(handles);
  } else {
    // Original Shopify logic
    let page = 1;
    const pageSize = 250;
    let htmlFallbackUsed = false;

    while (true) {
      const url = `${base}/collections/${slug}/products.json?limit=${pageSize}&page=${page}`;
      const data = await tryFetchJson(url);

      // Check if we got HTML instead of JSON (modern Shopify stores)
      if (data && typeof data === 'string' && data.includes('<html')) {
        if (!htmlFallbackUsed) {
          console.warn(`Got HTML response for ${url}, trying HTML scraping for collection ${slug}`);
          // Try HTML scraping approach
          const htmlHandles = await scrapeCollectionPageHTML(domain, slug, limitPerCollection);
          console.log(`HTML scraping found ${htmlHandles.length} products for collection ${slug}`);
          handles = new Set([...handles, ...htmlHandles]);
          htmlFallbackUsed = true;
        }
        break;
      }

      const products: any[] =
        data && Array.isArray(data.products) ? data.products : [];
      for (const p of products) {
        if (p && p.handle) {
          handles.add(String(p.handle));
          if (limitPerCollection && handles.size >= limitPerCollection)
            return Array.from(handles);
        }
      }
      if (!products.length) break;
      page += 1;
      if (page > 20) break; // safety
    }
  }

  return Array.from(handles);
}

async function fetchProduct(
  domain: string,
  handle: string
): Promise<any | null> {
  // Check if it's a Webflow site
  const isWebflow = await detectWebflow(domain);

  if (isWebflow) {
    // For Webflow, fetch the product page and parse HTML
    try {
      const res = await fetch(`https://${domain}/${handle}`, {
        headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
      });
      if (res.ok) {
        const html = await res.text();

        // Extract product data from HTML using regex patterns
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        const title = titleMatch ? titleMatch[1].replace(/\s*\|.*$/, '').trim() : handle.replace(/-/g, ' ');

        // Extract description
        const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
        const description = descMatch ? descMatch[1] : '';

        // Extract images with better filtering for Webflow sites
        const images: any[] = [];
        const imgMatches = html.match(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi) || [];

        for (const imgMatch of imgMatches) {
          const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
          const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
          const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

          if (srcMatch && srcMatch[1]) {
            const src = ensureHttps(srcMatch[1]);
            const alt = altMatch ? altMatch[1] : '';
            const classes = classMatch ? classMatch[1] : '';

            // Skip navigation images, icons, logos, and small images
            if (src.includes('icon') || src.includes('logo') || src.includes('favicon') ||
                classes.includes('nav') || classes.includes('menu') || classes.includes('card-product-small') ||
                classes.includes('navbar') || classes.includes('navigation') ||
                src.includes('small') || src.includes('thumbnail') ||
                alt.includes('navigation') || alt.includes('menu') ||
                // Skip images that are clearly from other products
                (!src.toLowerCase().includes(handle.toLowerCase()) &&
                 !alt.toLowerCase().includes(handle.toLowerCase()) &&
                 !classes.includes('hero') && !classes.includes('main') && !classes.includes('primary') &&
                 !classes.includes('large') && !classes.includes('full') &&
                 !src.includes('hero') && !src.includes('main'))) {
              continue;
            }

            // Prioritize images that are likely product images
            const isProductImage = (
              // Hero images
              classes.includes('hero') || src.includes('hero') ||
              // Main product images
              classes.includes('main') || classes.includes('primary') ||
              // Large images
              classes.includes('large') || classes.includes('full') ||
              // Product-specific images (check if filename contains product keywords)
              src.toLowerCase().includes(handle.toLowerCase()) ||
              alt.toLowerCase().includes(handle.toLowerCase()) ||
              // Images in main content areas
              !classes.includes('nav') && !classes.includes('footer') && !classes.includes('header')
            );

            if (isProductImage) {
              images.push({
                src,
                alt: alt || title,
                width: 800, // default
                height: 600, // default
              });
            }
          }
        }

        // If no images found with the above criteria, fall back to all non-navigation images
        if (images.length === 0) {
          for (const imgMatch of imgMatches) {
            const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
            const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
            const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

            if (srcMatch && srcMatch[1]) {
              const src = ensureHttps(srcMatch[1]);
              const alt = altMatch ? altMatch[1] : '';
              const classes = classMatch ? classMatch[1] : '';

              // Skip only the most obvious navigation/UI images
              if (!classes.includes('nav') && !classes.includes('menu') && !classes.includes('card-product-small') &&
                  !src.includes('icon') && !src.includes('logo') && !src.includes('favicon')) {
                images.push({
                  src,
                  alt: alt || title,
                  width: 800,
                  height: 600,
                });
              }
            }
          }
        }

        // Extract price if available
        const priceMatch = html.match(/€\s*(\d+(?:[.,]\d{2})?)/);
        const price = priceMatch ? priceMatch[1].replace(',', '.') : '0';

        return {
          id: handle,
          title,
          handle,
          description,
          images,
          variants: [{
            id: `variant_${handle}`,
            price,
            option1: 'Default',
          }],
          available: true,
          product_type: 'Product',
          tags: [],
          vendor: domain,
        };
      }
    } catch {}
    return null;
  } else {
    // Original Shopify logic
    const url = `https://${domain}/products/${handle}.js`;
    const data = await tryFetchJson(url);

    // Check if we got HTML instead of JSON (modern Shopify stores)
    if (data && typeof data === 'string' && data.includes('<html')) {
      console.warn(`Got HTML response for ${url}, trying HTML scraping for product ${handle}`);
      return await scrapeProductPageHTML(domain, handle);
    }

    if (data && data.title) {
      // Normalize image list - handle different Shopify image formats
      if (Array.isArray(data.images)) {
        // Ensure each image has a proper src_url
        data.images = data.images
          .map((img: any) => {
            if (typeof img === "string") {
              return { src: ensureHttps(img) };
            } else if (img && typeof img === "object") {
              // Handle various Shopify image object formats
              const src = img.src || img.url || img.src_url || img.image_url;
              return {
                ...img,
                src: src ? ensureHttps(src) : "",
                id: img.id || img.image_id || "",
                width: img.width || img.image_width,
                height: img.height || img.image_height,
                alt: img.alt || img.alt_text || img.title || "",
              };
            }
            return img;
          })
          .filter((img: any) => img && (img.src || img.src_url));
      } else if (data.featured_image) {
        // Fallback to featured_image if images array doesn't exist
        const featuredSrc =
          typeof data.featured_image === "string"
            ? data.featured_image
            : data.featured_image.src || data.featured_image.url;
        data.images = [
          {
            src: featuredSrc ? ensureHttps(featuredSrc) : "",
            id: data.featured_image.id || data.id || "",
            width: data.featured_image.width,
            height: data.featured_image.height,
            alt: data.featured_image.alt || data.title || "",
          },
        ];
      } else {
        // No images found
        data.images = [];
      }

      return data;
    }
    return null;
  }
}

// In-memory runners (per-process). Resume is triggered by /resume.
const runners = new Map<string, { stop: () => void }>();

async function startRunner(jobId: string, domain: string, shopId: string) {
  let stopped = false;
  const stop = () => {
    stopped = true;
  };
  runners.set(jobId, { stop });

  const concurrency = 5;
  const scrapedProducts: any[] = [];

  const tick = async () => {
    if (stopped) return;
    // Pull a batch of queued items and process
    const batch = await query<{
      id: string;
      handle: string;
      collection_slug: string;
    }>(
      `UPDATE app.scrape_job_items i
       SET status = 'running', updated_at = now()
       WHERE id IN (
         SELECT id FROM app.scrape_job_items
         WHERE job_id = $1 AND status IN ('queued','failed')
         ORDER BY updated_at ASC
         LIMIT $2
       )
       RETURNING id, handle, collection_slug`,
      [jobId, concurrency]
    );

    if (batch.rowCount === 0) {
      // No more work: mark job complete if nothing left running
      const remaining = await query(
        `SELECT 1 FROM app.scrape_job_items WHERE job_id = $1 AND status IN ('queued','running','failed') LIMIT 1`,
        [jobId]
      );
      if (remaining.rowCount === 0) {
        await query(
          `UPDATE app.scrape_jobs SET status = 'completed', finished_at = now() WHERE id = $1 AND status <> 'canceled'`,
          [jobId]
        );

        // Save scraped products to domain-specific JSON file
        try {
          // Ensure directory exists
          if (!fs.existsSync(SCRAPER_DATA_DIR)) {
            fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
          }

          const domainDataPath = getDomainDataPath(domain);

          // Read existing data for this domain (supports flat array legacy and structured object)
          let existingParsed: any = [];
          if (fs.existsSync(domainDataPath)) {
            const existingContent = fs.readFileSync(domainDataPath, "utf8");
            existingParsed = JSON.parse(existingContent || "[]");
          }

          // Normalize to structured object
          let existingObj: any;
          if (Array.isArray(existingParsed)) {
            existingObj = {
              products: existingParsed,
              collections: [] as any[],
            };
          } else if (existingParsed && typeof existingParsed === "object") {
            existingObj = existingParsed;
            if (!Array.isArray(existingObj.products)) existingObj.products = [];
            if (!Array.isArray(existingObj.collections))
              existingObj.collections = [];
          } else {
            existingObj = { products: [], collections: [] as any[] };
          }

          // Merge new data (avoid duplicates by handle)
          const existingProducts: any[] = Array.isArray(existingObj.products)
            ? existingObj.products
            : [];
          const existingHandles = new Set(
            existingProducts.map((p: any) => p?.handle).filter(Boolean)
          );
          const newProducts = scrapedProducts.filter(
            (p) => !existingHandles.has(p.handle)
          );
          const mergedProducts = [...existingProducts, ...newProducts];

          // Write to domain-specific file as structured object { products, collections }
          try {
            existingObj.products = mergedProducts;
            fs.writeFileSync(
              domainDataPath,
              JSON.stringify(existingObj, null, 2)
            );
          } catch {
            // fallback to flat array if any issue
            fs.writeFileSync(
              domainDataPath,
              JSON.stringify(mergedProducts, null, 2)
            );
          }
          console.log(
            `Saved ${newProducts.length} new products for ${domain} to ${domainDataPath}`
          );
        } catch (error) {
          console.error("Failed to save scraped data to JSON:", error);
        }

        runners.delete(jobId);
        return;
      }
      // Otherwise, wait and retry
      setTimeout(tick, 1000);
      return;
    }

    await Promise.all(
      batch.rows.map(async (row: any) => {
        try {
          const pdata = await fetchProduct(domain, row.handle);
          if (!pdata) throw new Error("no product data");

          // Store product data for JSON export
          scrapedProducts.push({
            id:
              pdata.id?.toString() || `scraped_${Date.now()}_${Math.random()}`,
            title: pdata.title || "",
            handle: pdata.handle || "",
            domain: domain,
            shop_id: shopId,
            scraped_at: new Date().toISOString(),
            // retain collection context of this scrape for per-collection scraped counts
            collections: row.collection_slug ? [row.collection_slug] : [],
            images: Array.isArray(pdata.images)
              ? pdata.images
                  .map((img: any) => ({
                    id: img.id?.toString() || "",
                    src_url: img.src || "",
                    width: img.width,
                    height: img.height,
                    alt: img.alt,
                  }))
                  .filter((img) => img.src_url)
              : [],
            variants: Array.isArray(pdata.variants)
              ? pdata.variants.map((v: any) => ({
                  id: v.id?.toString() || "",
                  sku: v.sku,
                  price: v.price?.toString() || "0",
                  option1: v.option1,
                  option2: v.option2,
                  option3: v.option3,
                }))
              : [],
            raw_json: { ...pdata, shop_id: shopId, domain: domain },
          });

          await query(
            `UPDATE app.scrape_job_items SET status = 'done', attempts = attempts + 1, updated_at = now(), error = NULL WHERE id = $1`,
            [row.id]
          );
        } catch (err: any) {
          await query(
            `UPDATE app.scrape_job_items SET status = 'failed', attempts = attempts + 1, updated_at = now(), error = $2 WHERE id = $1`,
            [row.id, String(err?.message || err)]
          );
        }
      })
    );

    setTimeout(tick, 50);
  };

  setTimeout(tick, 10);
  return stop;
}

export async function scraperRoutes(app: FastifyInstance) {
  // GET /scraper/collections?domain=x&limit=25&offset=0&fast=1
  app.get("/scraper/collections", async (req, reply) => {
    const q = (req.query as any) || {};
    const domain = String(q.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    if (!domain)
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "domain required" } });

    // Helper: map common DB-down errors to a friendly 503 payload
    const toDbDown = (err: any) => {
      const msg = String(err?.message || err || "");
      const code = (err as any)?.code || "";
      const isDown =
        code === "ECONNREFUSED" ||
        code === "ECONNRESET" ||
        msg.includes("ECONNREFUSED") ||
        msg.includes("ECONNRESET");
      if (isDown) {
        return reply.code(503).send({
          error: {
            code: "db_unavailable",
            message: "Database is not reachable. Start Postgres and retry.",
            hint: "cd api && docker compose up -d  &&  pnpm migrate",
          },
        });
      }
      throw err;
    };

    let shop: { id: string; domain: string } | { id?: string; domain: string };
    try {
      shop = await ensureShop(domain);
    } catch (e) {
      // If DB is down but fast mode requested, proceed without DB (no upserts/counts)
      const fastFlag = q.fast == null ? true : String(q.fast) !== "0";
      if (fastFlag) {
        shop = { domain };
      } else {
        return toDbDown(e);
      }
    }

    const limit = q.limit != null ? Number(q.limit) : undefined;
    const offset = q.offset != null ? Number(q.offset) : undefined;
    const fast = q.fast == null ? true : String(q.fast) !== "0";
    const debug = String((q as any).debug || "") === "1";
    const refresh = String((q as any).refresh || "") === "1";
    const debugNotes: string[] = [];
    if (debug) {
      debugNotes.push(
        `collections request: domain=${domain}, limit=${limit}, offset=${offset}, fast=${fast}, refresh=${refresh}`
      );
    }

    const cols = await discoverCollections(domain, { limit, offset, fast });

    // Compute total available collections quickly from the public endpoint (fast mode)
    let totalCollections = Array.isArray(cols) ? cols.length : 0;
    try {
      const base = `https://${domain}`;
      const dataFast = await tryFetchJson(`${base}/collections.json?limit=250`);
      if (dataFast && Array.isArray(dataFast.collections)) {
        totalCollections = dataFast.collections.length;
      }
    } catch {}

    // Load domain JSON to compute per-collection scraped counts and persist collections
    const domainDataPath = getDomainDataPath(domain);
    let savedHandles = new Set<string>();
    let existingObj: any = { products: [], collections: [] };
    try {
      if (fs.existsSync(domainDataPath)) {
        const raw = fs.readFileSync(domainDataPath, "utf8");
        const parsed = JSON.parse(raw || "[]");
        if (Array.isArray(parsed)) {
          existingObj.products = parsed;
          existingObj.collections = [];
        } else if (parsed && typeof parsed === "object") {
          existingObj = {
            products: parsed.products || [],
            collections: parsed.collections || [],
          };
        }
        for (const p of existingObj.products) {
          if (p && typeof p === "object" && p.handle)
            savedHandles.add(String(p.handle));
        }
      }
    } catch {}

    // Upsert returned slice (idempotent)
    try {
      if ((shop as any).id) {
        for (const c of cols) {
          await query(
            `INSERT INTO app.collections (shop_id, slug, title) VALUES ($1,$2,$3)
                        ON CONFLICT (shop_id, slug) DO UPDATE SET title = excluded.title, updated_at = now()`,
            [(shop as any).id, c.slug, c.title]
          );
        }
      }
    } catch (e) {
      // If DB is unavailable and we don't have a shop id, skip upsert in fast mode
      const fastFlag = q.fast == null ? true : String(q.fast) !== "0";
      if (!fastFlag) return toDbDown(e);
    }

    // Get accurate counts from database for the returned collections
    let collectionsWithCounts = cols;
    if (!fast && (shop as any).id) {
      try {
        const slugs = cols.map((c) => c.slug);
        if (slugs.length > 0) {
          const countsResult = await query(
            `SELECT c.slug, COUNT(pc.product_id) as count
             FROM app.collections c
             LEFT JOIN app.product_collections pc ON c.id = pc.collection_id
             WHERE c.shop_id = $1 AND c.slug = ANY($2)
             GROUP BY c.id, c.slug`,
            [(shop as any).id, slugs]
          );

          const countMap = new Map(
            countsResult.rows.map((row: any) => [row.slug, parseInt(row.count)])
          );

          collectionsWithCounts = cols.map((c) => ({
            ...c,
            count: countMap.get(c.slug) || c.count,
          }));
        }
      } catch (e) {
        console.warn("Failed to get database counts, using API counts:", e);
      }
    }

    // Enhance collections with available_count and scraped_count and persist to per-domain JSON
    // Use TTL caching unless refresh=1 is passed
    const TTL_MS = Number(
      process.env.SCRAPER_COUNTS_TTL_MS || 6 * 60 * 60 * 1000
    ); // 6h default
    const nowMs = Date.now();

    let enhanced: any[] = [];
    try {
      const existingBySlug = new Map<string, any>(
        (existingObj.collections || []).map((x: any) => [x.slug, x])
      );
      for (const c of collectionsWithCounts) {
        const prev = existingBySlug.get(c.slug);
        const prevUpdated = prev?.updated_at ? Date.parse(prev.updated_at) : 0;
        const fresh =
          !refresh &&
          prev &&
          Number.isFinite(prevUpdated) &&
          nowMs - prevUpdated < TTL_MS;

        if (fresh && (prev.available_count != null || prev.count != null)) {
          const available_count = Number(
            prev.available_count ?? prev.count ?? 0
          );
          const scraped_count = Number(prev.scraped_count ?? 0);
          enhanced.push({
            ...c,
            available_count,
            scraped_count,
            updated_at: prev.updated_at,
          });
          continue;
        }

        // Recompute counts
        let available_count = 0;
        let scraped_count = 0;
        if (!fast) {
          try {
            const handles = await fetchCollectionHandles(domain, c.slug);
            available_count = handles.length;
            scraped_count = handles.reduce(
              (acc, h) => acc + (savedHandles.has(h) ? 1 : 0),
              0
            );
          } catch {}
        }
        const now = "2023-01-01T00:00:00.000Z";
        enhanced.push({
          ...c,
          available_count,
          scraped_count,
          updated_at: now,
        });
      }

      // Merge into existing JSON structure and persist
      const bySlug = new Map<string, any>(
        (existingObj.collections || []).map((x: any) => [x.slug, x])
      );
      for (const ec of enhanced) {
        const prev = bySlug.get(ec.slug) || null;
        bySlug.set(ec.slug, { ...(prev || {}), ...ec });
      }
      existingObj.collections = Array.from(bySlug.values());
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
      }
      fs.writeFileSync(domainDataPath, JSON.stringify(existingObj, null, 2));
    } catch (e) {
      // Persisting collections is best-effort; continue even if it fails
      console.warn("Failed to persist enhanced collections JSON:", e);
    }

    const payload: any = {
      shop,
      collections: enhanced.length ? enhanced : collectionsWithCounts,
      page: { limit, offset },
    };
    payload.total = totalCollections;
    if (debug) {
      payload.debug = {
        domain,
        limit,
        offset,
        fast,
        notes: debugNotes,
      };
      try {
        console.log(
          "[/scraper/collections][debug]",
          JSON.stringify(payload.debug)
        );
      } catch {}
    }
    return reply.send(payload);
  });

  // POST /scraper/jobs { domain, selections: [{ slug }], per_collection_limit?: number }
  app.post("/scraper/jobs", async (req, reply) => {
    const body = (req.body as any) || {};
    const domain = String(body.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    const selections: Array<{ slug: string }> = Array.isArray(body.selections)
      ? body.selections
      : [];
    const perLimitRaw = body.per_collection_limit;
    let perCollectionLimit: number | undefined = undefined;
    if (perLimitRaw != null) {
      const n = Number(perLimitRaw);
      if (Number.isFinite(n) && n > 0) perCollectionLimit = Math.floor(n);
    }
    if (!domain || selections.length === 0)
      return reply.code(400).send({
        error: {
          code: "bad_request",
          message: "domain and selections required",
        },
      });

    // Helper for DB-down errors
    const toDbDown = (err: any) => {
      const msg = String(err?.message || err || "");
      const code = (err as any)?.code || "";
      const isDown =
        code === "ECONNREFUSED" ||
        code === "ECONNRESET" ||
        msg.includes("ECONNREFUSED") ||
        msg.includes("ECONNRESET");
      if (isDown) {
        return reply.code(503).send({
          error: {
            code: "db_unavailable",
            message: "Database is not reachable. Start Postgres and retry.",
            hint: "cd api && docker compose up -d  &&  pnpm migrate",
          },
        });
      }
      throw err;
    };

    let shop: { id: string; domain: string };
    try {
      shop = await ensureShop(domain);
    } catch (e) {
      return toDbDown(e);
    }

    let jobId: string;
    try {
      const ins = await query<{ id: string }>(
        `INSERT INTO app.scrape_jobs (shop_id, domain, mode, status, config_json)
         VALUES ($1, $2, 'public', 'queued', $3) RETURNING id`,
        [
          shop.id,
          domain,
          { selections, per_collection_limit: perCollectionLimit } as any,
        ]
      );
      jobId = ins.rows[0].id;
    } catch (e) {
      return toDbDown(e);
    }

    // Expand selections into handles and enqueue items
    try {
      for (const sel of selections) {
        const handles = await fetchCollectionHandles(
          domain,
          sel.slug,
          perCollectionLimit
        );
        for (const h of handles) {
          await query(
            `INSERT INTO app.scrape_job_items (job_id, collection_slug, handle)
             VALUES ($1, $2, $3)
             ON CONFLICT (job_id, collection_slug, handle) DO NOTHING`,
            [jobId, sel.slug, h]
          );
        }
      }
    } catch (e) {
      return toDbDown(e);
    }

    // Start runner immediately
    try {
      await query(
        `UPDATE app.scrape_jobs SET status = 'running', started_at = now() WHERE id = $1`,
        [jobId]
      );
    } catch (e) {
      return toDbDown(e);
    }
    await startRunner(jobId, domain, shop.id);

    return reply.send({ job_id: jobId, shop, started: true });
  });

  // GET /scraper/jobs/:id/status
  app.get("/scraper/jobs/:id/status", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    const job = await query(
      `SELECT id, shop_id, domain, status, created_at, started_at, finished_at FROM app.scrape_jobs WHERE id = $1`,
      [jobId]
    );
    if (job.rowCount === 0)
      return reply.code(404).send({ error: { code: "not_found" } });
    const counts = await query(
      `SELECT status, COUNT(*)::int AS c FROM app.scrape_job_items WHERE job_id = $1 GROUP BY status`,
      [jobId]
    );
    const byStatus: Record<string, number> = {};
    for (const r of counts.rows as any[]) byStatus[r.status] = r.c;
    return reply.send({ job: job.rows[0], counts: byStatus });
  });

  // POST /scraper/jobs/:id/pause
  app.post("/scraper/jobs/:id/pause", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    await query(`UPDATE app.scrape_jobs SET status = 'paused' WHERE id = $1`, [
      jobId,
    ]);
    const r = runners.get(jobId);
    if (r) r.stop();
    runners.delete(jobId);
    return reply.send({ ok: true });
  });

  // POST /scraper/jobs/:id/resume
  app.post("/scraper/jobs/:id/resume", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    const job = await query(
      `SELECT id, domain, shop_id FROM app.scrape_jobs WHERE id = $1`,
      [jobId]
    );
    if (job.rowCount === 0)
      return reply.code(404).send({ error: { code: "not_found" } });
    await query(`UPDATE app.scrape_jobs SET status = 'running' WHERE id = $1`, [
      jobId,
    ]);
    await startRunner(
      jobId,
      (job.rows[0] as any).domain,
      (job.rows[0] as any).shop_id
    );
    return reply.send({ ok: true });
  });

  // POST /scraper/jobs/:id/cancel
  app.post("/scraper/jobs/:id/cancel", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    await query(
      `UPDATE app.scrape_jobs SET status = 'canceled', finished_at = now() WHERE id = $1`,
      [jobId]
    );
    const r = runners.get(jobId);
    if (r) r.stop();
    runners.delete(jobId);
    await query(
      `UPDATE app.scrape_job_items SET status = 'canceled', updated_at = now() WHERE job_id = $1 AND status IN ('queued','running')`,
      [jobId]
    );
    return reply.send({ ok: true });
  });

  // GET /scraper/shops - list known shops
  app.get("/scraper/shops", async (_req, reply) => {
    const rows = await query<{ id: string; domain: string }>(
      `SELECT id, domain FROM app.shops ORDER BY created_at DESC NULLS LAST, domain ASC`
    );
    return reply.send({ items: rows.rows });
  });

  // POST /scraper/save-collections - save collections with counts to JSON file for a store
  app.post("/scraper/save-collections", async (req, reply) => {
    const body = (req.body as any) || {};
    const shopId = String(body.shop_id || "").trim();

    if (!shopId) {
      return reply.code(400).send({
        error: { code: "bad_request", message: "shop_id required" },
      });
    }

    try {
      // Get shop domain for filename
      const shopResult = await query<{ domain: string }>(
        `SELECT domain FROM app.shops WHERE id = $1`,
        [shopId]
      );

      if (shopResult.rowCount === 0) {
        return reply.code(404).send({
          error: { code: "not_found", message: "Shop not found" },
        });
      }

      const domain = shopResult.rows[0].domain;

      // Get collections with product counts
      const collectionsResult = await query(
        `SELECT c.slug, c.title,
                COUNT(pc.product_id) as count
         FROM app.collections c
         LEFT JOIN app.product_collections pc ON c.id = pc.collection_id
         WHERE c.shop_id = $1
         GROUP BY c.id, c.slug, c.title
         ORDER BY c.title ASC`,
        [shopId]
      );

      const collectionsData = {
        shop_id: shopId,
        domain: domain,
        collections: collectionsResult.rows,
        saved_at: new Date().toISOString(),
      };

      // Create collections directory if it doesn't exist
      const collectionsDir = path.join(__dirname, "../../../data/collections");
      if (!fs.existsSync(collectionsDir)) {
        fs.mkdirSync(collectionsDir, { recursive: true });
      }

      // Save to JSON file named after the domain
      const filename = `${domain.replace(
        /[^a-zA-Z0-9]/g,
        "_"
      )}_collections.json`;
      const filepath = path.join(collectionsDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(collectionsData, null, 2));

      console.log(
        `Saved ${collectionsResult.rows.length} collections for ${domain} to ${filepath}`
      );

      return reply.send({
        success: true,
        filename: filename,
        collections_count: collectionsResult.rows.length,
        filepath: filepath,
      });
    } catch (error) {
      console.error("Failed to save collections:", error);
      return reply.code(500).send({ error: "Failed to save collections" });
    }
  });

  // GET /scraper/domains - list all available domain data files
  app.get("/scraper/domains", async (_req, reply) => {
    try {
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        return reply.send({ domains: [] });
      }

      const entries = fs.readdirSync(SCRAPER_DATA_DIR);
      const domains = entries
        .filter((name) => {
          const p = path.join(SCRAPER_DATA_DIR, name);
          return (
            name.endsWith(".json") &&
            fs.statSync(p).isFile() &&
            !name.endsWith("_collections.json")
          );
        })
        .map((file) => {
          const p = path.join(SCRAPER_DATA_DIR, file);
          let count = 0;
          try {
            const txt = fs.readFileSync(p, "utf8") || "[]";
            const parsed = JSON.parse(txt || "[]");
            if (Array.isArray(parsed)) {
              count = parsed.length;
            } else if (
              parsed &&
              typeof parsed === "object" &&
              Array.isArray(parsed.products)
            ) {
              count = parsed.products.length;
            } else {
              count = 0;
            }
          } catch {}
          const stat = fs.statSync(p);
          return {
            domain: file.replace(/\.json$/, ""),
            filename: file,
            product_count: count,
            file_size: stat.size,
            last_modified: stat.mtime.toISOString(),
          };
        });

      return reply.send({ domains });
    } catch (err) {
      console.error("Failed to list domains:", err);
      return reply.code(500).send({ error: "Failed to list domains" });
    }
  });

  // GET /scraper/collections-saved?domain=x
  app.get("/scraper/collections-saved", async (req, reply) => {
    const q = (req.query as any) || {};
    const domain = String(q.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    if (!domain)
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "domain required" } });
    const domainDataPath = getDomainDataPath(domain);
    try {
      if (!fs.existsSync(domainDataPath))
        return reply.send({ collections: [], total: 0 });
      const raw = fs.readFileSync(domainDataPath, "utf8");
      const parsed = JSON.parse(raw || "[]");
      const collections = Array.isArray(parsed)
        ? []
        : Array.isArray(parsed.collections)
        ? parsed.collections
        : [];
      return reply.send({ collections, total: collections.length });
    } catch (e) {
      console.error("Failed to load saved collections:", e);
      return reply
        .code(500)
        .send({ error: "Failed to load saved collections" });
    }
  });

  // POST /scraper/domains - create an empty domain data file
  app.post("/scraper/domains", async (req, reply) => {
    try {
      const body = (req.body as any) || {};
      const raw = String(body.domain || "")
        .trim()
        .replace(/^https?:\/\//, "")
        .replace(/\/+$/, "");
      if (!raw) {
        return reply
          .code(400)
          .send({ error: { code: "bad_request", message: "domain required" } });
      }
      const filePath = getDomainDataPath(raw);
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
      }
      if (!fs.existsSync(filePath)) {
        const empty = { products: [], collections: [] as any[] };
        fs.writeFileSync(filePath, JSON.stringify(empty, null, 2));
      }
      return reply.send({
        ok: true,
        domain: raw,
        filename: path.basename(filePath),
      });
    } catch (err) {
      console.error("Failed to create domain file:", err);
      return reply.code(500).send({ error: "Failed to create domain file" });
    }
  });

  // GET /scraper/data - serve the scraped data JSON file for a specific domain/shop
  app.get("/scraper/data", async (req, reply) => {
    const q = (req.query as any) || {};
    const shopId = String(q.shop_id || "").trim();
    const domain = String(q.domain || "").trim();

    try {
      let targetDomain = domain;
      let targetShopId = shopId;

      // If shop_id is provided but no domain, look up the domain from database
      if (shopId && !domain) {
        const shopResult = await query<{ domain: string }>(
          `SELECT domain FROM app.shops WHERE id = $1`,
          [shopId]
        );
        if (shopResult.rowCount > 0) {
          targetDomain = shopResult.rows[0].domain;
          targetShopId = shopId;
        } else {
          return reply
            .code(404)
            .send({ error: { code: "not_found", message: "Shop not found" } });
        }
      }

      // If domain is provided but no shop_id, look up the shop_id from database
      if (domain && !shopId) {
        const shopResult = await query<{ id: string }>(
          `SELECT id FROM app.shops WHERE domain = $1`,
          [domain]
        );
        if (shopResult.rowCount > 0) {
          targetShopId = shopResult.rows[0].id;
        }
      }

      if (!targetDomain) {
        return reply.code(400).send({
          error: {
            code: "bad_request",
            message: "domain or shop_id parameter required",
          },
        });
      }

      const domainDataPath = getDomainDataPath(targetDomain);

      if (fs.existsSync(domainDataPath)) {
        const raw = fs.readFileSync(domainDataPath, "utf8");
        const parsed = JSON.parse(raw || "[]");
        let products: any[] = [];
        if (Array.isArray(parsed)) {
          products = parsed;
        } else if (
          parsed &&
          typeof parsed === "object" &&
          Array.isArray(parsed.products)
        ) {
          products = parsed.products;
        }

        // Filter products by shop_id if provided (additional safety check)
        if (targetShopId) {
          products = products.filter((product: any) => {
            return (
              product.shop_id === targetShopId ||
              (product.raw_json && product.raw_json.shop_id === targetShopId)
            );
          });
        }

        return reply.send(products);
      } else {
        return reply.send([]);
      }
    } catch (error) {
      console.error("Failed to read scraped data:", error);
      return reply.code(500).send({ error: "Failed to read scraped data" });
    }
  });
}
