import { FastifyInstance } from "fastify";
import { query } from "../db";
import fs from "fs";
import path from "path";
import { SCRAPER_DATA_DIR, getDomainDataPath, ensureShop, discoverCollections, fetchCollectionHandles } from "./scraper-utils";
import { startRunner, runners } from "./scraper-runner";

export function registerScraperHandlers(app: FastifyInstance) {
  // GET /scraper/collections?domain=x&limit=25&offset=0&fast=1
  app.get("/scraper/collections", async (req, reply) => {
    const q = (req.query as any) || {};
    const domain = String(q.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    if (!domain)
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "domain required" } });

    // Helper: map common DB-down errors to a friendly 503 payload
    const toDbDown = (err: any) => {
      const msg = String(err?.message || err || "").toLowerCase();
      const code = (err as any)?.code || "";
      const isDown =
        code === "ECONNREFUSED" ||
        code === "ECONNRESET" ||
        code === "ENOTFOUND" ||
        code === "ETIMEDOUT" ||
        msg.includes("econnrefused") ||
        msg.includes("econnreset") ||
        msg.includes("connection terminated due to connection timeout") ||
        msg.includes("connection timeout") ||
        msg.includes("server closed the connection") ||
        msg.includes("connection terminated unexpectedly") ||
        msg.includes("connection lost") ||
        msg.includes("connection reset by peer") ||
        msg.includes("broken pipe") ||
        msg.includes("database system is shutting down") ||
        msg.includes("the database system is starting up") ||
        msg.includes("connection to server was lost") ||
        msg.includes("could not connect to server");
      if (isDown) {
        console.error("Database connection error detected:", { code, message: msg });
        return reply.code(503).send({
          error: {
            code: "db_unavailable",
            message: "Database is not reachable. Start Postgres and retry.",
            hint: "cd api && docker compose up -d  &&  pnpm migrate",
          },
        });
      }
      throw err;
    };

    let shop: { id: string; domain: string } | { id?: string; domain: string };
    try {
      shop = await ensureShop(domain);
    } catch (e) {
      // If DB is down but fast mode requested, proceed without DB (no upserts/counts)
      const fastFlag = q.fast == null ? true : String(q.fast) !== "0";
      if (fastFlag) {
        shop = { domain };
      } else {
        return toDbDown(e);
      }
    }

    const limit = q.limit != null ? Number(q.limit) : undefined;
    const offset = q.offset != null ? Number(q.offset) : undefined;
    const fast = q.fast == null ? true : String(q.fast) !== "0";
    const debug = String((q as any).debug || "") === "1";
    const refresh = String((q as any).refresh || "") === "1";
    const debugNotes: string[] = [];
    if (debug) {
      debugNotes.push(
        `collections request: domain=${domain}, limit=${limit}, offset=${offset}, fast=${fast}, refresh=${refresh}`
      );
    }

    const cols = await discoverCollections(domain, { limit, offset, fast });

    // Compute total available collections quickly from the public endpoint (fast mode)
    let totalCollections = Array.isArray(cols) ? cols.length : 0;
    try {
      const base = `https://${domain}`;
      const dataFast = await fetch(`${base}/collections.json?limit=250`, {
        headers: {
          "user-agent":
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0 Safari/537.36",
          accept: "application/json, text/plain, */*",
          "accept-language": "en-US,en;q=0.9",
        },
      });
      if (dataFast.ok) {
        const json = await dataFast.json();
        if (json && Array.isArray(json.collections)) {
          totalCollections = json.collections.length;
        }
      }
    } catch {}

    // Load domain JSON to compute per-collection scraped counts and persist collections
    const domainDataPath = getDomainDataPath(domain);
    let savedHandles = new Set<string>();
    let existingObj: any = { products: [], collections: [] };
    try {
      if (fs.existsSync(domainDataPath)) {
        const raw = fs.readFileSync(domainDataPath, "utf8");
        const parsed = JSON.parse(raw || "[]");
        if (Array.isArray(parsed)) {
          existingObj.products = parsed;
          existingObj.collections = [];
        } else if (parsed && typeof parsed === "object") {
          existingObj = {
            products: parsed.products || [],
            collections: parsed.collections || [],
          };
        }
        for (const p of existingObj.products) {
          if (p && typeof p === "object" && p.handle)
            savedHandles.add(String(p.handle));
        }
      }
    } catch {}

    // Upsert returned slice (idempotent)
    try {
      if ((shop as any).id) {
        for (const c of cols) {
          await query(
            `INSERT INTO app.collections (shop_id, slug, title) VALUES ($1,$2,$3)
                         ON CONFLICT (shop_id, slug) DO UPDATE SET title = excluded.title, updated_at = now()`,
            [(shop as any).id, c.slug, c.title]
          );
        }
      }
    } catch (e) {
      // If DB is unavailable and we don't have a shop id, skip upsert in fast mode
      const fastFlag = q.fast == null ? true : String(q.fast) !== "0";
      if (!fastFlag) return toDbDown(e);
    }

    // Get accurate counts from database for the returned collections
    let collectionsWithCounts = cols;
    if (!fast && (shop as any).id) {
      try {
        const slugs = cols.map((c) => c.slug);
        if (slugs.length > 0) {
          const countsResult = await query(
            `SELECT c.slug, COUNT(pc.product_id) as count
             FROM app.collections c
             LEFT JOIN app.product_collections pc ON c.id = pc.collection_id
             WHERE c.shop_id = $1 AND c.slug = ANY($2)
             GROUP BY c.id, c.slug`,
            [(shop as any).id, slugs]
          );

          const countMap = new Map(
            countsResult.rows.map((row: any) => [row.slug, parseInt(row.count)])
          );

          collectionsWithCounts = cols.map((c) => ({
            ...c,
            count: countMap.get(c.slug) || c.count,
          }));
        }
      } catch (e) {
        console.warn("Failed to get database counts, using API counts:", e);
      }
    }

    // Enhance collections with available_count and scraped_count and persist to per-domain JSON
    // Use TTL caching unless refresh=1 is passed
    const TTL_MS = Number(
      process.env.SCRAPER_COUNTS_TTL_MS || 6 * 60 * 60 * 1000
    ); // 6h default
    const nowMs = Date.now();

    let enhanced: any[] = [];
    try {
      const existingBySlug = new Map<string, any>(
        (existingObj.collections || []).map((x: any) => [x.slug, x])
      );
      for (const c of collectionsWithCounts) {
        const prev = existingBySlug.get(c.slug);
        const prevUpdated = prev?.updated_at ? Date.parse(prev.updated_at) : 0;
        const fresh =
          !refresh &&
          prev &&
          Number.isFinite(prevUpdated) &&
          nowMs - prevUpdated < TTL_MS;

        if (fresh && (prev.available_count != null || prev.count != null)) {
          const available_count = Number(
            prev.available_count ?? prev.count ?? 0
          );
          const scraped_count = Number(prev.scraped_count ?? 0);
          enhanced.push({
            ...c,
            available_count,
            scraped_count,
            updated_at: prev.updated_at,
          });
          continue;
        }

        // Recompute counts
        let available_count = 0;
        let scraped_count = 0;
        if (!fast) {
          try {
            const handles = await fetchCollectionHandles(domain, c.slug);
            available_count = handles.length;
            scraped_count = handles.reduce(
              (acc, h) => acc + (savedHandles.has(h) ? 1 : 0),
              0
            );
          } catch {}
        }
        const now = "2023-01-01T00:00:00.000Z";
        enhanced.push({
          ...c,
          available_count,
          scraped_count,
          updated_at: now,
        });
      }

      // Merge into existing JSON structure and persist
      const bySlug = new Map<string, any>(
        (existingObj.collections || []).map((x: any) => [x.slug, x])
      );
      for (const ec of enhanced) {
        const prev = bySlug.get(ec.slug) || null;
        bySlug.set(ec.slug, { ...(prev || {}), ...ec });
      }
      existingObj.collections = Array.from(bySlug.values());
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
      }
      fs.writeFileSync(domainDataPath, JSON.stringify(existingObj, null, 2));
    } catch (e) {
      // Persisting collections is best-effort; continue even if it fails
      console.warn("Failed to persist enhanced collections JSON:", e);
    }

    const payload: any = {
      shop,
      collections: enhanced.length ? enhanced : collectionsWithCounts,
      page: { limit, offset },
    };
    payload.total = totalCollections;
    if (debug) {
      payload.debug = {
        domain,
        limit,
        offset,
        fast,
        notes: debugNotes,
      };
      try {
        console.log(
          "[/scraper/collections][debug]",
          JSON.stringify(payload.debug)
        );
      } catch {}
    }
    return reply.send(payload);
  });

  // POST /scraper/jobs { domain, selections: [{ slug }], per_collection_limit?: number }
  app.post("/scraper/jobs", async (req, reply) => {
    const body = (req.body as any) || {};
    const domain = String(body.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    const selections: Array<{ slug: string }> = Array.isArray(body.selections)
      ? body.selections
      : [];
    const perLimitRaw = body.per_collection_limit;
    let perCollectionLimit: number | undefined = undefined;
    if (perLimitRaw != null) {
      const n = Number(perLimitRaw);
      if (Number.isFinite(n) && n > 0) perCollectionLimit = Math.floor(n);
    }
    if (!domain || selections.length === 0)
      return reply.code(400).send({
        error: {
          code: "bad_request",
          message: "domain and selections required",
        },
      });

    // Helper for DB-down errors
    const toDbDown = (err: any) => {
      const msg = String(err?.message || err || "").toLowerCase();
      const code = (err as any)?.code || "";
      const isDown =
        code === "ECONNREFUSED" ||
        code === "ECONNRESET" ||
        code === "ENOTFOUND" ||
        code === "ETIMEDOUT" ||
        msg.includes("econnrefused") ||
        msg.includes("econnreset") ||
        msg.includes("connection terminated due to connection timeout") ||
        msg.includes("connection timeout") ||
        msg.includes("server closed the connection") ||
        msg.includes("connection terminated unexpectedly") ||
        msg.includes("connection lost") ||
        msg.includes("connection reset by peer") ||
        msg.includes("broken pipe") ||
        msg.includes("database system is shutting down") ||
        msg.includes("the database system is starting up") ||
        msg.includes("connection to server was lost") ||
        msg.includes("could not connect to server");
      if (isDown) {
        console.error("Database connection error detected:", { code, message: msg });
        return reply.code(503).send({
          error: {
            code: "db_unavailable",
            message: "Database is not reachable. Start Postgres and retry.",
            hint: "cd api && docker compose up -d  &&  pnpm migrate",
          },
        });
      }
      throw err;
    };

    let shop: { id: string; domain: string };
    try {
      shop = await ensureShop(domain);
    } catch (e) {
      return toDbDown(e);
    }

    let jobId: string;
    try {
      const ins = await query<{ id: string }>(
        `INSERT INTO app.scrape_jobs (shop_id, domain, mode, status, config_json)
         VALUES ($1, $2, 'public', 'queued', $3) RETURNING id`,
        [
          shop.id,
          domain,
          { selections, per_collection_limit: perCollectionLimit } as any,
        ]
      );
      jobId = ins.rows[0].id;
    } catch (e) {
      return toDbDown(e);
    }

    // Expand selections into handles and enqueue items
    try {
      for (const sel of selections) {
        const handles = await fetchCollectionHandles(
          domain,
          sel.slug,
          perCollectionLimit
        );
        for (const h of handles) {
          await query(
            `INSERT INTO app.scrape_job_items (job_id, collection_slug, handle)
             VALUES ($1, $2, $3)
             ON CONFLICT (job_id, collection_slug, handle) DO NOTHING`,
            [jobId, sel.slug, h]
          );
        }
      }
    } catch (e) {
      return toDbDown(e);
    }

    // Start runner immediately
    try {
      await query(
        `UPDATE app.scrape_jobs SET status = 'running', started_at = now() WHERE id = $1`,
        [jobId]
      );
    } catch (e) {
      return toDbDown(e);
    }
    await startRunner(jobId, domain, shop.id);

    return reply.send({ job_id: jobId, shop, started: true });
  });

  // GET /scraper/jobs/:id/status
  app.get("/scraper/jobs/:id/status", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    const job = await query(
      `SELECT id, shop_id, domain, status, created_at, started_at, finished_at FROM app.scrape_jobs WHERE id = $1`,
      [jobId]
    );
    if (job.rowCount === 0)
      return reply.code(404).send({ error: { code: "not_found" } });
    const counts = await query(
      `SELECT status, COUNT(*)::int AS c FROM app.scrape_job_items WHERE job_id = $1 GROUP BY status`,
      [jobId]
    );
    const byStatus: Record<string, number> = {};
    for (const r of counts.rows as any[]) byStatus[r.status] = r.c;
    return reply.send({ job: job.rows[0], counts: byStatus });
  });

  // POST /scraper/jobs/:id/pause
  app.post("/scraper/jobs/:id/pause", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    await query(`UPDATE app.scrape_jobs SET status = 'paused' WHERE id = $1`, [
      jobId,
    ]);
    const r = runners.get(jobId);
    if (r) r.stop();
    runners.delete(jobId);
    return reply.send({ ok: true });
  });

  // POST /scraper/jobs/:id/resume
  app.post("/scraper/jobs/:id/resume", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    const job = await query(
      `SELECT id, domain, shop_id FROM app.scrape_jobs WHERE id = $1`,
      [jobId]
    );
    if (job.rowCount === 0)
      return reply.code(404).send({ error: { code: "not_found" } });
    await query(`UPDATE app.scrape_jobs SET status = 'running' WHERE id = $1`, [
      jobId,
    ]);
    await startRunner(
      jobId,
      (job.rows[0] as any).domain,
      (job.rows[0] as any).shop_id
    );
    return reply.send({ ok: true });
  });

  // POST /scraper/jobs/:id/cancel
  app.post("/scraper/jobs/:id/cancel", async (req, reply) => {
    const jobId = (req.params as any)?.id;
    await query(
      `UPDATE app.scrape_jobs SET status = 'canceled', finished_at = now() WHERE id = $1`,
      [jobId]
    );
    const r = runners.get(jobId);
    if (r) r.stop();
    runners.delete(jobId);
    await query(
      `UPDATE app.scrape_job_items SET status = 'canceled', updated_at = now() WHERE job_id = $1 AND status IN ('queued','running')`,
      [jobId]
    );
    return reply.send({ ok: true });
  });

  // GET /scraper/shops - list known shops
  app.get("/scraper/shops", async (_req, reply) => {
    const rows = await query<{ id: string; domain: string }>(
      `SELECT id, domain FROM app.shops ORDER BY created_at DESC NULLS LAST, domain ASC`
    );
    return reply.send({ items: rows.rows });
  });

  // POST /scraper/save-collections - save collections with counts to JSON file for a store
  app.post("/scraper/save-collections", async (req, reply) => {
    const body = (req.body as any) || {};
    const shopId = String(body.shop_id || "").trim();

    if (!shopId) {
      return reply.code(400).send({
        error: { code: "bad_request", message: "shop_id required" },
      });
    }

    try {
      // Get shop domain for filename
      const shopResult = await query<{ domain: string }>(
        `SELECT domain FROM app.shops WHERE id = $1`,
        [shopId]
      );

      if (shopResult.rowCount === 0) {
        return reply.code(404).send({
          error: { code: "not_found", message: "Shop not found" },
        });
      }

      const domain = shopResult.rows[0].domain;

      // Get collections with product counts
      const collectionsResult = await query(
        `SELECT c.slug, c.title,
                COUNT(pc.product_id) as count
         FROM app.collections c
         LEFT JOIN app.product_collections pc ON c.id = pc.collection_id
         WHERE c.shop_id = $1
         GROUP BY c.id, c.slug, c.title
         ORDER BY c.title ASC`,
        [shopId]
      );

      const collectionsData = {
        shop_id: shopId,
        domain: domain,
        collections: collectionsResult.rows,
        saved_at: new Date().toISOString(),
      };

      // Create collections directory if it doesn't exist
      const collectionsDir = path.join(__dirname, "../../../data/collections");
      if (!fs.existsSync(collectionsDir)) {
        fs.mkdirSync(collectionsDir, { recursive: true });
      }

      // Save to JSON file named after the domain
      const filename = `${domain.replace(
        /[^a-zA-Z0-9]/g,
        "_"
      )}_collections.json`;
      const filepath = path.join(collectionsDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(collectionsData, null, 2));

      console.log(
        `Saved ${collectionsResult.rows.length} collections for ${domain} to ${filepath}`
      );

      return reply.send({
        success: true,
        filename: filename,
        collections_count: collectionsResult.rows.length,
        filepath: filepath,
      });
    } catch (error) {
      console.error("Failed to save collections:", error);
      return reply.code(500).send({ error: "Failed to save collections" });
    }
  });

  // GET /scraper/domains - list all available domain data files
  app.get("/scraper/domains", async (_req, reply) => {
    try {
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        return reply.send({ domains: [] });
      }

      const entries = fs.readdirSync(SCRAPER_DATA_DIR);
      const domains = entries
        .filter((name) => {
          const p = path.join(SCRAPER_DATA_DIR, name);
          return (
            name.endsWith(".json") &&
            fs.statSync(p).isFile() &&
            !name.endsWith("_collections.json")
          );
        })
        .map((file) => {
          const p = path.join(SCRAPER_DATA_DIR, file);
          let count = 0;
          try {
            const txt = fs.readFileSync(p, "utf8") || "[]";
            const parsed = JSON.parse(txt || "[]");
            if (Array.isArray(parsed)) {
              count = parsed.length;
            } else if (
              parsed &&
              typeof parsed === "object" &&
              Array.isArray(parsed.products)
            ) {
              count = parsed.products.length;
            } else {
              count = 0;
            }
          } catch {}
          const stat = fs.statSync(p);
          return {
            domain: file.replace(/\.json$/, ""),
            filename: file,
            product_count: count,
            file_size: stat.size,
            last_modified: stat.mtime.toISOString(),
          };
        });

      return reply.send({ domains });
    } catch (err) {
      console.error("Failed to list domains:", err);
      return reply.code(500).send({ error: "Failed to list domains" });
    }
  });

  // GET /scraper/collections-saved?domain=x
  app.get("/scraper/collections-saved", async (req, reply) => {
    const q = (req.query as any) || {};
    const domain = String(q.domain || "")
      .replace(/^https?:\/\//, "")
      .replace(/\/$/, "");
    if (!domain)
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "domain required" } });
    const domainDataPath = getDomainDataPath(domain);
    try {
      if (!fs.existsSync(domainDataPath))
        return reply.send({ collections: [], total: 0 });
      const raw = fs.readFileSync(domainDataPath, "utf8");
      const parsed = JSON.parse(raw || "[]");
      const collections = Array.isArray(parsed)
        ? []
        : Array.isArray(parsed.collections)
        ? parsed.collections
        : [];
      return reply.send({ collections, total: collections.length });
    } catch (e) {
      console.error("Failed to load saved collections:", e);
      return reply
        .code(500)
        .send({ error: "Failed to load saved collections" });
    }
  });

  // POST /scraper/domains - create an empty domain data file
  app.post("/scraper/domains", async (req, reply) => {
    try {
      const body = (req.body as any) || {};
      const raw = String(body.domain || "")
        .trim()
        .replace(/^https?:\/\//, "")
        .replace(/\/+$/, "");
      if (!raw) {
        return reply
          .code(400)
          .send({ error: { code: "bad_request", message: "domain required" } });
      }
      const filePath = getDomainDataPath(raw);
      if (!fs.existsSync(SCRAPER_DATA_DIR)) {
        fs.mkdirSync(SCRAPER_DATA_DIR, { recursive: true });
      }
      if (!fs.existsSync(filePath)) {
        const empty = { products: [], collections: [] as any[] };
        fs.writeFileSync(filePath, JSON.stringify(empty, null, 2));
      }
      return reply.send({
        ok: true,
        domain: raw,
        filename: path.basename(filePath),
      });
    } catch (err) {
      console.error("Failed to create domain file:", err);
      return reply.code(500).send({ error: "Failed to create domain file" });
    }
  });

  // GET /scraper/data - serve the scraped data JSON file for a specific domain/shop
  app.get("/scraper/data", async (req, reply) => {
    const q = (req.query as any) || {};
    const shopId = String(q.shop_id || "").trim();
    const domain = String(q.domain || "").trim();

    try {
      let targetDomain = domain;
      let targetShopId = shopId;

      // If shop_id is provided but no domain, look up the domain from database
      if (shopId && !domain) {
        const shopResult = await query<{ domain: string }>(
          `SELECT domain FROM app.shops WHERE id = $1`,
          [shopId]
        );
        if (shopResult.rowCount > 0) {
          targetDomain = shopResult.rows[0].domain;
          targetShopId = shopId;
        } else {
          return reply
            .code(404)
            .send({ error: { code: "not_found", message: "Shop not found" } });
        }
      }

      // If domain is provided but no shop_id, look up the shop_id from database
      if (domain && !shopId) {
        const shopResult = await query<{ id: string }>(
          `SELECT id FROM app.shops WHERE domain = $1`,
          [domain]
        );
        if (shopResult.rowCount > 0) {
          targetShopId = shopResult.rows[0].id;
        }
      }

      if (!targetDomain) {
        return reply.code(400).send({
          error: {
            code: "bad_request",
            message: "domain or shop_id parameter required",
          },
        });
      }

      const domainDataPath = getDomainDataPath(targetDomain);

      if (fs.existsSync(domainDataPath)) {
        const raw = fs.readFileSync(domainDataPath, "utf8");
        const parsed = JSON.parse(raw || "[]");
        let products: any[] = [];
        if (Array.isArray(parsed)) {
          products = parsed;
        } else if (
          parsed &&
          typeof parsed === "object" &&
          Array.isArray(parsed.products)
        ) {
          products = parsed.products;
        }

        // Filter products by shop_id if provided (additional safety check)
        if (targetShopId) {
          products = products.filter((product: any) => {
            return (
              product.shop_id === targetShopId ||
              (product.raw_json && product.raw_json.shop_id === targetShopId)
            );
          });
        }

        return reply.send(products);
      } else {
        return reply.send([]);
      }
    } catch (error) {
      console.error("Failed to read scraped data:", error);
      return reply.code(500).send({ error: "Failed to read scraped data" });
    }
  });
}