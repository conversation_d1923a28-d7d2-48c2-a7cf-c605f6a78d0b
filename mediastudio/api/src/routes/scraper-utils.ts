import { query } from "../db";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
export const SCRAPER_DATA_DIR = path.join(__dirname, "../../../data");

// Helper function to get domain-specific data file path
export function getDomainDataPath(domain: string): string {
  const sanitizedDomain = domain.replace(/[^a-zA-Z0-9.-]/g, "_");
  return path.join(SCRAPER_DATA_DIR, `${sanitizedDomain}.json`);
}

export const DEFAULT_HEADERS = {
  "user-agent":
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0 Safari/537.36",
  accept: "application/json, text/plain, */*",
  "accept-language": "en-US,en;q=0.9",
} as const;

export async function ensureShop(
  domain: string
): Promise<{ id: string; domain: string }> {
  // Upsert by domain
  const sel = await query<{ id: string }>(
    `SELECT id FROM app.shops WHERE domain = $1`,
    [domain]
  );
  if (sel.rowCount && sel.rows[0]) return { id: sel.rows[0].id, domain };
  const ins = await query<{ id: string }>(
    `INSERT INTO app.shops (domain) VALUES ($1) ON CONFLICT (domain) DO UPDATE SET domain = excluded.domain RETURNING id`,
    [domain]
  );
  return { id: ins.rows[0].id, domain };
}

export function ensureHttps(u: string): string {
  if (!u) return u;
  if (u.startsWith("//")) return "https:" + u;
  if (u.startsWith("http://")) return u.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(u)) return "https://" + u.replace(/^\/+/, "");
  return u;
}

export async function tryFetchJson(url: string): Promise<any | null> {
  try {
    const u = new URL(url);
    const res = await fetch(url, {
      headers: { ...DEFAULT_HEADERS, referer: `${u.origin}/` },
    });
    if (res.ok) {
      const contentType = res.headers.get('content-type') || '';
      if (contentType.includes('application/json')) {
        return await res.json();
      } else {
        // If it's not JSON, get the text and return it as a string
        // This allows us to detect HTML responses
        const text = await res.text();
        return text;
      }
    }
  } catch {}
  return null;
}

export async function detectWebflow(domain: string): Promise<boolean> {
  try {
    const res = await fetch(`https://${domain}/`, {
      headers: DEFAULT_HEADERS,
    });
    if (res.ok) {
      const html = await res.text();
      return html.includes('data-wf-site') || html.includes('cdn.prod.website-files.com');
    }
  } catch {}
  return false;
}

export async function scrapeCollectionPageHTML(domain: string, slug: string, limit?: number): Promise<string[]> {
  const handles = new Set<string>();

  try {
    // Try to fetch the collection page
    const collectionUrl = `https://${domain}/collections/${slug}`;
    const res = await fetch(collectionUrl, {
      headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
    });

    if (!res.ok) {
      console.warn(`Failed to fetch collection page: ${collectionUrl}`);
      return [];
    }

    const html = await res.text();

    // Look for product links in the HTML
    // Enhanced patterns for Shopify product links, including Fashion Nova specifics
    const productLinkPatterns = [
      /href=["']\/products\/([^"'\?#]+)["']/gi,
      /href=["']([^"']*\/products\/[^"'\?#]+)["']/gi,
      /data-product-handle=["']([^"']+)["']/gi,
      /data-handle=["']([^"']+)["']/gi,
      // Fashion Nova specific patterns
      /href=["']\/collections\/[^\/]+\/products\/([^"'\?#]+)["']/gi,
      /href=["']([^"']*\/collections\/[^\/]+\/products\/[^"'\?#]+)["']/gi,
      // Generic product URL patterns
      /\/products\/([^\/\?#"'\s]+)/gi,
      // JSON data in script tags
      /"handle"\s*:\s*"([^"]+)"/gi,
      /"product_handle"\s*:\s*"([^"]+)"/gi,
    ];

    for (const pattern of productLinkPatterns) {
      let match;
      while ((match = pattern.exec(html)) !== null) {
        let handle = match[1];

        // Clean up the handle if it contains full URL
        if (handle.includes('/products/')) {
          handle = handle.split('/products/')[1].split('/')[0].split('?')[0].split('#')[0];
        } else if (handle.includes('/collections/')) {
          // Handle collection/product URLs
          const parts = handle.split('/products/');
          if (parts.length > 1) {
            handle = parts[1].split('/')[0].split('?')[0].split('#')[0];
          }
        }

        // Clean up the handle - remove any remaining path separators, query params, etc.
        handle = handle.replace(/^[\/]+|[\/\?\#].*$/g, '');

        if (handle && handle.length > 2 && handle.length < 100 && /^[a-zA-Z0-9_-]+$/.test(handle)) {
          handles.add(handle);
          console.log(`[HTML Scraping] Found product handle: ${handle} (pattern: ${pattern.source})`);
          if (limit && handles.size >= limit) {
            return Array.from(handles);
          }
        }
      }
    }

    // Also try to find JSON-LD structured data
    const jsonLdMatch = html.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]+)<\/script>/gi);
    if (jsonLdMatch) {
      for (const script of jsonLdMatch) {
        try {
          const jsonContent = script.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
          const data = JSON.parse(jsonContent);
          if (data && data['@type'] === 'Product' && data.sku) {
            handles.add(data.sku);
            if (limit && handles.size >= limit) {
              return Array.from(handles);
            }
          }
          // Handle CollectionPage with itemListElement
          if (data && data['@type'] === 'CollectionPage' && data.mainEntity && data.mainEntity.itemListElement) {
            for (const item of data.mainEntity.itemListElement) {
              if (item && item.url) {
                const url = item.url;
                const match = url.match(/\/products\/([^\/?#]+)/);
                if (match && match[1]) {
                  handles.add(match[1]);
                  if (limit && handles.size >= limit) {
                    return Array.from(handles);
                  }
                }
              }
            }
          }
        } catch {}
      }
    }

        console.log(`Found ${handles.size} products in collection ${slug} via HTML scraping`);
  } catch (error) {
    console.warn(`Error scraping collection page for ${slug}:`, error);
  }

  return Array.from(handles);
}

export async function scrapeProductPageHTML(domain: string, handle: string): Promise<any | null> {
  try {
    // Try to fetch the product page
    const productUrl = `https://${domain}/products/${handle}`;
    const res = await fetch(productUrl, {
      headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
    });

    if (!res.ok) {
      console.warn(`Failed to fetch product page: ${productUrl}`);
      return null;
    }

    const html = await res.text();

    // Extract product data from HTML using regex patterns
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|.*$/, '').trim() : handle.replace(/-/g, ' ');

    // Extract description - look for various meta tags and content
    let description = '';
    const descPatterns = [
      /<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i,
      /<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']+)["']/i,
      /<div[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)<\/div>/i
    ];

    for (const pattern of descPatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        description = match[1].trim();
        break;
      }
    }

    // Extract images with better filtering for modern Shopify stores
    const images: any[] = [];
    const imgMatches = html.match(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi) || [];

    for (const imgMatch of imgMatches) {
      const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
      const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
      const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

      if (srcMatch && srcMatch[1]) {
        const src = ensureHttps(srcMatch[1]);
        const alt = altMatch ? altMatch[1] : '';
        const classes = classMatch ? classMatch[1] : '';

        // Skip navigation images, icons, logos, and small images
        if (src.includes('icon') || src.includes('logo') || src.includes('favicon') ||
            classes.includes('nav') || classes.includes('menu') || classes.includes('social') ||
            classes.includes('navbar') || classes.includes('navigation') ||
            src.includes('small') || src.includes('thumbnail') ||
            alt.includes('navigation') || alt.includes('menu') ||
            // Skip images that are clearly from other products
            (!src.toLowerCase().includes(handle.toLowerCase()) &&
             !alt.toLowerCase().includes(handle.toLowerCase()) &&
             !classes.includes('hero') && !classes.includes('main') && !classes.includes('primary') &&
             !classes.includes('large') && !classes.includes('full') &&
             !src.includes('hero') && !src.includes('main'))) {
          continue;
        }

        // Prioritize images that are likely product images
        const isProductImage = (
          // Hero images
          classes.includes('hero') || src.includes('hero') ||
          // Main product images
          classes.includes('main') || classes.includes('primary') ||
          // Large images
          classes.includes('large') || classes.includes('full') ||
          // Product-specific images (check if filename contains product keywords)
          src.toLowerCase().includes(handle.toLowerCase()) ||
          alt.toLowerCase().includes(handle.toLowerCase()) ||
          // Images in main content areas
          !classes.includes('nav') && !classes.includes('footer') && !classes.includes('header')
        );

        if (isProductImage) {
          images.push({
            src,
            alt: alt || title,
            width: 800, // default
            height: 600, // default
          });
        }
      }
    }

    // If no images found with the above criteria, fall back to all non-navigation images
    if (images.length === 0) {
      for (const imgMatch of imgMatches) {
        const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
        const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
        const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

        if (srcMatch && srcMatch[1]) {
          const src = ensureHttps(srcMatch[1]);
          const alt = altMatch ? altMatch[1] : '';
          const classes = classMatch ? classMatch[1] : '';

          // Skip only the most obvious navigation/UI images
          if (!classes.includes('nav') && !classes.includes('menu') && !classes.includes('social') &&
              !src.includes('icon') && !src.includes('logo') && !src.includes('favicon')) {
            images.push({
              src,
              alt: alt || title,
              width: 800,
              height: 600,
            });
          }
        }
      }
    }

    // Extract price - look for various price patterns
    let price = '0';
    const pricePatterns = [
      /<span[^>]*class="[^"]*price[^"]*"[^>]*>([^<]+)<\/span>/gi,
      /<div[^>]*class="[^"]*price[^"]*"[^>]*>([^<]+)<\/div>/gi,
      /<meta[^>]*property=["']product:price:amount["'][^>]*content=["']([^"']+)["']/gi,
      /\$(\d+(?:\.\d{2})?)/g,
      /€(\d+(?:[.,]\d{2})?)/g
    ];

    for (const pattern of pricePatterns) {
      const match = html.match(pattern);
      if (match && match[1]) {
        price = match[1].replace(/[^\d.]/g, '');
        break;
      }
    }

    return {
      id: handle,
      title,
      handle,
      description,
      images,
      variants: [{
        id: `variant_${handle}`,
        price,
        option1: 'Default',
      }],
      available: true,
      product_type: 'Product',
      tags: [],
      vendor: domain,
    };
  } catch (error) {
    console.warn(`Error scraping product page for ${handle}:`, error);
    return null;
  }
}

export async function discoverCollections(
  domain: string,
  opts?: { limit?: number; offset?: number; fast?: boolean }
): Promise<Array<{ slug: string; title: string; count: number }>> {
  // Strategy: prefer /collections.json (fast), optionally slice/paginate; skip per-collection counts in fast mode
  const base = `https://${domain}`;
  const data = await tryFetchJson(`${base}/collections.json?limit=250`);
  const all: Array<{ slug: string; title: string }> = [];

  // Check if we got HTML instead of JSON (modern Shopify stores)
  if (data && typeof data === 'string' && data.includes('<html')) {
    console.warn(`Got HTML response for ${base}/collections.json, trying alternative methods`);
  } else if (data && Array.isArray(data.collections)) {
    for (const c of data.collections) {
      const slug = String(c.handle || c.id || "");
      const title = String(c.title || slug);
      all.push({ slug, title });
    }
    const offset = Math.max(0, opts?.offset ?? 0);
    const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
    const slice = all.slice(offset, offset + limit);

    if (opts?.fast !== false) {
      // Fast path: no extra network to compute counts
      return slice.map((c) => ({ ...c, count: 0 }));
    }

    // Slow path: optionally compute a lightweight count estimate only for the slice
    const out: Array<{ slug: string; title: string; count: number }> = [];
    for (const c of slice) {
      let count = 0;
      try {
        const first = await tryFetchJson(
          `${base}/collections/${c.slug}/products.json?limit=1`
        );
        if (first && Array.isArray(first.products)) {
          count = first.products.length; // not total, but signals non-empty
        }
      } catch {}
      out.push({ slug: c.slug, title: c.title, count });
    }
    return out;
  }

  // Fallback: parse /collections HTML links and paginate locally
  try {
    const res = await fetch(`${base}/collections`, {
      headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
    });
    if (res.ok) {
      const html = await res.text();
      const re = /href=["']([^"']+)["']/gi;
      const seen = new Set<string>();
      let m: RegExpExecArray | null;
      while ((m = re.exec(html))) {
        const href = m[1];
        const mm = href.match(/\/collections\/([^\/?#"']+)/);
        if (mm && mm[1]) {
          const slug = mm[1];
          if (!seen.has(slug)) {
            seen.add(slug);
            all.push({ slug, title: slug.replace(/-/g, " ") });
          }
        }
      }
      if (all.length > 0) {
        const offset = Math.max(0, opts?.offset ?? 0);
        const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
        const slice = all.slice(offset, offset + limit);
        return slice.map((c) => ({ ...c, count: 0 }));
      }
    }
  } catch {}

  // Final fallback: parse sitemap for collections
  try {
    const sitemapUrls = [
      `${base}/sitemap_collections_1.xml`,
      `${base}/sitemap.xml`,
    ];
    const seen = new Set<string>();
    for (const sm of sitemapUrls) {
      try {
        const res = await fetch(sm, {
          headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
        });
        if (!res.ok) continue;
        const xml = await res.text();
        const locRe = /<loc>([^<]+)<\/loc>/gi;
        let m: RegExpExecArray | null;
        while ((m = locRe.exec(xml))) {
          const loc = m[1];
          const mm = loc.match(/\/collections\/([^\/?#"']+)/);
          if (mm && mm[1] && !seen.has(mm[1])) {
            seen.add(mm[1]);
            all.push({ slug: mm[1], title: mm[1].replace(/-/g, " ") });
          }
        }
        if (all.length > 0) break;
      } catch {}
    }
    if (all.length > 0) {
      const offset = Math.max(0, opts?.offset ?? 0);
      const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
      const slice = all.slice(offset, offset + limit);
      return slice.map((c) => ({ ...c, count: 0 }));
    }
  } catch {}

  // Webflow fallback: detect Webflow and parse main page for product links
  try {
    const isWebflow = await detectWebflow(domain);
    if (isWebflow) {
      const res = await fetch(`${base}/`, {
        headers: { ...DEFAULT_HEADERS, referer: `${base}/` },
      });
      if (res.ok) {
        const html = await res.text();
        const seen = new Set<string>();
        // Look for product-like links in navigation or main content
        const productRe = /href=["']([^"']+)["']/gi;
        let m: RegExpExecArray | null;
        while ((m = productRe.exec(html))) {
          const href = m[1];
          // Match product-like paths (e.g., /hybride-warmtepomp, /all-electric)
          const mm = href.match(/^\/([a-z0-9-]+(?:-[a-z0-9-]+)*)$/);
          if (mm && mm[1] && !seen.has(mm[1]) && mm[1].length > 3) { // avoid short paths
            seen.add(mm[1]);
            all.push({ slug: mm[1], title: mm[1].replace(/-/g, " ") });
          }
        }
        if (all.length > 0) {
          const offset = Math.max(0, opts?.offset ?? 0);
          const limit = Math.max(1, Math.min(250, opts?.limit ?? all.length));
          const slice = all.slice(offset, offset + limit);
          return slice.map((c) => ({ ...c, count: 0 }));
        }
      }
    }
  } catch {}

  return [];
}

export async function fetchCollectionHandles(
  domain: string,
  slug: string,
  limitPerCollection?: number
): Promise<string[]> {
  const base = `https://${domain}`;
  const handles = new Set<string>();

  // Check if it's a Webflow site
  const isWebflow = await detectWebflow(domain);

  if (isWebflow) {
    // For Webflow sites like quatt.io, each "collection" is actually a single product page
    // So we treat the slug itself as a product handle
    handles.add(slug);
    if (limitPerCollection && handles.size >= limitPerCollection)
      return Array.from(handles);
  } else {
    // Special handling for Fashion Nova - their JSON API returns HTML
    if (domain === 'www.fashionnova.com') {
      console.log(`[Fashion Nova] Using HTML scraping for collection ${slug}`);
      const htmlHandles = await scrapeCollectionPageHTML(domain, slug, limitPerCollection);
      console.log(`[Fashion Nova] HTML scraping found ${htmlHandles.length} products for collection ${slug}`);
      if (htmlHandles.length === 0) {
        console.warn(`[Fashion Nova] No products found for collection ${slug}, trying alternative collection URL`);
        // Try alternative URL format for Fashion Nova
        const altHandles = await scrapeCollectionPageHTML(domain, `all/${slug}`, limitPerCollection);
        console.log(`[Fashion Nova] Alternative scraping found ${altHandles.length} products for collection all/${slug}`);
        if (altHandles.length > 0) {
          return altHandles;
        }
      }
      return htmlHandles;
    }

    // Original Shopify logic for other stores
    let page = 1;
    const pageSize = 250;
    let htmlFallbackUsed = false;
    while (true) {
      const url = `${base}/collections/${slug}/products.json?limit=${pageSize}&page=${page}`;
      const data = await tryFetchJson(url);

      // Check if we got HTML instead of JSON (modern Shopify stores)
      if (data && typeof data === 'string' && data.includes('<html')) {
        if (!htmlFallbackUsed) {
          console.warn(`Got HTML response for ${url}, trying HTML scraping for collection ${slug}`);
          // Try HTML scraping approach
          const htmlHandles = await scrapeCollectionPageHTML(domain, slug, limitPerCollection);
          console.log(`HTML scraping found ${htmlHandles.length} products for collection ${slug}`);
          handles = new Set([...handles, ...htmlHandles]);
          htmlFallbackUsed = true;
        }
        break;
      }

      const products: any[] =
        data && Array.isArray(data.products) ? data.products : [];
      for (const p of products) {
        if (p && p.handle) {
          handles.add(String(p.handle));
          if (limitPerCollection && handles.size >= limitPerCollection)
            return Array.from(handles);
        }
      }
      if (!products.length) break;
      page += 1;
      if (page > 20) break; // safety
    }
  }

  return Array.from(handles);
}

export async function fetchProduct(
  domain: string,
  handle: string
): Promise<any | null> {
  // Check if it's a Webflow site
  const isWebflow = await detectWebflow(domain);

  if (isWebflow) {
    // For Webflow, fetch the product page and parse HTML
    try {
      const res = await fetch(`https://${domain}/${handle}`, {
        headers: { ...DEFAULT_HEADERS, referer: `https://${domain}/` },
      });
      if (res.ok) {
        const html = await res.text();

        // Extract product data from HTML using regex patterns
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        const title = titleMatch ? titleMatch[1].replace(/\s*\|.*$/, '').trim() : handle.replace(/-/g, ' ');

        // Extract description
        const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
        const description = descMatch ? descMatch[1] : '';

        // Extract images with better filtering for Webflow sites
        const images: any[] = [];
        const imgMatches = html.match(/<img[^>]*src=["']([^"']+)["'][^>]*>/gi) || [];

        for (const imgMatch of imgMatches) {
          const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
          const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
          const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

          if (srcMatch && srcMatch[1]) {
            const src = ensureHttps(srcMatch[1]);
            const alt = altMatch ? altMatch[1] : '';
            const classes = classMatch ? classMatch[1] : '';

            // Skip navigation images, icons, logos, and small images
            if (src.includes('icon') || src.includes('logo') || src.includes('favicon') ||
                classes.includes('nav') || classes.includes('menu') || classes.includes('card-product-small') ||
                classes.includes('navbar') || classes.includes('navigation') ||
                src.includes('small') || src.includes('thumbnail') ||
                alt.includes('navigation') || alt.includes('menu') ||
                // Skip images that are clearly from other products
                (!src.toLowerCase().includes(handle.toLowerCase()) &&
                 !alt.toLowerCase().includes(handle.toLowerCase()) &&
                 !classes.includes('hero') && !classes.includes('main') && !classes.includes('primary') &&
                 !classes.includes('large') && !classes.includes('full') &&
                 !src.includes('hero') && !src.includes('main'))) {
              continue;
            }

            // Prioritize images that are likely product images
            const isProductImage = (
              // Hero images
              classes.includes('hero') || src.includes('hero') ||
              // Main product images
              classes.includes('main') || classes.includes('primary') ||
              // Large images
              classes.includes('large') || classes.includes('full') ||
              // Product-specific images (check if filename contains product keywords)
              src.toLowerCase().includes(handle.toLowerCase()) ||
              alt.toLowerCase().includes(handle.toLowerCase()) ||
              // Images in main content areas
              !classes.includes('nav') && !classes.includes('footer') && !classes.includes('header')
            );

            if (isProductImage) {
              images.push({
                src,
                alt: alt || title,
                width: 800, // default
                height: 600, // default
              });
            }
          }
        }

        // If no images found with the above criteria, fall back to all non-navigation images
        if (images.length === 0) {
          for (const imgMatch of imgMatches) {
            const srcMatch = imgMatch.match(/src=["']([^"']+)["']/);
            const altMatch = imgMatch.match(/alt=["']([^"']+)["']/);
            const classMatch = imgMatch.match(/class=["']([^"']*)["']/);

            if (srcMatch && srcMatch[1]) {
              const src = ensureHttps(srcMatch[1]);
              const alt = altMatch ? altMatch[1] : '';
              const classes = classMatch ? classMatch[1] : '';

              // Skip only the most obvious navigation/UI images
              if (!classes.includes('nav') && !classes.includes('menu') && !classes.includes('card-product-small') &&
                  !src.includes('icon') && !src.includes('logo') && !src.includes('favicon')) {
                images.push({
                  src,
                  alt: alt || title,
                  width: 800,
                  height: 600,
                });
              }
            }
          }
        }

        // Extract price if available
        const priceMatch = html.match(/€\s*(\d+(?:[.,]\d{2})?)/);
        const price = priceMatch ? priceMatch[1].replace(',', '.') : '0';

        return {
          id: handle,
          title,
          handle,
          description,
          images,
          variants: [{
            id: `variant_${handle}`,
            price,
            option1: 'Default',
          }],
          available: true,
          product_type: 'Product',
          tags: [],
          vendor: domain,
        };
      }
    } catch {}
    return null;
  } else {
    // Original Shopify logic
    const url = `https://${domain}/products/${handle}.js`;
    const data = await tryFetchJson(url);

    // Check if we got HTML instead of JSON (modern Shopify stores)
    if (data && typeof data === 'string' && data.includes('<html')) {
      console.warn(`Got HTML response for ${url}, trying HTML scraping for product ${handle}`);
      return await scrapeProductPageHTML(domain, handle);
    }

    if (data && data.title) {
      // Normalize image list - handle different Shopify image formats
      if (Array.isArray(data.images)) {
        // Ensure each image has a proper src_url
        data.images = data.images
          .map((img: any) => {
            if (typeof img === "string") {
              return { src: ensureHttps(img) };
            } else if (img && typeof img === "object") {
              // Handle various Shopify image object formats
              const src = img.src || img.url || img.src_url || img.image_url;
              return {
                ...img,
                src: src ? ensureHttps(src) : "",
                id: img.id || img.image_id || "",
                width: img.width || img.image_width,
                height: img.height || img.image_height,
                alt: img.alt || img.alt_text || img.title || "",
              };
            }
            return img;
          })
          .filter((img: any) => img && (img.src || img.src_url));
      } else if (data.featured_image) {
        // Fallback to featured_image if images array doesn't exist
        const featuredSrc =
          typeof data.featured_image === "string"
            ? data.featured_image
            : data.featured_image.src || data.featured_image.url;
        data.images = [
          {
            src: featuredSrc ? ensureHttps(featuredSrc) : "",
            id: data.featured_image.id || data.id || "",
            width: data.featured_image.width,
            height: data.featured_image.height,
            alt: data.featured_image.alt || data.title || "",
          },
        ];
      } else {
        // No images found
        data.images = [];
      }

      return data;
    }
    return null;
  }
}