import { FastifyInstance } from "fastify";
import { generateBody } from "../schemas/generate";
import { query, transaction } from "../db";
import crypto from "crypto";
import { randomUUID } from "crypto";

function canonicalize(obj: any): any {
  if (obj === null || typeof obj !== "object") return obj;
  if (Array.isArray(obj)) return obj.map(canonicalize);
  const sorted: any = {};
  for (const key of Object.keys(obj).sort()) {
    sorted[key] = canonicalize(obj[key]);
  }
  return sorted;
}

function parseCursorGen(cursor?: string): { ts: string; id: string } | null {
  if (!cursor) return null;
  const parts = cursor.split("|");
  if (parts.length !== 2) return null;
  const [ts, id] = parts;
  const isTs = !isNaN(Date.parse(ts));
  const isUuid = /^[0-9a-fA-F-]{36}$/.test(id);
  if (!isTs || !isUuid) return null;
  return { ts: new Date(ts).toISOString(), id };
}

export async function generateRoutes(app: FastifyInstance) {
  // POST /generate
  app.post("/generate", async (req, reply) => {
    const idemKey = req.headers["idempotency-key"] as string | undefined;

    const parsed = generateBody.safeParse(req.body);
    if (!parsed.success)
      return reply.code(400).send({
        error: { code: "bad_request", message: parsed.error.message },
      });

    const { workspace_id, mode, aspect_ratio, quality, model, items } =
      parsed.data;

    if (!idemKey) {
      // no idempotency header: normal flow
      const result = await transaction(async (client) => {
        const batchRes = await client.query(
          `INSERT INTO app.batches (workspace_id, mode, aspect_ratio, quality, model, requested_count)
           VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
          [workspace_id, mode, aspect_ratio, quality, model, items.length]
        );
        const batch_id = batchRes.rows[0].id as string;

        const requestIds: string[] = [];
        for (const item of items) {
          const { product_id, variant_id, prompt, params } = item;
          const res = await client.query(
            `INSERT INTO app.generation_requests (batch_id, product_id, variant_id, prompt, params_json)
             VALUES ($1, $2, $3, $4, $5) RETURNING id`,
            [batch_id, product_id, variant_id ?? null, prompt, params]
          );
          requestIds.push(res.rows[0].id);
        }
        return { batch_id, request_ids: requestIds };
      });
      return reply.code(201).send(result);
    }

    // durable idempotency path
    const canonical = canonicalize(parsed.data);
    const requestHash = crypto
      .createHash("sha256")
      .update(JSON.stringify(canonical))
      .digest("hex");
    const batchId = randomUUID();

    try {
      const result = await transaction(async (client) => {
        // try reserve key
        const reserve = await client.query(
          `INSERT INTO app.idempotency_keys(key, request_hash, batch_id)
           VALUES ($1, $2, $3)
           ON CONFLICT (key) DO NOTHING`,
          [idemKey, requestHash, batchId]
        );

        if (reserve.rowCount === 0) {
          // existing key
          const existing = await client.query<{
            request_hash: string;
            batch_id: string;
          }>(
            `SELECT request_hash, batch_id FROM app.idempotency_keys WHERE key = $1 FOR UPDATE`,
            [idemKey]
          );
          const row = existing.rows[0];
          if (!row) {
            throw new Error("idempotency reservation not found after conflict");
          }
          if (row.request_hash !== requestHash) {
            throw Object.assign(new Error("IDEMPOTENCY_KEY_CONFLICT"), {
              statusCode: 409,
            });
          }
          // same request; return prior batch id without creating new rows
          return { batch_id: row.batch_id, request_ids: [] as string[] };
        }

        // we reserved: create batch and requests with the pre-generated batchId
        await client.query(
          `INSERT INTO app.batches (id, workspace_id, mode, aspect_ratio, quality, model, requested_count, status)
           VALUES ($1, $2, $3, $4, $5, $6, $7, 'queued')`,
          [
            batchId,
            workspace_id,
            mode,
            aspect_ratio,
            quality,
            model,
            items.length,
          ]
        );

        const requestIds: string[] = [];
        for (const item of items) {
          const { product_id, variant_id, prompt, params } = item;
          const res = await client.query(
            `INSERT INTO app.generation_requests (batch_id, product_id, variant_id, prompt, params_json)
             VALUES ($1, $2, $3, $4, $5) RETURNING id`,
            [batchId, product_id, variant_id ?? null, prompt, params]
          );
          requestIds.push(res.rows[0].id);
        }
        return { batch_id: batchId, request_ids: requestIds };
      });

      const code = result.request_ids.length > 0 ? 201 : 200;
      return reply.code(code).send(result);
    } catch (e: any) {
      if (e?.statusCode === 409) {
        return reply.code(409).send({
          error: {
            code: "idempotency_conflict",
            message: "Idempotency key used with different request body",
          },
        });
      }
      throw e;
    }
  });

  // GET /batches/:batch_id
  app.get("/batches/:batch_id", async (req, reply) => {
    const batch_id = (req.params as any).batch_id as string;
    if (!batch_id || !batch_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "invalid batch_id" } });
    }

    const batchRes = await query(
      `SELECT id, status, requested_count, mode, aspect_ratio, quality, model, created_at
       FROM app.batches WHERE id = $1`,
      [batch_id]
    );
    if (batchRes.rowCount === 0)
      return reply
        .code(404)
        .send({ error: { code: "not_found", message: "batch not found" } });

    const requestsRes = await query(
      `SELECT id, product_id, variant_id, prompt, status, started_at, finished_at, error
       FROM app.generation_requests WHERE batch_id = $1 ORDER BY created_at ASC`,
      [batch_id]
    );

    const assetsRes = await query(
      `SELECT id, type, file_uri, preview_uri, sha256, width, height, duration_ms, source_request_id
       FROM app.assets WHERE source_request_id IN (SELECT id FROM app.generation_requests WHERE batch_id = $1)
       ORDER BY created_at ASC`,
      [batch_id]
    );

    return reply.send({
      batch: batchRes.rows[0],
      requests: requestsRes.rows,
      assets: assetsRes.rows,
    });
  });

  // GET /assets/:asset_id
  app.get("/assets/:asset_id", async (req, reply) => {
    const asset_id = (req.params as any).asset_id as string;
    if (!asset_id || !asset_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "invalid asset_id" } });
    }

    const res = await query(
      `SELECT id, type, file_uri, preview_uri, sha256, width, height, duration_ms, source_request_id
       FROM app.assets WHERE id = $1`,
      [asset_id]
    );
    if (res.rowCount === 0)
      return reply
        .code(404)
        .send({ error: { code: "not_found", message: "asset not found" } });

    return reply.send(res.rows[0]);
  });

  // GET /assets/:asset_id/stream - server-side proxy/stream for remote files (e.g., Gemini URIs)
  app.get("/assets/:asset_id/stream", async (req, reply) => {
    const asset_id = (req.params as any).asset_id as string;
    if (!asset_id || !asset_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "invalid asset_id" } });
    }

    const res = await query(`SELECT file_uri FROM app.assets WHERE id = $1`, [
      asset_id,
    ]);
    if (res.rowCount === 0) {
      return reply
        .code(404)
        .send({ error: { code: "not_found", message: "asset not found" } });
    }

    const fileUri: string | null = (res.rows[0] as any)?.file_uri || null;
    if (!fileUri)
      return reply
        .code(404)
        .send({ error: { code: "not_found", message: "file uri missing" } });

    // If it's a data URL, just redirect to the metadata endpoint so the UI can read it
    if (fileUri.startsWith("data:")) {
      return reply.redirect(`/assets/${asset_id}`);
    }

    try {
      // For Gemini files endpoints, include API key so download works without CORS/auth issues in the browser
      const headers: Record<string, string> = {};
      const needsKey = /generativelanguage\.googleapis\.com\//.test(fileUri);
      if (needsKey && process.env.GEMINI_API_KEY) {
        headers["x-goog-api-key"] = process.env.GEMINI_API_KEY;
      }
      const upstream = await fetch(fileUri, { headers });
      if (!upstream.ok || !upstream.body) {
        const bodyText = await upstream.text().catch(() => "");
        return reply.code(upstream.status || 502).send({
          error: {
            code: "upstream_error",
            message: bodyText || "failed to fetch upstream",
          },
        });
      }
      const ct = upstream.headers.get("content-type") || "video/mp4";
      reply.header("Content-Type", ct);
      reply.header("Cache-Control", "no-store");
      return reply.send(upstream.body as any);
    } catch (e: any) {
      return reply.code(502).send({
        error: { code: "proxy_error", message: String(e?.message || e) },
      });
    }
  });

  // GET /assets/generated?limit=...&cursor=ISO8601|uuid
  app.get("/assets/generated", async (req, reply) => {
    const q = (req.query as any) || {};
    const limitRaw = q.limit;
    const cursorRaw = q.cursor as string | undefined;

    let limit = Number(limitRaw ?? 200);
    if (!Number.isFinite(limit) || limit < 1) limit = 50;
    if (limit > 500) limit = 500;

    const cur = parseCursorGen(cursorRaw);
    if (cursorRaw && !cur) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "malformed cursor" } });
    }

    const params: any[] = [];
    const conds: string[] = [];
    if (cur) {
      conds.push("(a.created_at, a.id) < ($1::timestamptz, $2::uuid)");
      params.push(cur.ts, cur.id);
    }
    const whereClause = conds.length ? `WHERE ${conds.join(" AND ")}` : "";
    params.push(limit + 1);

    const sql = `
      SELECT a.id,
             a.type,
             a.file_uri,
             a.preview_uri,
             a.sha256,
             a.width,
             a.height,
             a.duration_ms,
             a.source_request_id,
             gr.product_id,
             gr.prompt,
             a.created_at
      FROM app.assets a
      JOIN app.generation_requests gr ON gr.id = a.source_request_id
      ${whereClause}
      ORDER BY a.created_at DESC, a.id DESC
      LIMIT $${params.length}
    `;

    const { rows } = await query(sql, params);
    const hasMore = rows.length > limit;
    const slice = rows.slice(0, limit);
    const last = slice[slice.length - 1];
    const next_cursor =
      hasMore && last
        ? `${new Date(last.created_at).toISOString()}|${last.id}`
        : null;

    return reply.send({ items: slice, next_cursor });
  });

  // GET /assets/:asset_id/prompt - get the generation prompt for a specific asset
  app.get("/assets/:asset_id/prompt", async (req, reply) => {
    const asset_id = (req.params as any).asset_id as string;
    if (!asset_id || !asset_id.match(/^[0-9a-fA-F-]{36}$/)) {
      return reply
        .code(400)
        .send({ error: { code: "bad_request", message: "invalid asset_id" } });
    }

    const res = await query(
      `SELECT gr.prompt, gr.params_json, a.id as asset_id
       FROM app.assets a
       JOIN app.generation_requests gr ON gr.id = a.source_request_id
       WHERE a.id = $1`,
      [asset_id]
    );

    if (res.rowCount === 0) {
      return reply
        .code(404)
        .send({ error: { code: "not_found", message: "asset not found" } });
    }

    return reply.send(res.rows[0]);
  });
}
