import { Pool, PoolClient, QueryResult } from "pg";
import dotenv from "dotenv";

dotenv.config();

const DATABASE_URL =
  process.env.DATABASE_URL ||
  "postgres://postgres:postgres@localhost:5432/mediastudio";

// Enhanced pool configuration to handle connection issues
export const pool = new Pool({
  connectionString: DATABASE_URL,
  // Connection settings
  connectionTimeoutMillis: 20000, // 20 seconds (increased)
  query_timeout: 60000, // 60 seconds (increased for long queries)
  statement_timeout: 60000, // 60 seconds (increased for long statements)
  idle_in_transaction_session_timeout: 120000, // 2 minutes (increased)
  // Keep alive settings to prevent connection drops
  keepAlive: true,
  keepAliveInitialDelayMillis: 0,
  // Pool settings
  max: 15, // Reduced from 20 to prevent overwhelming the database
  min: 1, // Reduced from 2 for better resource management
  idleTimeoutMillis: 60000, // 1 minute (increased from 30 seconds)
  allowExitOnIdle: true,
  // Retry settings
  retryOnExit: true,
  // Additional connection options
  application_name: 'mediastudio-scraper',
});

// Add pool event handlers for better error monitoring
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
});

pool.on('connect', (client) => {
  console.log('New client connected to database');
});

pool.on('remove', (client) => {
  console.log('Client removed from pool');
});

export async function withClient<T>(
  fn: (client: PoolClient) => Promise<T>
): Promise<T> {
  const maxRetries = 3;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const client = await pool.connect();
    try {
      return await fn(client);
    } catch (error: any) {
      lastError = error;

      // Check if this is a connection error that we should retry
      const isRetryableError =
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.message?.includes('ECONNRESET') ||
        error.message?.includes('connection terminated');

      if (!isRetryableError || attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff
      const delay = Math.min(100 * Math.pow(2, attempt - 1), 2000);
      console.warn(`Database client operation failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delay));
    } finally {
      client.release();
    }
  }

  throw lastError;
}

export async function query<T = any>(
  text: string,
  params?: any[]
): Promise<QueryResult<T>> {
  const maxRetries = 3;
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await pool.query<T>(text, params);
    } catch (error: any) {
      lastError = error;

      // Check if this is a connection error that we should retry
      const isRetryableError =
        error.code === 'ECONNRESET' ||
        error.code === 'ECONNREFUSED' ||
        error.code === 'ENOTFOUND' ||
        error.message?.includes('ECONNRESET') ||
        error.message?.includes('connection terminated') ||
        error.message?.includes('server closed the connection');

      if (!isRetryableError || attempt === maxRetries) {
        throw error;
      }

      // Exponential backoff: wait 100ms, 200ms, 400ms...
      const delay = Math.min(100 * Math.pow(2, attempt - 1), 2000);
      console.warn(`Database query failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, error.message);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

export async function transaction<T>(
  fn: (client: PoolClient) => Promise<T>
): Promise<T> {
  return withClient(async (client) => {
    await client.query("BEGIN");
    try {
      const res = await fn(client);
      await client.query("COMMIT");
      return res;
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    }
  });
}

// Lightweight DB connectivity check used by /health and preflight guards
export async function pingDb(): Promise<boolean> {
  const tryOnce = async () => {
    const client = await pool.connect();
    try {
      await client.query("SELECT 1");
      return true;
    } catch (error: any) {
      console.warn('Database ping failed:', error.message);
      throw error;
    } finally {
      client.release();
    }
  };

  const maxRetries = 3;
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await tryOnce();
    } catch (error: any) {
      if (attempt === maxRetries) {
        console.error('Database ping failed after all retries:', error.message);
        return false;
      }
      // brief retry to handle just-started DBs
      const delay = 200 * attempt;
      console.warn(`Database ping failed (attempt ${attempt}/${maxRetries}), retrying in ${delay}ms:`, error.message);
      await new Promise((r) => setTimeout(r, delay));
    }
  }
  return false;
}
