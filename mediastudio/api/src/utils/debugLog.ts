// Simple in-memory ring buffer debug log (dev-only by default)
// Redacts secrets and large payloads. Not for production persistence.

export type DebugEvent = {
  ts: string; // ISO timestamp
  source: "server" | "external";
  kind:
    | "request"
    | "response"
    | "external-request"
    | "external-response"
    | "external-error"
    | "error";
  rid?: string; // request id if available
  method?: string;
  url?: string;
  status?: number;
  durationMs?: number;
  note?: string;
  meta?: Record<string, any>;
};

const MAX_EVENTS = Number(process.env.DEBUG_LOG_MAX || 1000);
const buf: DebugEvent[] = [];

function push(ev: DebugEvent) {
  buf.push(ev);
  while (buf.length > MAX_EVENTS) buf.shift();
}

export function logEvent(ev: Omit<DebugEvent, "ts">) {
  try {
    push({ ts: new Date().toISOString(), ...ev });
  } catch {}
}

export function getEvents(): DebugEvent[] {
  return buf.slice(-MAX_EVENTS);
}

export function clearEvents() {
  buf.length = 0;
}

