import fs from 'fs';
import path from 'path';

// Test the JSON-LD regex and parsing
const testHtml = `<script type="application/ld+json">{"@context":"http://schema.org","@type":"CollectionPage","name":"Shop All Halloween","url":"https://www.fashionnova.com/collections/halloween","mainEntity":{"@type":"ItemList","itemListElement":[{"@type":"ListItem","position":1,"name":"Night Stalker Cat 4 Piece Costume Set - Black","url":"https://www.fashionnova.com/products/night-stalker-cat-4-piece-costume-set-black"}]}}</script>`;

console.log('Testing JSON-LD regex...');

// Test the regex
const jsonLdMatch = testHtml.match(/<script[^>]*type=["']application\/ld\+json["'][^>]*>([^<]+)<\/script>/gi);
console.log('Regex matches:', jsonLdMatch?.length || 0);

if (jsonLdMatch) {
  for (const script of jsonLdMatch) {
    console.log('Script found:', script.substring(0, 100) + '...');

    try {
      const jsonContent = script.replace(/<script[^>]*>/, '').replace(/<\/script>/, '');
      console.log('JSON content length:', jsonContent.length);
      console.log('JSON content preview:', jsonContent.substring(0, 200));

      const data = JSON.parse(jsonContent);
      console.log('Parsed data type:', data?.['@type']);
      console.log('Has mainEntity:', !!data?.mainEntity);
      console.log('Has itemListElement:', !!data?.mainEntity?.itemListElement);

      if (data && data['@type'] === 'CollectionPage' && data.mainEntity && data.mainEntity.itemListElement) {
        console.log('Found items:', data.mainEntity.itemListElement.length);
        for (const item of data.mainEntity.itemListElement) {
          if (item && item.url) {
            const url = item.url;
            const match = url.match(/\/products\/([^\/?#]+)/);
            if (match && match[1]) {
              console.log('Found product handle:', match[1]);
            } else {
              console.log('URL pattern failed for:', url);
            }
          }
        }
      }
    } catch (error) {
      console.error('Parse error:', error.message);
    }
  }
}