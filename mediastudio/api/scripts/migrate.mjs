import { fileURLToPath } from 'url';
import path from 'path';
import fs from 'fs/promises';
import process from 'process';
import { Pool } from 'pg';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const MIGRATIONS_DIR = path.resolve(__dirname, '../db/migrations');

function die(msg, code = 1) {
  console.error(msg);
  process.exit(code);
}

async function ensureMigrationsTable(client) {
  // Ensure schema exists before creating migrations table
  await client.query(`CREATE SCHEMA IF NOT EXISTS app;`);
  await client.query(`
    CREATE TABLE IF NOT EXISTS app.migrations (
      filename TEXT PRIMARY KEY,
      applied_at TIMESTAMPTZ NOT NULL DEFAULT now()
    );
  `);
}

async function listSqlFiles(dir) {
  const entries = await fs.readdir(dir, { withFileTypes: true });
  return entries
    .filter(e => e.isFile() && e.name.endsWith('.sql'))
    .map(e => e.name)
    .sort(); // 0001_..., 0002_... order
}

async function readApplied(client) {
  const { rows } = await client.query('SELECT filename FROM app.migrations ORDER BY filename');
  return new Set(rows.map(r => r.filename));
}

async function applyFile(client, filename, sql) {
  await client.query('BEGIN');
  try {
    await client.query(sql);
    await client.query('INSERT INTO app.migrations (filename) VALUES ($1)', [filename]);
    await client.query('COMMIT');
    console.log(`Applied ${filename}`);
  } catch (err) {
    await client.query('ROLLBACK');
    throw new Error(`Migration failed ${filename}: ${err.message}`);
  }
}

async function main() {
  const url = process.env.DATABASE_URL;
  if (!url) die('DATABASE_URL is not set');

  let dryRun = process.argv.includes('--dry-run');

  const pool = new Pool({ connectionString: url, max: 1 });
  const client = await pool.connect();
  try {
    await ensureMigrationsTable(client);

    const files = await listSqlFiles(MIGRATIONS_DIR);
    const onlyIdx = process.argv.indexOf('--only');
    const only = onlyIdx >= 0 && process.argv[onlyIdx + 1] ? process.argv[onlyIdx + 1] : null;
    const selected = only ? files.filter(f => f === only) : files;
    if (selected.length === 0) die(`No .sql files found in ${MIGRATIONS_DIR}${only ? ` (filter --only ${only})` : ''}`);

    const applied = await readApplied(client);
    const pending = selected.filter(f => !applied.has(f));

    if (pending.length === 0) {
      console.log('No pending migrations.');
      await client.release();
      await pool.end();
      process.exit(0);
    }

    console.log(`Pending: ${pending.join(', ')}`);
    if (dryRun) {
      console.log('Dry run: exiting without applying.');
      await client.release();
      await pool.end();
      process.exit(0);
    }

    for (const f of pending) {
      const full = path.join(MIGRATIONS_DIR, f);
      const sql = await fs.readFile(full, 'utf8');
      await applyFile(client, f, sql);
    }
  } finally {
    client.release();
    await pool.end();
  }
}

main().catch(err => die(err.stack || String(err)));

