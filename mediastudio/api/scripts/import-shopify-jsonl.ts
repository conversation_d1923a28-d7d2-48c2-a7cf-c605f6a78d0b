import { createReadStream } from "fs";
import readline from "readline";
import { query } from "../src/db";
import crypto from "crypto";

interface Args {
  file: string;
  shopId: string;
}

function parseArgs(): Args {
  const fileIdx = process.argv.indexOf("--file");
  const shopIdx = process.argv.indexOf("--shop-id");
  if (fileIdx === -1 || shopIdx === -1) {
    console.error(
      "Usage: pnpm seed:jsonl -- --file ./export.jsonl --shop-id <uuid>"
    );
    process.exit(1);
  }
  const file = process.argv[fileIdx + 1];
  const shopId = process.argv[shopIdx + 1];
  return { file, shopId };
}

async function upsertProduct(node: any, shopId: string) {
  const external_gid = node.id || node.admin_graphql_api_id;
  const external_rest_id = Number(
    node.legacyResourceId || node.id || node.product_id
  );
  const handle =
    node.handle ||
    node.title?.toLowerCase().replace(/\s+/g, "-").slice(0, 60) ||
    "product";
  const title = node.title || "Untitled";
  const status = node.status || "ACTIVE";

  const res = await query(
    `INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
     VALUES ($1, $2, $3, $4, $5, $6, $7)
     ON CONFLICT (external_gid) DO UPDATE SET title = EXCLUDED.title, handle = EXCLUDED.handle, status = EXCLUDED.status, raw_json = EXCLUDED.raw_json, updated_at = now()
     RETURNING id`,
    [shopId, external_gid, external_rest_id, handle, title, status, node]
  );
  return res.rows[0].id as string;
}

async function upsertVariant(node: any, productId: string) {
  const external_gid = node.id || node.admin_graphql_api_id;
  const external_rest_id = Number(
    node.legacyResourceId || node.id || node.variant_id
  );
  const sku = node.sku ?? null;
  const price = Number(node.price || 0);
  const option1 = node.option1 || node.option_values?.[0]?.value || null;
  const option2 = node.option2 || node.option_values?.[1]?.value || null;
  const option3 = node.option3 || node.option_values?.[2]?.value || null;

  await query(
    `INSERT INTO app.variants (product_id, external_gid, external_rest_id, sku, price, option1, option2, option3, raw_json)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
     ON CONFLICT (external_gid) DO UPDATE SET sku = EXCLUDED.sku, price = EXCLUDED.price, option1 = EXCLUDED.option1, option2 = EXCLUDED.option2, option3 = EXCLUDED.option3, raw_json = EXCLUDED.raw_json, updated_at = now()`,
    [
      productId,
      external_gid,
      external_rest_id,
      sku,
      price,
      option1,
      option2,
      option3,
      node,
    ]
  );
}

async function insertImage(node: any, productId: string) {
  const external_gid = node.id || node.admin_graphql_api_id || null;
  const external_rest_id = node.legacyResourceId
    ? Number(node.legacyResourceId)
    : null;
  const src_url = node.src || node.originalSrc || node.url;
  const width = node.width || null;
  const height = node.height || null;
  const alt = node.alt || node.altText || null;
  const sha256 = src_url
    ? crypto.createHash("sha256").update(src_url).digest("hex")
    : null;

  const img = await query(
    `INSERT INTO app.product_images (product_id, external_gid, external_rest_id, src_url, width, height, alt, sha256, raw_json)
     VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
     ON CONFLICT (sha256) DO NOTHING
     RETURNING id`,
    [
      productId,
      external_gid,
      external_rest_id,
      src_url,
      width,
      height,
      alt,
      sha256,
      node,
    ]
  );

  // If no default image set yet, set it to the first inserted image
  if (img.rowCount > 0) {
    await query(
      `UPDATE app.products SET default_image_id = COALESCE(default_image_id, $2) WHERE id = $1`,
      [productId, img.rows[0].id]
    );
  }
}

async function main() {
  const { file, shopId } = parseArgs();

  const rl = readline.createInterface({
    input: createReadStream(file),
    crlfDelay: Infinity,
  });

  for await (const line of rl) {
    if (!line.trim()) continue;
    const node = JSON.parse(line);

    if (node.__typename === "Product" || node.resource_type === "product") {
      const productId = await upsertProduct(node, shopId);
      // handle variants if embedded
      if (Array.isArray(node.variants)) {
        for (const v of node.variants) await upsertVariant(v, productId);
      }
      // handle images if embedded
      if (Array.isArray(node.images)) {
        for (const img of node.images) await insertImage(img, productId);
      }
    } else if (
      node.__typename === "ProductVariant" ||
      node.resource_type === "product_variant"
    ) {
      // variant records need product link; if not provided, skip (or resolve via external ids in a more advanced importer)
      if (!node.product_id) continue;
    } else if (
      node.__typename === "ProductImage" ||
      node.resource_type === "product_image"
    ) {
      // rely on product context in full export
    }
  }

  console.log("Import complete");
}

main();
