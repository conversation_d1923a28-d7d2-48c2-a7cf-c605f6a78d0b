import { writeFileSync } from 'fs';
import { Pool } from 'pg';

const url = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/mediastudio';

(async () => {
  const pool = new Pool({ connectionString: url });
  const out = { ok: false, error: null };
  try {
    const { rows } = await pool.query('select 1 as one');
    out.ok = rows && rows[0] && rows[0].one === 1;
  } catch (e) {
    out.error = String(e && e.message ? e.message : e);
  } finally {
    await pool.end();
  }
  writeFileSync('scripts/pingDb.out', JSON.stringify(out, null, 2));
})().catch((e) => {
  writeFileSync('scripts/pingDb.out', JSON.stringify({ ok: false, error: String(e && e.message ? e.message : e) }, null, 2));
});

