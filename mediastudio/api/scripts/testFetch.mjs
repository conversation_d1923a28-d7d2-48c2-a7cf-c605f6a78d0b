const fetchFn = globalThis.fetch;
if (!fetchFn) {
  console.error('No global fetch available in this Node runtime');
  process.exit(1);
}
const base = 'https://dancingqueens.ch';
const headers = {
  'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.5 Safari/605.1.15',
  accept: 'application/json, text/plain, */*',
};

(async () => {
  try {
    const r = await fetchFn(base + '/collections.json?limit=250', { headers });
    const text = await r.text();
    console.log('collections.json', r.status, r.statusText, 'len', text.length);
    console.log(text.slice(0, 200));
  } catch (e) {
    console.log('collections.json error', e?.message || String(e));
  }
  try {
    const r2 = await fetchFn(base + '/collections', { headers });
    const text2 = await r2.text();
    console.log('/collections', r2.status, r2.statusText, 'len', text2.length);
    console.log(text2.slice(0, 200));
  } catch (e) {
    console.log('/collections error', e?.message || String(e));
  }
})();

