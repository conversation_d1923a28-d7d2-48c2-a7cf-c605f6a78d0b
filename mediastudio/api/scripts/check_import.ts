import fs from 'fs';
import path from 'path';
import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

function getArg(flag: string): string | undefined {
  const i = process.argv.indexOf(flag);
  return i >= 0 ? process.argv[i + 1] : undefined;
}

async function main() {
  const fileArg = getArg('--file');
  const file = fileArg || path.resolve(process.cwd(), 'dq_top_products.jsonl');

  if (!fs.existsSync(file)) {
    console.error(`JSONL not found at ${file}`);
    process.exit(2);
  }

  const lines = fs
    .readFileSync(file, 'utf8')
    .split(/\r?\n/)
    .map((l) => l.trim())
    .filter(Boolean);

  const handles: string[] = [];
  for (const line of lines) {
    try {
      const rec = JSON.parse(line);
      const h = (rec.handle || '').trim();
      if (h) handles.push(h);
    } catch (e) {
      // ignore bad lines
    }
  }

  const uniqueHandles = Array.from(new Set(handles));
  if (uniqueHandles.length === 0) {
    console.error('No handles found in JSONL.');
    process.exit(3);
  }

  const DATABASE_URL = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/mediastudio';
  const pool = new Pool({ connectionString: DATABASE_URL });

  try {
    const client = await pool.connect();
    try {
      const { rows } = await client.query(
        `SELECT p.handle,
                p.id,
                p.title,
                p.status,
                (SELECT COUNT(*) FROM app.variants v WHERE v.product_id = p.id) AS variants,
                (SELECT COUNT(*) FROM app.product_images i WHERE i.product_id = p.id) AS images
         FROM app.products p
         WHERE p.handle = ANY($1::text[])
         ORDER BY p.handle`,
        [uniqueHandles]
      );

      const foundByHandle = new Map<string, any>();
      for (const r of rows) foundByHandle.set(r.handle, r);

      const missing: string[] = [];
      for (const h of uniqueHandles) if (!foundByHandle.has(h)) missing.push(h);

      console.log(`Products in DB: ${rows.length}/${uniqueHandles.length}`);
      if (missing.length) {
        console.log(`Missing (${missing.length}):`);
        for (const h of missing) console.log(`  - ${h}`);
      }

      console.log('Sample matches:');
      for (const r of rows.slice(0, 10)) {
        console.log(
          `${r.handle}\t${r.id}\t${r.title}\tstatus=${r.status}\tvariants=${r.variants}\timages=${r.images}`
        );
      }
    } finally {
      client.release();
    }
  } finally {
    await pool.end();
  }
}

main().catch((err) => {
  console.error(err?.stack || String(err));
  process.exit(1);
});

