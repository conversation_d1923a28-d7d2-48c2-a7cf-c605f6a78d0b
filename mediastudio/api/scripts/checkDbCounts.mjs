import { Client } from 'pg';

const url = process.env.DATABASE_URL;
if (!url) {
  console.error('No DATABASE_URL');
  process.exit(2);
}

(async () => {
  const c = new Client({ connectionString: url });
  await c.connect();
  const tables = [
    'app.products',
    'app.product_images',
    'app.batches',
    'app.generation_requests',
    'app.assets',
    'app.shops',
    'app.collections',
    'app.scrape_jobs',
    'app.scrape_job_items',
  ];
  for (const t of tables) {
    try {
      const r = await c.query(`SELECT COUNT(*)::int AS c FROM ${t}`);
      console.log(`${t}: ${r.rows[0].c}`);
    } catch (e) {
      console.log(`${t}: ERROR ${e.message}`);
    }
  }
  await c.end();
})().catch((e) => {
  console.error(e);
  process.exit(1);
});

