import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { pool, withClient } from "../src/db";
import dotenv from "dotenv";

dotenv.config();

// Configuration constants
const CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // ms
  REQUEST_TIMEOUT: 30000, // ms
  RATE_LIMIT_DELAY: 500, // ms between requests
  BATCH_SIZE: 10, // concurrent requests
  MAX_PRODUCTS_PER_COLLECTION: 200, // limit to prevent overwhelming
  PROGRESS_UPDATE_INTERVAL: 10, // log progress every N products
};

// Statistics tracking
interface ScraperStats {
  collectionsProcessed: number;
  productsFound: number;
  productsProcessed: number;
  productsSkipped: number;
  productsFailed: number;
  imagesFetched: number;
  startTime: number;
  errors: string[];
}

const stats: ScraperStats = {
  collectionsProcessed: 0,
  productsFound: 0,
  productsProcessed: 0,
  productsSkipped: 0,
  productsFailed: 0,
  imagesFetched: 0,
  startTime: Date.now(),
  errors: [],
};

// Utility functions
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = CONFIG.MAX_RETRIES,
  baseDelay: number = CONFIG.RETRY_DELAY
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        break;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      console.warn(`Attempt ${attempt + 1} failed, retrying in ${delay}ms:`, error.message);
      await sleep(delay);
    }
  }

  throw lastError!;
}

function logProgress(message: string, force: boolean = false) {
  const elapsed = (Date.now() - stats.startTime) / 1000;
  const rate = stats.productsProcessed / elapsed;

  if (force || stats.productsProcessed % CONFIG.PROGRESS_UPDATE_INTERVAL === 0) {
    console.log(`[${new Date().toISOString()}] ${message}`);
    console.log(`  Progress: ${stats.productsProcessed}/${stats.productsFound} products (${(stats.productsProcessed/stats.productsFound*100).toFixed(1)}%)`);
    console.log(`  Rate: ${rate.toFixed(1)} products/sec | Elapsed: ${elapsed.toFixed(0)}s`);
    console.log(`  Errors: ${stats.errors.length}`);
  }
}

function logFinalStats() {
  const elapsed = (Date.now() - stats.startTime) / 1000;
  const rate = stats.productsProcessed / elapsed;

  console.log('\n=== SCRAPER COMPLETED ===');
  console.log(`Total time: ${elapsed.toFixed(1)}s`);
  console.log(`Processing rate: ${rate.toFixed(1)} products/sec`);
  console.log(`Collections processed: ${stats.collectionsProcessed}`);
  console.log(`Products found: ${stats.productsFound}`);
  console.log(`Products processed: ${stats.productsProcessed}`);
  console.log(`Products skipped: ${stats.productsSkipped}`);
  console.log(`Products failed: ${stats.productsFailed}`);
  console.log(`Images fetched: ${stats.imagesFetched}`);

  if (stats.errors.length > 0) {
    console.log('\n=== ERRORS ===');
    stats.errors.slice(0, 10).forEach((error, i) => {
      console.log(`${i + 1}. ${error}`);
    });
    if (stats.errors.length > 10) {
      console.log(`... and ${stats.errors.length - 10} more errors`);
    }
  }
}

// Enhanced fetch with timeout and better error handling
async function fetchWithTimeout(url: string, options: RequestInit = {}): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
      headers: {
        'User-Agent': 'MediaStudioBot/1.0',
        ...options.headers,
      },
    });
    return response;
  } finally {
    clearTimeout(timeoutId);
  }
}

// Batch processing utility
async function processBatch<T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  batchSize: number = CONFIG.BATCH_SIZE
): Promise<R[]> {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchPromises = batch.map(processor);

    try {
      const batchResults = await Promise.allSettled(batchPromises);
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          console.error(`Batch item ${i + index} failed:`, result.reason);
          stats.errors.push(`Batch item ${i + index}: ${result.reason}`);
        }
      });
    } catch (error) {
      console.error(`Batch processing error:`, error);
    }

    // Rate limiting between batches
    if (i + batchSize < items.length) {
      await sleep(CONFIG.RATE_LIMIT_DELAY);
    }
  }

  return results;
}

// Enhanced collection processing with better error handling
async function processCollectionUrl(
  collectionUrl: string,
  shopId: string,
  dryRun: boolean
) {
  try {
    const collectionSlug = extractCollectionSlugFromUrl(collectionUrl);
    console.log(`\n=== Processing collection: ${collectionSlug} ===`);

    const handles = await retryWithBackoff(async () => {
      const response = await fetchWithTimeout(collectionUrl);
      if (response.status !== 200) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const html = await response.text();
      const handles = extractHandlesFromCollectionHtml(html, new URL(collectionUrl).host);

      if (handles.length === 0) {
        throw new Error('No product handles found in collection');
      }

      return handles;
    });

    console.log(`Found ${handles.length} products in collection ${collectionSlug}`);

    // Limit products per collection to prevent overwhelming
    const limitedHandles = handles.slice(0, CONFIG.MAX_PRODUCTS_PER_COLLECTION);
    if (limitedHandles.length < handles.length) {
      console.warn(`Limiting to ${CONFIG.MAX_PRODUCTS_PER_COLLECTION} products (found ${handles.length})`);
    }

    stats.collectionsProcessed++;
    stats.productsFound += limitedHandles.length;

    if (!dryRun) {
      // Process products in batches
      const records = limitedHandles.map(handle => ({
        shop_domain: new URL(collectionUrl).host,
        handle,
        product_url: `https://${new URL(collectionUrl).host}/products/${handle}`,
        collection: collectionSlug,
      }));

      await processBatch(records, (record) => processRecord(record, shopId, dryRun), CONFIG.BATCH_SIZE);
    }

  } catch (error) {
    const errorMsg = `Collection ${collectionUrl}: ${error.message}`;
    console.error(`❌ ${errorMsg}`);
    stats.errors.push(errorMsg);
  }
}

function extractCollectionSlugFromUrl(collectionUrl: string): string {
  try {
    const url = new URL(collectionUrl);
    const parts = url.pathname.split('/').filter(Boolean);
    const collectionsIndex = parts.findIndex(p => p === 'collections');
    if (collectionsIndex >= 0 && parts[collectionsIndex + 1]) {
      return parts[collectionsIndex + 1];
    }
  } catch {}
  return 'unknown';
}

// Enhanced product processing with better validation
async function processRecord(rec: any, shopId: string, dryRun: boolean) {
  try {
    const shop = String(rec.shop_domain || "").trim();
    let handle = String(rec.handle || "").trim();
    const url = String(rec.product_url || "").trim();

    if (!handle) handle = deriveHandleFromUrl(url) || handle;
    if (!shop || !handle) {
      stats.productsSkipped++;
      return;
    }

    // Fetch product data with retries
    let product = await retryWithBackoff(() => fetchProductJson(shop, handle));

    if (!product && url) {
      product = await retryWithBackoff(() => fetchFromHtml(url));
    }

    if (!product) {
      stats.productsFailed++;
      throw new Error(`Could not fetch product data for ${shop}/${handle}`);
    }

    // Validate product data
    if (!product.title || !Array.isArray(product.images)) {
      stats.productsSkipped++;
      console.warn(`⚠️  Skipping ${handle}: invalid product data`);
      return;
    }

    stats.imagesFetched += product.images.length;

    if (dryRun) {
      console.log(`📋 ${shop}/${handle}: ${product.images.length} images`);
      stats.productsProcessed++;
      return;
    }

    // Process product with database transaction
    await withClient(async (client) => {
      await client.query("BEGIN");
      try {
        await upsertProductWithRelations(client, shopId, product);
        await client.query("COMMIT");
        stats.productsProcessed++;
        logProgress(`✅ Processed ${handle}`);
      } catch (err) {
        await client.query("ROLLBACK");
        throw err;
      }
    });

  } catch (error) {
    const errorMsg = `Product ${rec.handle}: ${error.message}`;
    console.error(`❌ ${errorMsg}`);
    stats.errors.push(errorMsg);
    stats.productsFailed++;
  }
}

// Enhanced main function with better CLI and progress tracking
async function main() {
  const args = parseArgs(process.argv.slice(2));
  const file = (args.file as string) || defaultSeedPath();
  const shopId = (args["shop-id"] as string) || (args.shopId as string);
  const dryRun = Boolean(args["dry-run"] || args.dryRun);
  const collectionsRaw = (args["collections"] as string) || (args["collection"] as string) || "";
  const collections = collectionsRaw.split(",").map((s) => s.trim()).filter((s) => s.length > 0);

  // Validate inputs
  if (!shopId || !/^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i.test(shopId)) {
    console.error("❌ --shop-id is required and must be a UUID");
    process.exit(1);
  }

  console.log(`🚀 MediaStudio Scraper v2.0`);
  console.log(`Mode: ${dryRun ? 'DRY RUN' : 'LIVE'}`);
  console.log(`Shop ID: ${shopId}`);
  console.log(`Batch size: ${CONFIG.BATCH_SIZE}`);
  console.log(`Rate limit: ${CONFIG.RATE_LIMIT_DELAY}ms`);
  console.log(`Max retries: ${CONFIG.MAX_RETRIES}`);
  console.log('');

  try {
    if (collections.length > 0) {
      console.log(`📂 Processing ${collections.length} collection(s)`);
      for (const collection of collections) {
        await processCollectionUrl(collection, shopId, dryRun);
        // Rate limiting between collections
        await sleep(CONFIG.RATE_LIMIT_DELAY * 2);
      }
    } else {
      // Process seed file
      if (!fs.existsSync(file)) {
        console.error(`❌ Seed file not found: ${file}`);
        process.exit(1);
      }

      const content = fs.readFileSync(file, "utf8");
      const lines = content.split(/\r?\n/).filter((l) => l.trim().length > 0);
      console.log(`📄 Processing ${lines.length} records from ${file}`);

      const records = [];
      for (const line of lines) {
        try {
          const rec = JSON.parse(line);
          records.push(rec);
        } catch (err) {
          console.warn(`⚠️  Skipping invalid JSON line: ${err.message}`);
        }
      }

      stats.productsFound = records.length;

      if (!dryRun) {
        await processBatch(records, (record) => processRecord(record, shopId, dryRun), CONFIG.BATCH_SIZE);
      } else {
        // Dry run - just count
        for (const record of records) {
          stats.productsProcessed++;
          if (stats.productsProcessed % CONFIG.PROGRESS_UPDATE_INTERVAL === 0) {
            logProgress(`📋 Dry run: ${stats.productsProcessed}/${stats.productsFound} products`);
          }
        }
      }
    }

    logFinalStats();

  } catch (error) {
    console.error('💥 Fatal error:', error);
    logFinalStats();
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Keep existing utility functions (ensureHttps, deriveHandleFromUrl, extractHandlesFromCollectionHtml, etc.)
// ... existing code ...

main().catch((err) => {
  console.error('💥 Unhandled error:', err?.stack || String(err));
  process.exit(1);
});