import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";
import { pool, withClient } from "../src/db";
import dotenv from "dotenv";

dotenv.config();

// Simple CLI arg parser
function parseArgs(argv: string[]) {
  const args: Record<string, string | boolean> = {};
  for (let i = 0; i < argv.length; i++) {
    const a = argv[i];
    if (a.startsWith("--")) {
      const key = a.slice(2);
      const next = argv[i + 1];
      if (next && !next.startsWith("--")) {
        args[key] = next;
        i++;
      } else {
        args[key] = true; // boolean flag
      }
    }
  }
  return args as { file?: string; shopId?: string; dryRun?: boolean } as any;
}

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function defaultSeedPath() {
  // Prefer repo-root dq_top_products.jsonl, fallback to api/seed/dq_top_products.jsonl
  const rootCandidate = path.resolve(__dirname, "../../dq_top_products.jsonl");
  if (fs.existsSync(rootCandidate)) return rootCandidate;
  return path.resolve(__dirname, "../seed/dq_top_products.jsonl");
}

function ensureHttps(url: string): string {
  if (!url) return url;
  if (url.startsWith("//")) return "https:" + url;
  if (url.startsWith("http://")) return url.replace(/^http:\/\//, "https://");
  if (!/^https?:\/\//.test(url)) return "https://" + url.replace(/^\/+/, "");
  return url;
}

function deriveHandleFromUrl(productUrl?: string): string | null {
  if (!productUrl) return null;
  try {
    const u = new URL(productUrl);
    // Paths like /products/<handle> or /<locale>/products/<handle>
    const parts = u.pathname.split("/").filter(Boolean);
    const idx = parts.findIndex((p) => p === "products");
    if (idx >= 0 && parts[idx + 1]) return parts[idx + 1];
  } catch {}
  return null;
}

// Extract product handles from a collection HTML page
function extractHandlesFromCollectionHtml(
  html: string,
  baseDomain: string
): string[] {
  const handles = new Set<string>();
  // Find href links and derive handles from URLs containing /products/
  const hrefRe = /href=["']([^"']+)["']/gi;
  let m: RegExpExecArray | null;
  while ((m = hrefRe.exec(html))) {
    const href = m[1];
    try {
      const url = new URL(href, `https://${baseDomain}`);
      const h = deriveHandleFromUrl(url.toString());
      if (h) handles.add(h);
    } catch {}
  }
  return Array.from(handles);
}

async function processCollectionUrl(
  collectionUrl: string,
  shopId: string,
  dryRun: boolean
) {
  let u: URL;
  try {
    u = new URL(collectionUrl);
  } catch {
    console.warn(`Skip: invalid collection URL ${collectionUrl}`);
    return;
  }
  const shopDomain = u.host;
  try {
    const res = await fetch(collectionUrl, {
      headers: { "user-agent": "MediaStudioBot/0.1" } as any,
    });
    if (res.status !== 200) {
      console.warn(`WARN: collection fetch ${collectionUrl} -> ${res.status}`);
      return;
    }
    const html = await res.text();
    const handles = extractHandlesFromCollectionHtml(html, shopDomain);
    if (handles.length === 0) {
      console.warn(`WARN: no product handles found on ${collectionUrl}`);
      return;
    }
    console.log(`Collection ${collectionUrl}: found ${handles.length} handles`);
    for (const handle of handles) {
      const rec = {
        shop_domain: shopDomain,
        handle,
        product_url: `https://${shopDomain}/products/${handle}`,
      };
      await processRecord(rec, shopId, dryRun);
    }
  } catch (err: any) {
    console.warn(
      `Collection processing error for ${collectionUrl}: ${
        err?.message || String(err)
      }`
    );
  }
}

async function fetchProductJson(
  shopDomain: string,
  handle: string,
  localeHint?: string
): Promise<any | null> {
  // A) /products/<handle>.js
  const urls: string[] = [`https://${shopDomain}/products/${handle}.js`];
  if (localeHint) {
    urls.push(`https://${shopDomain}/${localeHint}/products/${handle}.js`);
  }

  let productData: any = null;
  for (const u of urls) {
    try {
      const res = await fetch(u, {
        headers: { "user-agent": "MediaStudioBot/0.1" } as any,
      });
      if (res.status === 200) {
        const data: any = await res.json();
        if (data && (data as any).id && (data as any).variants) {
          productData = data as any;
          break;
        }
      }
      if (res.status === 404) continue;
    } catch (_) {
      // try next
    }
  }

  // B) Always try to get additional images from HTML page
  const productUrl = `https://${shopDomain}/products/${handle}`;
  const htmlData = await fetchFromHtml(productUrl);

  if (productData && htmlData && htmlData.images) {
    // Merge images from both sources
    const jsImages = (productData.images || []).map((img: any) =>
      typeof img === 'string' ? img : (img.src || img.url || '')
    ).filter((url: string) => url);

    const htmlImages = htmlData.images.filter((url: string) => url);

    // Combine and deduplicate
    const allImages = Array.from(new Set([...jsImages, ...htmlImages]));
    productData.images = allImages;
  } else if (!productData && htmlData) {
    // If we only have HTML data, use it
    productData = htmlData;
  }

  return productData;
}

async function fetchFromHtml(productUrl: string): Promise<any | null> {
  try {
    const res = await fetch(productUrl, {
      headers: { "user-agent": "MediaStudioBot/0.1" } as any,
    });
    if (res.status !== 200) return null;
    const html = await res.text();

    // Try to find <script type="application/ld+json"> blocks
    const ldMatches = [
      ...html.matchAll(
        /<script[^>]*type=["']application\/ld\+json["'][^>]*>([\s\S]*?)<\/script>/gi
      ),
    ];
    for (const m of ldMatches) {
      try {
        const json = JSON.parse(m[1].trim());
        const candidate = Array.isArray(json)
          ? json.find((j) => j["@type"] === "Product")
          : json;
        if (candidate && candidate["@type"] === "Product") {
          // Normalize to product.js-like shape where possible
          const images: string[] = [];
          if (Array.isArray(candidate.image)) {
            for (const im of candidate.image)
              images.push(ensureHttps(String(im)));
          } else if (typeof candidate.image === "string") {
            images.push(ensureHttps(candidate.image));
          }

          const product: any = {
            id: candidate.sku || Date.now(),
            title: candidate.name || "",
            handle: candidate.sku || "",
            variants: [
              {
                id: candidate.sku || Date.now(),
                title: candidate.name || "",
                sku: candidate.sku || null,
                price:
                  typeof candidate.offers?.price === "string"
                    ? Number(candidate.offers.price)
                    : Number(candidate.offers?.price || 0),
                option1: null,
                option2: null,
                option3: null,
              },
            ],
            images,
          };
          return product;
        }
      } catch {}
    }

    // Also look for Open Graph images (og:image / og:image:secure_url)
    const ogImgUrls = new Set<string>();
    for (const m of html.matchAll(
      /<meta[^>]+property=["']og:image(?::secure_url)?["'][^>]*content=["']([^"']+)["'][^>]*>/gi
    )) {
      ogImgUrls.add(ensureHttps(m[1]));
    }

    // Collect CDN images from DOM as additional hints
    const cdnImgUrls = new Set<string>();
    for (const m of html.matchAll(/<img[^>]+src=["']([^"']+cdn[^"']+)["']/gi)) {
      cdnImgUrls.add(ensureHttps(m[1]));
    }

    const allImgs = Array.from(new Set<string>([...ogImgUrls, ...cdnImgUrls]));
    if (allImgs.length > 0) {
      return {
        id: Date.now(),
        title: "Unknown",
        handle: "unknown",
        variants: [],
        images: allImgs,
      };
    }
  } catch {}
  return null;
}

function centsToNumber(price: any): number {
  if (price == null) return 0;
  const n = Number(price);
  if (!Number.isFinite(n)) return 0;
  // Many Shopify themes expose variant.price as cents integer
  if (n > 1000 && Number.isInteger(n)) return Math.round((n / 100) * 100) / 100;
  return Math.round(n * 100) / 100;
}

async function upsertProductWithRelations(
  client: any,
  shopId: string,
  productJson: any
) {
  // Normalize fields from product.js shape
  const prodId = Number(productJson.id);
  const title = String(productJson.title || "");
  const handle = String(productJson.handle || "");
  const external_gid = `gid://shopify/Product/${prodId}`;
  const external_rest_id = prodId;

  // Product upsert
  const existing = await client.query(
    `SELECT id, default_image_id FROM app.products WHERE external_gid = $1`,
    [external_gid]
  );

  let productId: string;
  let productInserted = false;
  if (existing.rowCount > 0) {
    productId = existing.rows[0].id;
    await client.query(
      `UPDATE app.products
       SET shop_id = $2, handle = $3, title = $4, status = 'ACTIVE', raw_json = $5, updated_at = now()
       WHERE id = $1`,
      [productId, shopId, handle, title, productJson]
    );
  } else {
    const ins = await client.query(
      `INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
       VALUES ($1, $2, $3, $4, $5, 'ACTIVE', $6)
       RETURNING id`,
      [shopId, external_gid, external_rest_id, handle, title, productJson]
    );
    productId = ins.rows[0].id;
    productInserted = true;
  }

  // Variants upsert
  let variantsInserted = 0;
  let variantsUpdated = 0;
  const variants: any[] = Array.isArray(productJson.variants)
    ? productJson.variants
    : [];
  for (const v of variants) {
    const vId = Number(v.id);
    if (!Number.isFinite(vId)) continue;
    const v_gid = `gid://shopify/ProductVariant/${vId}`;
    const sku = (v.sku && String(v.sku).trim()) || null;
    const priceNum = centsToNumber(v.price);

    const exV = await client.query(
      `SELECT id FROM app.variants WHERE external_gid = $1`,
      [v_gid]
    );
    if (exV.rowCount > 0) {
      await client.query(
        `UPDATE app.variants
         SET product_id = $2, sku = $3, price = $4, option1 = $5, option2 = $6, option3 = $7, raw_json = $8, updated_at = now()
         WHERE external_gid = $1`,
        [
          v_gid,
          productId,
          sku,
          priceNum,
          v.option1 ?? null,
          v.option2 ?? null,
          v.option3 ?? null,
          v,
        ]
      );
      variantsUpdated++;
    } else {
      await client.query(
        `INSERT INTO app.variants (product_id, external_gid, external_rest_id, sku, price, option1, option2, option3, raw_json)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
        [
          productId,
          v_gid,
          vId,
          sku,
          priceNum,
          v.option1 ?? null,
          v.option2 ?? null,
          v.option3 ?? null,
          v,
        ]
      );
      variantsInserted++;
    }
  }

  // Images upsert (by product_id, src_url)
  let imagesInserted = 0;
  let imagesUpdated = 0;
  const images: any[] = Array.isArray(productJson.images)
    ? productJson.images
    : [];
  let defaultImageId = existing.rows?.[0]?.default_image_id || null;

  const normalizedImages = images
    .map((img: any) => {
      if (typeof img === "string") return { src: ensureHttps(img) };
      return {
        id: img.id ?? null,
        src: ensureHttps(img.src || img.url || ""),
        width: img.width ?? null,
        height: img.height ?? null,
        alt: img.alt ?? null,
        raw: img,
      };
    })
    .filter((i: any) => i.src);

  // Deduplicate by src
  const seen = new Set<string>();
  const uniqueImgs = normalizedImages.filter((i: any) => {
    if (seen.has(i.src)) return false;
    seen.add(i.src);
    return true;
  });

  for (const img of uniqueImgs) {
    const exI = await client.query(
      `SELECT id FROM app.product_images WHERE product_id = $1 AND src_url = $2`,
      [productId, img.src]
    );
    if (exI.rowCount > 0) {
      await client.query(
        `UPDATE app.product_images
         SET external_gid = $3, width = $4, height = $5, alt = $6, raw_json = $7, updated_at = now()
         WHERE product_id = $1 AND src_url = $2`,
        [
          productId,
          img.src,
          img.id ? `gid://shopify/ProductImage/${img.id}` : null,
          img.width ?? null,
          img.height ?? null,
          img.alt ?? null,
          img.raw ?? img,
        ]
      );
      imagesUpdated++;
    } else {
      const insI = await client.query(
        `INSERT INTO app.product_images (product_id, external_gid, src_url, width, height, alt, raw_json)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING id`,
        [
          productId,
          img.id ? `gid://shopify/ProductImage/${img.id}` : null,
          img.src,
          img.width ?? null,
          img.height ?? null,
          img.alt ?? null,
          img.raw ?? img,
        ]
      );
      const newImgId = insI.rows[0].id;
      imagesInserted++;
      if (!defaultImageId) {
        await client.query(
          `UPDATE app.products SET default_image_id = $2 WHERE id = $1 AND default_image_id IS NULL`,
          [productId, newImgId]
        );
        defaultImageId = newImgId;
      }
    }
  }

  console.log(
    `product ${
      productInserted ? "ins" : "upd"
    } | variants +${variantsInserted}/~${variantsUpdated} | images +${imagesInserted}/~${imagesUpdated}`
  );
}

async function processRecord(rec: any, shopId: string, dryRun: boolean) {
  const shop = String(rec.shop_domain || "").trim();
  let handle = String(rec.handle || "").trim();
  const url = String(rec.product_url || "").trim();
  const locale =
    (rec.locale_hint && String(rec.locale_hint).trim()) || undefined;

  if (!handle) handle = deriveHandleFromUrl(url) || handle;
  if (!shop || !handle) {
    console.warn(`Skip: missing shop or handle for ${JSON.stringify(rec)}`);
    return;
  }

  // Fetch storefront JSON
  let product = await fetchProductJson(shop, handle, locale);
  if (!product && url) {
    product = await fetchFromHtml(url);
  }
  if (!product) {
    console.warn(`WARN: could not fetch product for ${shop}/${handle}`);
    return;
  }

  if (dryRun) {
    console.log(
      `dry-run: would upsert ${shop}/${handle} (variants=${
        product.variants?.length || 0
      }, images=${product.images?.length || 0})`
    );
    return;
  }

  await withClient(async (client) => {
    await client.query("BEGIN");
    try {
      await upsertProductWithRelations(client, shopId, product);
      await client.query("COMMIT");
    } catch (err) {
      await client.query("ROLLBACK");
      throw err;
    }
  });
}

async function main() {
  const args = parseArgs(process.argv.slice(2));
  const file = (args.file as string) || defaultSeedPath();
  const shopId = (args["shop-id"] as string) || (args.shopId as string);
  const dryRun = Boolean(args["dry-run"] || args.dryRun);
  const collectionsRaw =
    (args["collections"] as string) || (args["collection"] as string) || "";
  const collections = collectionsRaw
    .split(",")
    .map((s) => s.trim())
    .filter((s) => s.length > 0);

  if (
    !shopId ||
    !/^([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i.test(
      shopId
    )
  ) {
    console.error("--shop-id is required and must be a UUID");
    process.exit(1);
  }

  if (collections.length > 0) {
    console.log(
      `Processing ${collections.length} collection(s) (dryRun=${dryRun})`
    );
    for (const c of collections) {
      await processCollectionUrl(c, shopId, dryRun);
    }
    await pool.end();
    console.log(`Done processing collections.`);
    return;
  }

  if (!fs.existsSync(file)) {
    const expected = defaultSeedPath();
    console.error(
      `Place dq_top_products.jsonl in api/seed/ and re-run. Expected at: ${expected}`
    );
    process.exit(1);
  }

  const content = fs.readFileSync(file, "utf8");
  const lines = content.split(/\r?\n/).filter((l) => l.trim().length > 0);
  console.log(
    `Processing ${lines.length} lines from ${file} (dryRun=${dryRun})`
  );

  let count = 0;
  for (const line of lines) {
    try {
      const rec = JSON.parse(line);
      await processRecord(rec, shopId, dryRun);
      count++;
    } catch (err: any) {
      console.warn(
        `Line parse/processing error: ${err?.message || String(err)}`
      );
    }
  }

  await pool.end();
  console.log(`Done. Processed ${count} records.`);
}

main().catch((err) => {
  console.error(err?.stack || String(err));
  process.exit(1);
});
