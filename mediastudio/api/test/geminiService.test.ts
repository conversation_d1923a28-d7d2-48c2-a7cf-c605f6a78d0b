import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { generateVideo } from "../src/services/geminiService";

const SAMPLE_DATA_URL =
  "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR4nGMAAc8AAnkB2nJxAQAAAABJRU5ErkJggg==";

declare const global: any;

describe("generateVideo", () => {
  const realEnv = { ...process.env };

  beforeEach(() => {
    vi.restoreAllMocks();
    process.env = { ...realEnv };
  });
  afterEach(() => {
    process.env = realEnv;
  });

  it("returns mock video when REAL disabled", async () => {
    process.env.VEO_ENABLE_REAL = "0";
    const out = await generateVideo("test", { aspectRatio: "16:9", resolution: "720p", duration: 4, fps: 24, motionStrength: 5, audio: false, quality: "Standard", seed: 1 }, SAMPLE_DATA_URL, "veo-2.0-generate-001");
    expect(out.url).toContain("samplelib.com");
    expect(out.preview_url).toMatch(/^data:image\/png;base64,/);
  });

  it("sends inline bytes for data: URLs when REAL enabled", async () => {
    process.env.VEO_ENABLE_REAL = "1";
    process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || "test-key";

    const fetchMock = vi.fn();
    // First call: start LRO
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ name: "operations/123" }),
    } as any);
    // Second call: poll done
    fetchMock.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        done: true,
        response: {
          generateVideoResponse: {
            generatedSamples: [ { video: { uri: "https://example.com/video.mp4" } } ]
          }
        }
      })
    } as any);

    const origFetch = global.fetch;
    global.fetch = fetchMock;

    const out = await generateVideo(
      "A product spinning",
      { aspectRatio: "16:9", resolution: "720p", duration: 4, fps: 24, motionStrength: 5, audio: false, quality: "Standard", seed: 1 },
      SAMPLE_DATA_URL,
      "veo-2.0-generate-001"
    );

    expect(out.url).toEqual("https://example.com/video.mp4");

    const firstCall = fetchMock.mock.calls[0];
    const body = JSON.parse(firstCall[1].body);
    const inst = body.instances?.[0] || {};
    expect(inst.image).toBeDefined();
    expect(inst.image.bytesBase64Encoded).toBeDefined();
    expect(inst.image.mimeType).toMatch(/^image\//);

    global.fetch = origFetch;
  });
});

