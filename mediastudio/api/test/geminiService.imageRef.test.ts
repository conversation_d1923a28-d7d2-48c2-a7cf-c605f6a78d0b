import { beforeEach, describe, expect, it, vi } from "vitest";
import { generateVideo } from "../src/services/geminiService";

// Ensure REAL path runs
process.env.VEO_ENABLE_REAL = "1";
process.env.GEMINI_API_KEY = process.env.GEMINI_API_KEY || "TEST_KEY";

// Helper to create Response-like objects
function makeRes(body: any, init: { ok?: boolean; status?: number; headers?: Record<string, string> } = {}) {
  const ok = init.ok ?? true;
  const status = init.status ?? (ok ? 200 : 500);
  const headers = new Headers(init.headers || {});
  return {
    ok,
    status,
    headers,
    async json() { return body; },
    async text() { return typeof body === "string" ? body : JSON.stringify(body); },
    async arrayBuffer() { return new TextEncoder().encode(typeof body === "string" ? body : JSON.stringify(body)); },
  } as unknown as Response;
}

// Minimal video op response
const startOp = { name: "operations/test-op" };
const doneOp = {
  done: true,
  response: {
    generatedVideos: [{ video: { uri: "https://example.com/video.mp4" } }],
  },
};

describe("generateVideo reference image handling", () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("rejects unsupported MIME types (e.g., webp)", async () => {
    const fetchMock = vi.fn()
      // 1) fetchAsBase64(reference) => webp
      .mockResolvedValueOnce(makeRes("WEBP_BYTES", { headers: { "content-type": "image/webp" } }));
    // @ts-expect-error override global
    global.fetch = fetchMock;

    await expect(
      generateVideo("prompt", { aspectRatio: "16:9", resolution: "720p", duration: 5 }, "https://example.com/ref.webp", "veo-2.0-generate-001")
    ).rejects.toThrow(/MIME type 'image\/webp' not supported/i);

    expect(fetchMock).toHaveBeenCalledTimes(1);
  });

  it("accepts PNG and completes flow (mocked)", async () => {
    const pngBytes = "PNG_BYTES";
    const pngBuf = new TextEncoder().encode(pngBytes);

    const fetchMock = vi.fn()
      // 1) fetchAsBase64(reference) => png
      .mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Headers({ "content-type": "image/png" }),
        async arrayBuffer() { return pngBuf; },
      } as any)
      // 2) POST start
      .mockResolvedValueOnce(makeRes(startOp))
      // 3) GET poll
      .mockResolvedValueOnce(makeRes(doneOp));

    // @ts-expect-error override global
    global.fetch = fetchMock;

    const result = await generateVideo(
      "prompt",
      { aspectRatio: "16:9", resolution: "720p", duration: 5 },
      "https://example.com/ref.png",
      "veo-2.0-generate-001"
    );

    expect(result.url).toMatch(/example.com\/video\.mp4/);
    // Should have called: image fetch, start, poll
    expect(fetchMock).toHaveBeenCalledTimes(3);
  });
});

