# MediaStudio API (Minimal, Shopify-compatible)

## Quickstart

1. Start Postgres via Docker:
   docker compose up -d

2. Configure env (create .env):
   DATABASE_URL=postgres://postgres:postgres@localhost:5432/mediastudio
   PORT=8080
   STORAGE_BASE_URI=s3://your-bucket
   NODE_ENV=development

3. Install deps and run migrations:
   pnpm install
   pnpm migrate

4. Dev server:
   pnpm dev
   # All requests must include header: X-Env: dev

5. Tests:
   pnpm test

6. Seed Shopify JSONL export:
   pnpm seed:jsonl -- --file ./sample.jsonl --shop-id <uuid>

## Endpoints
- GET /health
- GET /catalog/products
- GET /catalog/products/:product_id/variants
- POST /generate
- GET /batches/:batch_id
- GET /assets/:asset_id

See openapi.yaml for details.

## Airbyte Swap
Views are the stable contract for the catalog. Later, update `db/migrations/0002_views.sql` to map `app.catalog_*_v` to Airbyte tables. No API or UI changes required.

