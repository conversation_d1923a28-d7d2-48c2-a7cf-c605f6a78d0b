import { beforeAll, afterAll, describe, it, expect } from "vitest";
import { pool, query } from "../src/db";
import Fastify from "fastify";
import { catalogRoutes } from "../src/routes/catalog";
import { generateRoutes } from "../src/routes/generate";

const app = Fastify();
app.addHook("onRequest", async (req, reply) => {
  if (req.headers["x-env"] !== "dev")
    reply
      .code(401)
      .send({
        error: {
          code: "unauthorized",
          message: "missing or invalid X-Env header",
        },
      });
});
app.register(catalogRoutes);
app.register(generateRoutes);

beforeAll(async () => {
  // Basic seed: create 60 products so we can test keyset pagination
  await query(
    `TRUNCATE app.assets, app.generation_requests, app.batches, app.product_images, app.variants, app.products RESTART IDENTITY CASCADE`
  );
  for (let i = 1; i <= 60; i++) {
    const p = await query(
      `INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status) VALUES (gen_random_uuid(), $1, $2, $3, $4, 'ACTIVE') RETURNING id`,
      [`gid://shopify/Product/${i}`, i, `handle-${i}`, `Product ${i}`]
    );
    // 1-2 variants
    await query(
      `INSERT INTO app.variants (product_id, external_gid, external_rest_id, sku, price) VALUES ($1, $2, $3, $4, 1000)`,
      [
        p.rows[0].id,
        `gid://shopify/ProductVariant/${i}1`,
        Number(`${i}1`),
        `SKU-${i}-1`,
      ]
    );
    await query(
      `INSERT INTO app.product_images (product_id, src_url) VALUES ($1, $2)`,
      [p.rows[0].id, `https://example.com/p${i}.png`]
    );
  }
});

afterAll(async () => {
  await pool.end();
});

describe("Catalog endpoints", () => {
  it("GET /catalog/products returns products and supports keyset pagination", async () => {
    const res1 = await app.inject({
      method: "GET",
      url: "/catalog/products?limit=25",
      headers: { "x-env": "dev" },
    });
    expect(res1.statusCode).toBe(200);
    const body1 = res1.json();
    expect(body1.items.length).toBe(25);
    expect(body1.next_cursor).toBeTypeOf("string");

    const res2 = await app.inject({
      method: "GET",
      url: `/catalog/products?limit=25&cursor=${encodeURIComponent(
        body1.next_cursor
      )}`,
      headers: { "x-env": "dev" },
    });
    expect(res2.statusCode).toBe(200);
    const body2 = res2.json();
    expect(body2.items.length).toBe(25);

    // ensure no overlap between page1 and page2
    const ids1 = new Set(body1.items.map((i: any) => i.product_id));
    const overlap = body2.items.some((i: any) => ids1.has(i.product_id));
    expect(overlap).toBe(false);
  });

  it("GET /catalog/products/:id/variants returns variants", async () => {
    const prod = await query<{ id: string }>(
      `SELECT id FROM app.products LIMIT 1`
    );
    const pid = prod.rows[0].id;
    const res = await app.inject({
      method: "GET",
      url: `/catalog/products/${pid}/variants`,
      headers: { "x-env": "dev" },
    });
    expect(res.statusCode).toBe(200);
    const body = res.json();
    expect(Array.isArray(body.items)).toBe(true);
    expect(body.items.length).toBeGreaterThan(0);
  });
});

describe("Generate flow", () => {
  it("POST /generate then GET /batches/:id returns requests", async () => {
    const prod = await query<{ id: string }>(
      `SELECT id FROM app.products LIMIT 1`
    );
    const varr = await query<{ id: string }>(
      `SELECT id FROM app.variants WHERE product_id = $1 LIMIT 1`,
      [prod.rows[0].id]
    );

    const body = {
      workspace_id: "11111111-1111-1111-1111-111111111111",
      mode: "image",
      aspect_ratio: "1:1",
      quality: "standard",
      model: "gemini-2.5-flash",
      items: [
        {
          product_id: prod.rows[0].id,
          variant_id: varr.rows[0].id,
          prompt: "Test",
          params: { seed: 1 },
        },
      ],
    };

    const create = await app.inject({
      method: "POST",
      url: "/generate",
      payload: body,
      headers: { "x-env": "dev" },
    });
    expect(create.statusCode).toBe(201);
    const created = create.json();

    // add a fake asset
    const reqId = created.request_ids[0];
    await query(
      `INSERT INTO app.assets (workspace_id, type, file_uri, sha256, source_request_id) VALUES (gen_random_uuid(), 'image', 's3://bucket/test.png', 'deadbeef', $1)`,
      [reqId]
    );

    const get = await app.inject({
      method: "GET",
      url: `/batches/${created.batch_id}`,
      headers: { "x-env": "dev" },
    });
    expect(get.statusCode).toBe(200);
    const batch = get.json();
    expect(Array.isArray(batch.requests)).toBe(true);
    expect(Array.isArray(batch.assets)).toBe(true);
    expect(batch.requests.length).toBe(1);
    expect(batch.assets.length).toBe(1);
  });

  it("Idempotency success: same key + same body returns same batch_id", async () => {
    const prod = await query<{ id: string }>(
      `SELECT id FROM app.products LIMIT 1`
    );
    const varr = await query<{ id: string }>(
      `SELECT id FROM app.variants WHERE product_id = $1 LIMIT 1`,
      [prod.rows[0].id]
    );

    const body = {
      workspace_id: "11111111-1111-1111-1111-111111111111",
      mode: "image",
      aspect_ratio: "1:1",
      quality: "standard",
      model: "gemini-2.5-flash",
      items: [
        {
          product_id: prod.rows[0].id,
          variant_id: varr.rows[0].id,
          prompt: "Test",
          params: { seed: 1 },
        },
      ],
    };

    const headers = { "x-env": "dev", "Idempotency-Key": "test-key-1" } as any;
    const first = await app.inject({
      method: "POST",
      url: "/generate",
      payload: body,
      headers,
    });
    expect([200, 201]).toContain(first.statusCode);
    const a = first.json();

    const second = await app.inject({
      method: "POST",
      url: "/generate",
      payload: body,
      headers,
    });
    expect([200, 201]).toContain(second.statusCode);
    const b = second.json();

    expect(a.batch_id).toBe(b.batch_id);
  });

  it("Idempotency conflict: same key + different body returns 409", async () => {
    const prod = await query<{ id: string }>(
      `SELECT id FROM app.products LIMIT 1`
    );
    const varr = await query<{ id: string }>(
      `SELECT id FROM app.variants WHERE product_id = $1 LIMIT 1`,
      [prod.rows[0].id]
    );

    const body1 = {
      workspace_id: "11111111-1111-1111-1111-111111111111",
      mode: "image",
      aspect_ratio: "1:1",
      quality: "standard",
      model: "gemini-2.5-flash",
      items: [
        {
          product_id: prod.rows[0].id,
          variant_id: varr.rows[0].id,
          prompt: "Test A",
          params: { seed: 1 },
        },
      ],
    };
    const body2 = {
      ...body1,
      items: [
        {
          product_id: prod.rows[0].id,
          variant_id: varr.rows[0].id,
          prompt: "Test B",
          params: { seed: 2 },
        },
      ],
    };

    const headers = { "x-env": "dev", "Idempotency-Key": "test-key-2" } as any;
    const first = await app.inject({
      method: "POST",
      url: "/generate",
      payload: body1,
      headers,
    });
    expect([200, 201]).toContain(first.statusCode);

    const second = await app.inject({
      method: "POST",
      url: "/generate",
      payload: body2,
      headers,
    });
    expect(second.statusCode).toBe(409);
  });
});

describe("updated_at trigger", () => {
  it("updates updated_at on row update", async () => {
    const prod = await query<{ id: string; updated_at: string }>(
      `SELECT id, updated_at FROM app.products LIMIT 1`
    );
    const before = new Date(prod.rows[0].updated_at).getTime();
    await query(`UPDATE app.products SET title = title || 'x' WHERE id = $1`, [
      prod.rows[0].id,
    ]);
    const afterRes = await query<{ updated_at: string }>(
      `SELECT updated_at FROM app.products WHERE id = $1`,
      [prod.rows[0].id]
    );
    const after = new Date(afterRes.rows[0].updated_at).getTime();
    expect(after).toBeGreaterThan(before);
  });
});
