openapi: 3.1.0
info:
  title: MediaStudio API
  version: 0.1.0
servers:
  - url: http://localhost:8080
paths:
  /health:
    get:
      summary: Health check
      responses:
        '200':
          description: OK
  /catalog/products:
    get:
      summary: List products (cursor-based)
      parameters:
        - name: limit
          in: query
          schema: { type: integer, minimum: 1, maximum: 200, default: 50 }
        - name: cursor
          in: query
          schema: { type: string }
      responses:
        '200':
          description: OK
  /catalog/products/{product_id}/variants:
    get:
      summary: List variants for a product
      parameters:
        - name: product_id
          in: path
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: OK
  /generate:
    post:
      summary: Create a generation batch and requests
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        '201':
          description: Created
  /batches/{batch_id}:
    get:
      summary: Get batch details
      parameters:
        - name: batch_id
          in: path
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: OK
  /assets/{asset_id}:
    get:
      summary: Get asset details
      parameters:
        - name: asset_id
          in: path
          required: true
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: OK

