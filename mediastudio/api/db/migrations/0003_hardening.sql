CREATE SCHEMA IF NOT EXISTS app;

-- 1) touch updated_at on UPDATE
CREATE OR REPLACE FUNCTION app.touch_updated_at()
RET<PERSON><PERSON> trigger AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_products') THEN
    CREATE TRIGGER trg_touch_products BEFORE UPDATE ON app.products
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_variants') THEN
    CREATE TRIGGER trg_touch_variants BEFORE UPDATE ON app.variants
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_images') THEN
    CREATE TRIGGER trg_touch_images BEFORE UPDATE ON app.product_images
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_batches') THEN
    CREATE TRIGGER trg_touch_batches BEFORE UPDATE ON app.batches
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_requests') THEN
    CREATE TRIGGER trg_touch_requests BEFORE UPDATE ON app.generation_requests
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
  IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_assets') THEN
    CREATE TRIGGER trg_touch_assets BEFORE UPDATE ON app.assets
    FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
  END IF;
END $$;

-- 2) durable idempotency across restarts
CREATE TABLE IF NOT EXISTS app.idempotency_keys (
  key TEXT PRIMARY KEY,           -- value from Idempotency-Key header
  request_hash TEXT NOT NULL,     -- sha256 of canonical request body
  batch_id UUID NOT NULL,         -- UUID we will also use for the batch row
  created_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_idem_created_at ON app.idempotency_keys(created_at);

-- 3) strong, keyset-friendly view (expose stable_id = product_id)
CREATE OR REPLACE VIEW app.catalog_products_v AS
SELECT p.id AS product_id,
       p.title, p.handle, p.status,
       COALESCE(pi.src_url, '') AS default_image_url,
       p.updated_at,
       p.id AS stable_id
FROM app.products p
LEFT JOIN app.product_images pi ON pi.id = p.default_image_id;

