-- 0005_assets_video_columns.sql
-- Ensure app.assets has columns needed for video support

CREATE SCHEMA IF NOT EXISTS app;

ALTER TABLE IF EXISTS app.assets
  ADD COLUMN IF NOT EXISTS preview_uri TEXT NULL,
  ADD COLUMN IF NOT EXISTS width INT NULL,
  ADD COLUMN IF NOT EXISTS height INT NULL,
  ADD COLUMN IF NOT EXISTS duration_ms INT NULL;

-- Optional helpful index if not present
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_indexes WHERE schemaname='app' AND tablename='assets' AND indexname='idx_assets_created_at'
  ) THEN
    CREATE INDEX idx_assets_created_at ON app.assets(created_at);
  END IF;
END $$;

