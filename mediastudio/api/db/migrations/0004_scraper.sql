-- 0004_scraper.sql
-- Additive, backward-compatible tables for collections and scraper jobs.
-- Also define a minimal shops table if not present.

CREATE SCHEMA IF NOT EXISTS app;

-- Shops (minimal)
CREATE TABLE IF NOT EXISTS app.shops (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  domain TEXT NOT NULL UNIQUE,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Collections (per shop)
CREATE TABLE IF NOT EXISTS app.collections (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id UUID NOT NULL,
  slug TEXT NOT NULL,
  title TEXT NOT NULL,
  raw_json JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE (shop_id, slug)
);
CREATE INDEX IF NOT EXISTS idx_collections_shop ON app.collections(shop_id);

-- Product to Collection mapping
CREATE TABLE IF NOT EXISTS app.product_collections (
  product_id UUID NOT NULL,
  collection_id UUID NOT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  PRIMARY KEY (product_id, collection_id)
);

-- Scrape job header
CREATE TABLE IF NOT EXISTS app.scrape_jobs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  shop_id UUID NOT NULL,
  domain TEXT NOT NULL,
  mode TEXT NOT NULL DEFAULT 'public', -- public | storefront | admin
  status TEXT NOT NULL DEFAULT 'queued', -- queued|running|paused|completed|failed|canceled
  config_json JSONB NOT NULL DEFAULT '{}'::jsonb,
  started_at TIMESTAMPTZ NULL,
  finished_at TIMESTAMPTZ NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
CREATE INDEX IF NOT EXISTS idx_scrape_jobs_shop_status ON app.scrape_jobs(shop_id, status);

-- Scrape job items (per collection+handle)
CREATE TABLE IF NOT EXISTS app.scrape_job_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  job_id UUID NOT NULL,
  collection_slug TEXT NOT NULL,
  handle TEXT NOT NULL,
  status TEXT NOT NULL DEFAULT 'queued', -- queued|running|done|skipped|failed|canceled
  attempts INT NOT NULL DEFAULT 0,
  error TEXT NULL,
  last_progress_at TIMESTAMPTZ DEFAULT now(),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  UNIQUE(job_id, collection_slug, handle)
);
CREATE INDEX IF NOT EXISTS idx_scrape_job_items_job_status ON app.scrape_job_items(job_id, status);

-- Ensure touch_updated_at triggers exist (function defined in 0003_hardening.sql)
DO $$
DECLARE
  fn_exists BOOLEAN;
BEGIN
  SELECT EXISTS (
    SELECT 1 FROM pg_proc p
    JOIN pg_namespace n ON n.oid = p.pronamespace
    WHERE p.proname = 'touch_updated_at' AND n.nspname = 'app'
  ) INTO fn_exists;

  IF fn_exists THEN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_shops') THEN
      CREATE TRIGGER trg_touch_shops BEFORE UPDATE ON app.shops
      FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_collections') THEN
      CREATE TRIGGER trg_touch_collections BEFORE UPDATE ON app.collections
      FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_scrape_jobs') THEN
      CREATE TRIGGER trg_touch_scrape_jobs BEFORE UPDATE ON app.scrape_jobs
      FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'trg_touch_scrape_job_items') THEN
      CREATE TRIGGER trg_touch_scrape_job_items BEFORE UPDATE ON app.scrape_job_items
      FOR EACH ROW EXECUTE FUNCTION app.touch_updated_at();
    END IF;
  END IF;
END $$;
