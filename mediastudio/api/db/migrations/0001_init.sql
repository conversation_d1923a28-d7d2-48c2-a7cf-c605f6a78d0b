CREATE TABLE IF NOT EXISTS app.generation_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  batch_id UUID NOT NULL REFERENCES app.batches(id) ON DELETE CASCADE,
  product_id UUID NULL REFERENCES app.products(id) ON DELETE SET NULL, -- Made nullable
  variant_id UUID NULL REFERENCES app.variants(id) ON DELETE SET NULL,
  prompt TEXT NOT NULL,
  params_json JSONB NOT NULL DEFAULT '{}'::jsonb,
  status TEXT NOT NULL DEFAULT 'queued',
  started_at TIMESTAMPTZ NULL,
  finished_at TIMESTAMPTZ NULL,
  error TEXT NULL,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
