{"name": "mediastudio-api", "version": "0.1.0", "private": true, "type": "module", "engines": {"node": ">=20"}, "scripts": {"dev": "tsx watch --clear-screen=false src/server.ts", "dev:db": "docker compose up -d", "dev:all": "docker compose up -d && tsx watch src/server.ts", "start": "node dist/server.js", "build": "tsc -p tsconfig.json", "migrate": "node ./scripts/migrate.mjs", "migrate:dry": "node ./scripts/migrate.mjs --dry-run", "seed:jsonl": "tsx scripts/import-shopify-jsonl.ts", "test": "vitest", "seed:scrape": "tsx scripts/scrape-storefront.ts"}, "dependencies": {"@google/genai": "^1.19.0", "@google/generative-ai": "^0.24.1", "dotenv": "^16.4.5", "fastify": "^5.0.0", "pg": "^8.13.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^22.14.0", "tsx": "^4.20.5", "typescript": "~5.8.2", "vitest": "^2.1.5"}}