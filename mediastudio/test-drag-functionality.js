// Test script to verify drag functionality from generated assets gallery
// Run this in the browser console to test drag and drop

console.log('🧪 Testing Drag Functionality from Generated Assets Gallery...');

// Test 1: Check if generated assets have draggable attribute
function testDraggableAttributes() {
  const generatedThumbnails = document.querySelectorAll('[data-testid="thumbnail"]');
  const draggableElements = document.querySelectorAll('[draggable="true"]');
  
  console.log('✅ Test 1: Draggable Elements');
  console.log(`Found ${generatedThumbnails.length} thumbnail elements`);
  console.log(`Found ${draggableElements.length} draggable elements`);
  
  // Check if any thumbnails in the preview pane are draggable
  const previewPane = document.querySelector('[data-testid="preview-pane"]') || 
                     document.querySelector('.preview-pane') ||
                     document.querySelector('[class*="preview"]');
  
  if (previewPane) {
    const previewDraggables = previewPane.querySelectorAll('[draggable="true"]');
    console.log(`Found ${previewDraggables.length} draggable elements in preview pane`);
    
    previewDraggables.forEach((el, i) => {
      console.log(`  Draggable ${i + 1}:`, el.tagName, el.className);
    });
  } else {
    console.log('⚠️  Could not find preview pane');
  }
  
  return draggableElements.length > 0;
}

// Test 2: Check if prompt inputs can receive drops
function testDropTargets() {
  const promptInputs = document.querySelectorAll('textarea');
  const mentionsInputs = document.querySelectorAll('.mentions__input');
  
  console.log('✅ Test 2: Drop Targets');
  console.log(`Found ${promptInputs.length} textarea elements`);
  console.log(`Found ${mentionsInputs.length} mentions input elements`);
  
  // Check if any have drop event listeners
  let hasDropHandlers = 0;
  promptInputs.forEach((input, i) => {
    const parent = input.closest('[data-product-row]') || input.parentElement;
    if (parent) {
      // Check for drop-related attributes or event listeners
      const hasDropAttr = parent.hasAttribute('ondrop') || 
                         parent.hasAttribute('ondragover') ||
                         parent.hasAttribute('ondragleave');
      
      if (hasDropAttr) {
        hasDropHandlers++;
        console.log(`  Input ${i + 1} has drop handlers`);
      }
    }
  });
  
  console.log(`Found ${hasDropHandlers} inputs with drop handlers`);
  return hasDropHandlers > 0;
}

// Test 3: Simulate drag and drop
function testDragAndDrop() {
  console.log('✅ Test 3: Simulate Drag and Drop');
  
  const draggableElement = document.querySelector('[draggable="true"]');
  const dropTarget = document.querySelector('textarea') || 
                    document.querySelector('.mentions__input');
  
  if (!draggableElement) {
    console.log('❌ No draggable elements found');
    return false;
  }
  
  if (!dropTarget) {
    console.log('❌ No drop targets found');
    return false;
  }
  
  console.log('Found draggable element:', draggableElement.tagName, draggableElement.className);
  console.log('Found drop target:', dropTarget.tagName, dropTarget.className);
  
  try {
    // Create drag start event
    const dragStartEvent = new DragEvent('dragstart', {
      bubbles: true,
      cancelable: true,
      dataTransfer: new DataTransfer()
    });
    
    // Simulate drag start
    draggableElement.dispatchEvent(dragStartEvent);
    console.log('✅ Drag start event dispatched');
    
    // Create drop event
    const dropEvent = new DragEvent('drop', {
      bubbles: true,
      cancelable: true,
      dataTransfer: dragStartEvent.dataTransfer
    });
    
    // Simulate drop
    const dropContainer = dropTarget.closest('[data-product-row]') || dropTarget.parentElement;
    if (dropContainer) {
      dropContainer.dispatchEvent(dropEvent);
      console.log('✅ Drop event dispatched');
    }
    
    return true;
  } catch (error) {
    console.log('❌ Error simulating drag and drop:', error);
    return false;
  }
}

// Test 4: Check for generated assets data
function testGeneratedAssetsData() {
  console.log('✅ Test 4: Generated Assets Data');
  
  // Look for generated assets in the DOM
  const assetElements = document.querySelectorAll('[data-asset-id]');
  const thumbnailElements = document.querySelectorAll('.thumbnail, [class*="thumbnail"]');
  
  console.log(`Found ${assetElements.length} elements with asset IDs`);
  console.log(`Found ${thumbnailElements.length} thumbnail elements`);
  
  // Check if any have generated asset characteristics
  let generatedCount = 0;
  assetElements.forEach((el) => {
    const assetId = el.getAttribute('data-asset-id');
    if (assetId && !assetId.startsWith('temp_')) {
      generatedCount++;
    }
  });
  
  console.log(`Found ${generatedCount} non-placeholder assets`);
  
  return generatedCount > 0;
}

// Test 5: Check console for drag-related errors
function testConsoleErrors() {
  console.log('✅ Test 5: Console Error Check');
  
  const originalError = console.error;
  const originalWarn = console.warn;
  let errorCount = 0;
  let warnCount = 0;
  
  console.error = function(...args) {
    if (args.some(arg => typeof arg === 'string' && 
        (arg.includes('drag') || arg.includes('drop') || arg.includes('mention')))) {
      errorCount++;
    }
    originalError.apply(console, args);
  };
  
  console.warn = function(...args) {
    if (args.some(arg => typeof arg === 'string' && 
        (arg.includes('drag') || arg.includes('drop') || arg.includes('mention')))) {
      warnCount++;
    }
    originalWarn.apply(console, args);
  };
  
  // Restore after a delay
  setTimeout(() => {
    console.error = originalError;
    console.warn = originalWarn;
    console.log(`Monitored ${errorCount} drag-related errors and ${warnCount} warnings`);
  }, 5000);
  
  return { errorCount, warnCount };
}

// Run all tests
function runDragTests() {
  console.log('\n🚀 Running Drag Functionality Tests...\n');
  
  const results = {
    draggableElements: testDraggableAttributes(),
    dropTargets: testDropTargets(),
    dragAndDrop: testDragAndDrop(),
    generatedAssets: testGeneratedAssetsData(),
    consoleErrors: testConsoleErrors()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    if (typeof passed === 'object') {
      console.log(`🔍 ${test}: ${JSON.stringify(passed)}`);
    } else {
      console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASS' : 'FAIL'}`);
    }
  });
  
  const passedTests = Object.values(results).filter(r => 
    typeof r === 'boolean' ? r : true
  ).length;
  const totalTests = Object.keys(results).length - 1; // Exclude consoleErrors
  
  console.log(`\n🎯 Overall Score: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All drag tests passed! Drag and drop should be working.');
  } else {
    console.log('⚠️  Some drag tests failed. Check the implementation.');
  }
  
  return results;
}

// Auto-run tests when script is loaded
if (typeof window !== 'undefined') {
  // Wait for page to load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runDragTests);
  } else {
    setTimeout(runDragTests, 1000); // Give React time to render
  }
}

// Export for manual testing
window.testDragFunctionality = runDragTests;
