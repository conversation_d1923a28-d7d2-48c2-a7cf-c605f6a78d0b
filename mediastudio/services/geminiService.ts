// Frontend service that calls the backend API
import type { ImageSettings, VideoSettings } from "../types";

export const API_BASE_URL = "http://localhost:8080";

export const generateImage = async (
  prompt: string,
  settings: ImageSettings,
  referenceImages?: File[]
): Promise<{ url: string }> => {
  try {
    console.log(
      "Generating image with prompt:",
      prompt,
      "and settings:",
      settings
    );

    const response = await fetch(`${API_BASE_URL}/api/generate/image`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt,
        settings,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || "Failed to generate image");
    }

    return result.data;
  } catch (error) {
    console.error("Error generating image:", error);
    throw error;
  }
};

export const generateVideo = async (
  prompt: string,
  settings: VideoSettings,
  referenceImages?: File[],
  audioFile?: File
): Promise<{ url: string }> => {
  try {
    console.log(
      "Generating video with prompt:",
      prompt,
      "and settings:",
      settings
    );

    const response = await fetch(`${API_BASE_URL}/api/generate/video`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        prompt,
        settings,
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.error || "Failed to generate video");
    }

    return result.data;
  } catch (error) {
    console.error("Error generating video:", error);
    throw error;
  }
};

// Batch generation API (backend-supported)
export async function startBatch(payload: {
  mode: "image" | "video";
  model: string;
  settings: any;
  items: Array<{
    productId: string;
    prompt: string;
    referenceImageUrls?: string[];
  }>;
}) {
  const resp = await fetch(`${API_BASE_URL}/api/generate/batch`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(payload),
  });
  // Try to parse body first to capture request_id in body and richer errors
  const jsonMaybe = await (async () => {
    try {
      return await resp.json();
    } catch {
      return null;
    }
  })();
  const rid =
    resp.headers.get("X-Request-Id") || (jsonMaybe as any)?.request_id || null;
  if (!resp.ok) {
    const errMsg =
      (jsonMaybe as any)?.error?.message ||
      (jsonMaybe as any)?.error ||
      `Failed to start batch`;
    throw new Error(
      `${errMsg} (HTTP ${resp.status}${rid ? `, id: ${rid}` : ""})`
    );
  }
  return { ...(jsonMaybe || {}), __rid: rid } as any;
}

export async function getBatchStatus(batchId: string) {
  const resp = await fetch(
    `${API_BASE_URL}/api/generate/batch/${batchId}/status`
  );
  const json = await (async () => {
    try {
      return await resp.json();
    } catch {
      return null;
    }
  })();
  const rid =
    resp.headers.get("X-Request-Id") || (json as any)?.request_id || null;
  if (!resp.ok) {
    const body = json
      ? JSON.stringify(json)
      : await resp.text().catch(() => "");
    const errMsg = body || `Failed to get batch status`;
    throw new Error(
      `${errMsg} (HTTP ${resp.status}${rid ? `, id: ${rid}` : ""})`
    );
  }
  return { ...(json || {}), __rid: rid } as any;
}

export async function getBatchDetails(batchId: string) {
  const resp = await fetch(`${API_BASE_URL}/batches/${batchId}`);
  const json = await (async () => {
    try {
      return await resp.json();
    } catch {
      return null;
    }
  })();
  const rid =
    resp.headers.get("X-Request-Id") || (json as any)?.request_id || null;
  if (!resp.ok) {
    const body = json
      ? JSON.stringify(json)
      : await resp.text().catch(() => "");
    const errMsg = body || `Failed to get batch details`;
    throw new Error(
      `${errMsg} (HTTP ${resp.status}${rid ? `, id: ${rid}` : ""})`
    );
  }
  return { ...(json || {}), __rid: rid } as any;
}
