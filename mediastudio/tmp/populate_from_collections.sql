-- populate_from_collections generated
BEGIN;
-- shop_id: 387c18a4-057a-4662-9cae-45dd8ea26ed6

-- www.dancingqueens.ch/products/1259-split-spanish-leggings-in-navy-blau
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1259-split-spanish-leggings-in-navy-blau', 9000000001, '1259-split-spanish-leggings-in-navy-blau', '1259 Split Spanish Leggings in Navy Blau', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1259-split-spanish-leggings-in-navy-blau' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_b8ca179f-a8e3-4e8a-97df-ea6c170105cb.jpg?v=1729161224', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_b8ca179f-a8e3-4e8a-97df-ea6c170105cb.jpg?v=1729161224' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_b8ca179f-a8e3-4e8a-97df-ea6c170105cb.jpg?v=1729161224');

-- www.dancingqueens.ch/products/1257-split-spanish-leggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1257-split-spanish-leggings-in-schwarz', 9000000002, '1257-split-spanish-leggings-in-schwarz', '1257 Split Spanish Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1257-split-spanish-leggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_fe3fe2bd-67ab-48c0-acc0-9c8c05d6039a.jpg?v=1729161607', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_fe3fe2bd-67ab-48c0-acc0-9c8c05d6039a.jpg?v=1729161607' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_fe3fe2bd-67ab-48c0-acc0-9c8c05d6039a.jpg?v=1729161607');

-- www.dancingqueens.ch/products/467-bilboa-leather-look-leggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/467-bilboa-leather-look-leggings-in-schwarz', 9000000003, '467-bilboa-leather-look-leggings-in-schwarz', '467 Bilboa Leather Look Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/467-bilboa-leather-look-leggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_99be157f-7af9-4420-9f4a-ea651af44a27.jpg?v=1738180954', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_99be157f-7af9-4420-9f4a-ea651af44a27.jpg?v=1738180954' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_99be157f-7af9-4420-9f4a-ea651af44a27.jpg?v=1738180954');

-- www.dancingqueens.ch/products/468-lanzarote-leather-look-leggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/468-lanzarote-leather-look-leggings-in-schwarz', 9000000004, '468-lanzarote-leather-look-leggings-in-schwarz', '468 Lanzarote Leather Look Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/468-lanzarote-leather-look-leggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_22715e70-32a2-40ee-9f99-26a18abc606f.jpg?v=1738181129', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_22715e70-32a2-40ee-9f99-26a18abc606f.jpg?v=1738181129' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_22715e70-32a2-40ee-9f99-26a18abc606f.jpg?v=1738181129');

-- www.dancingqueens.ch/products/1033-extra-high-waist-leggings-black
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-black', 9000000005, '1033-extra-high-waist-leggings-black', '1033 Extra High Waist Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-black' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/1_83953be1-f230-436b-9108-8bdb7c2cd470.jpg?v=1665486829', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/1_83953be1-f230-436b-9108-8bdb7c2cd470.jpg?v=1665486829' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/1_83953be1-f230-436b-9108-8bdb7c2cd470.jpg?v=1665486829');

-- www.dancingqueens.ch/products/1163-extra-high-waist-leggings-in-schwarz-blau-mit-streifen
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1163-extra-high-waist-leggings-in-schwarz-blau-mit-streifen', 9000000006, '1163-extra-high-waist-leggings-in-schwarz-blau-mit-streifen', '1163 Extra High Waist Leggings in Schwarz & Blau mit Streifen', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1163-extra-high-waist-leggings-in-schwarz-blau-mit-streifen' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem05206-kopya.jpg?v=1665487798', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem05206-kopya.jpg?v=1665487798' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem05206-kopya.jpg?v=1665487798');

-- www.dancingqueens.ch/products/334-extra-high-waist-leggings-in-rosa-blau-geblumt
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/334-extra-high-waist-leggings-in-rosa-blau-geblumt', 9000000007, '334-extra-high-waist-leggings-in-rosa-blau-geblumt', '334 High Waist Leggings in Schwarz, Blau, Rosa geblumt', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/334-extra-high-waist-leggings-in-rosa-blau-geblumt' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/WhatsAppImage2022-11-19at07.35.59.jpg?v=1671197822', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/WhatsAppImage2022-11-19at07.35.59.jpg?v=1671197822' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/WhatsAppImage2022-11-19at07.35.59.jpg?v=1671197822');

-- www.dancingqueens.ch/products/876-extra-high-waist-leggings-in-schwarz-grun-geblumt
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/876-extra-high-waist-leggings-in-schwarz-grun-geblumt', 9000000008, '876-extra-high-waist-leggings-in-schwarz-grun-geblumt', '876 Extra High Waist Leggings in Schwarz & Grün geblumt', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/876-extra-high-waist-leggings-in-schwarz-grun-geblumt' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/img-8538.jpg?v=1665486380', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/img-8538.jpg?v=1665486380' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/img-8538.jpg?v=1665486380');

-- www.dancingqueens.ch/products/287-khaki-extra-high-waist-leggings
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/287-khaki-extra-high-waist-leggings', 9000000009, '287-khaki-extra-high-waist-leggings', '287 Extra High Waist Leggings in Khaki', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/287-khaki-extra-high-waist-leggings' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/7_0daeec05-f8f5-4e1c-864e-a3f9f5ead771.jpg?v=1665486192', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/7_0daeec05-f8f5-4e1c-864e-a3f9f5ead771.jpg?v=1665486192' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/7_0daeec05-f8f5-4e1c-864e-a3f9f5ead771.jpg?v=1665486192');

-- www.dancingqueens.ch/products/269-extra-high-waist-leggings-in-navyblau
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/269-extra-high-waist-leggings-in-navyblau', 9000000010, '269-extra-high-waist-leggings-in-navyblau', '296 Extra High Waist Leggings in Navyblau', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/269-extra-high-waist-leggings-in-navyblau' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem04850-kopya_webp.jpg?v=1671196889', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem04850-kopya_webp.jpg?v=1671196889' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/cem04850-kopya_webp.jpg?v=1671196889');

-- www.dancingqueens.ch/products/359-mumbai-yoga-hose-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/359-mumbai-yoga-hose-in-schwarz', 9000000011, '359-mumbai-yoga-hose-in-schwarz', '359 Mumbai Yoga Hose in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/359-mumbai-yoga-hose-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/359MumbaiYogaHoseinSchwarz4.0.jpg?v=1695811851', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/359MumbaiYogaHoseinSchwarz4.0.jpg?v=1695811851' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/359MumbaiYogaHoseinSchwarz4.0.jpg?v=1695811851');

-- www.dancingqueens.ch/products/347-scratch-detailed-leather-look-leggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/347-scratch-detailed-leather-look-leggings-in-schwarz', 9000000012, '347-scratch-detailed-leather-look-leggings-in-schwarz', '347 Scratch Detailed Leather Look Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/347-scratch-detailed-leather-look-leggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/347ScratchDetailedLeatherLookLeggingsinSchwarz3.0.jpg?v=1695816746', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/347ScratchDetailedLeatherLookLeggingsinSchwarz3.0.jpg?v=1695816746' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/347ScratchDetailedLeatherLookLeggingsinSchwarz3.0.jpg?v=1695816746');

-- www.dancingqueens.ch/products/1194-leather-look-leggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1194-leather-look-leggings-in-schwarz', 9000000013, '1194-leather-look-leggings-in-schwarz', '1194 Leather Look Leggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1194-leather-look-leggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1194LeatherLookLeggingsinSchwarz3.0.jpg?v=1695817401', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1194LeatherLookLeggingsinSchwarz3.0.jpg?v=1695817401' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1194LeatherLookLeggingsinSchwarz3.0.jpg?v=1695817401');

-- www.dancingqueens.ch/products/435-pocket-schlagleggings-in-navyblau-1
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/435-pocket-schlagleggings-in-navyblau-1', 9000000014, '435-pocket-schlagleggings-in-navyblau-1', '435 Pocket Schlagleggings in Navyblau', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/435-pocket-schlagleggings-in-navyblau-1' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_8cd4045a-2b0a-40e5-9d84-9626e90da917.jpg?v=1718351258', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_8cd4045a-2b0a-40e5-9d84-9626e90da917.jpg?v=1718351258' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_8cd4045a-2b0a-40e5-9d84-9626e90da917.jpg?v=1718351258');

-- www.dancingqueens.ch/products/432-pocket-schlagleggings-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/432-pocket-schlagleggings-in-schwarz', 9000000015, '432-pocket-schlagleggings-in-schwarz', '432 Pocket Schlagleggings in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/432-pocket-schlagleggings-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_050906c5-f5a1-4fd8-b137-dfb8db7c8c90.jpg?v=1718351544', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_050906c5-f5a1-4fd8-b137-dfb8db7c8c90.jpg?v=1718351544' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_050906c5-f5a1-4fd8-b137-dfb8db7c8c90.jpg?v=1718351544');

-- www.dancingqueens.ch/products/446-mumbai-yoga-hose-in-anthrazit
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/446-mumbai-yoga-hose-in-anthrazit', 9000000016, '446-mumbai-yoga-hose-in-anthrazit', '446 Mumbai Yoga Hose in Anthrazit', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/446-mumbai-yoga-hose-in-anthrazit' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/3_ffc23871-1b25-4d35-a311-0032e48b86b2.jpg?v=1725108722', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/3_ffc23871-1b25-4d35-a311-0032e48b86b2.jpg?v=1725108722' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/3_ffc23871-1b25-4d35-a311-0032e48b86b2.jpg?v=1725108722');

-- www.dancingqueens.ch/products/1033-extra-high-waist-leggings-in-black&media=
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=', 9000000017, '1033-extra-high-waist-leggings-in-black&media=', '1033-extra-high-waist-leggings-in-black&media=', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, '', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE '' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = '');

-- www.dancingqueens.ch/products/dq02-flache-tanzschuhe-in-schwarz-glitter
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-schwarz-glitter', 9000000018, 'dq02-flache-tanzschuhe-in-schwarz-glitter', 'DQ02 flache Tanzschuhe in Schwarz Glitter', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-schwarz-glitter' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var9_Glitter_Nero_Bufala-5.jpg?v=1754482203', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var9_Glitter_Nero_Bufala-5.jpg?v=1754482203' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var9_Glitter_Nero_Bufala-5.jpg?v=1754482203');

-- www.dancingqueens.ch/products/dq02-flache-tanzschuhe-in-gold-glitter
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-gold-glitter', 9000000019, 'dq02-flache-tanzschuhe-in-gold-glitter', 'DQ02 flache Tanzschuhe in Gold Glitter', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-gold-glitter' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var10_Glitter_Platino_Bufala-5.jpg?v=1754482345', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var10_Glitter_Platino_Bufala-5.jpg?v=1754482345' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ02_var10_Glitter_Platino_Bufala-5.jpg?v=1754482345');

-- www.dancingqueens.ch/products/andromeda-tanzschuhe-in-beige
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/andromeda-tanzschuhe-in-beige', 9000000020, 'andromeda-tanzschuhe-in-beige', 'Andromeda Tanzschuhe in Beige', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/andromeda-tanzschuhe-in-beige' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ_Andromeda_Beige_7cm_2.jpg?v=1755845217', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ_Andromeda_Beige_7cm_2.jpg?v=1755845217' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/DQ_Andromeda_Beige_7cm_2.jpg?v=1755845217');

-- www.dancingqueens.ch/products/andromeda-tanzschuhe
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/andromeda-tanzschuhe', 9000000021, 'andromeda-tanzschuhe', 'Andromeda Tanzschuhe in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/andromeda-tanzschuhe' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/dq_52553742254464_A1_eac06f3f-9e26-4efe-a414-44b735260833.jpg?v=1738918847', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/dq_52553742254464_A1_eac06f3f-9e26-4efe-a414-44b735260833.jpg?v=1738918847' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/dq_52553742254464_A1_eac06f3f-9e26-4efe-a414-44b735260833.jpg?v=1738918847');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-grau
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-grau', 9000000022, 'smove-dance-sneaker-in-grau', 'SMOVE Dance Sneaker in Grau', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-grau' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Grey_1_1.jpg?v=1738766854', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Grey_1_1.jpg?v=1738766854' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Grey_1_1.jpg?v=1738766854');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-beige
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-beige', 9000000023, 'smove-dance-sneaker-in-beige', 'SMOVE Dance Sneaker in Beige', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-beige' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Dance_Sneaker_Beige_s.jpg?v=1749640508', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Dance_Sneaker_Beige_s.jpg?v=1749640508' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Dance_Sneaker_Beige_s.jpg?v=1749640508');

-- www.dancingqueens.ch/products/fuego-low-top-dance-sneakers-in-lavender
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/fuego-low-top-dance-sneakers-in-lavender', 9000000024, 'fuego-low-top-dance-sneakers-in-lavender', 'Fuego Low-Top Dance Sneakers in Lavender', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/fuego-low-top-dance-sneakers-in-lavender' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_d26ca1a2-f368-4668-8a22-c2675c32c294.jpg?v=1747985976', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_d26ca1a2-f368-4668-8a22-c2675c32c294.jpg?v=1747985976' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_d26ca1a2-f368-4668-8a22-c2675c32c294.jpg?v=1747985976');

-- www.dancingqueens.ch/products/fuego-dance-sneakers-white
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/fuego-dance-sneakers-white', 9000000025, 'fuego-dance-sneakers-white', 'Fuego Low-Top Dance Sneakers in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/fuego-dance-sneakers-white' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_c12a28c7-63f0-40bb-b1fd-e727696a8b4d.jpg?v=1739296508', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_c12a28c7-63f0-40bb-b1fd-e727696a8b4d.jpg?v=1739296508' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_c12a28c7-63f0-40bb-b1fd-e727696a8b4d.jpg?v=1739296508');

-- www.dancingqueens.ch/products/fuego-dance-sneakers-black
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/fuego-dance-sneakers-black', 9000000026, 'fuego-dance-sneakers-black', 'Fuego Low-Top Dance Sneakers in Schwarz/Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/fuego-dance-sneakers-black' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bfdc4554-468b-4728-a6fc-1d6ad9e0cba0.jpg?v=1739296975', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bfdc4554-468b-4728-a6fc-1d6ad9e0cba0.jpg?v=1739296975' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bfdc4554-468b-4728-a6fc-1d6ad9e0cba0.jpg?v=1739296975');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-weiss-2
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-weiss-2', 9000000027, 'smove-dance-sneaker-in-weiss-2', 'SMOVE Dance Sneaker in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-weiss-2' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Sneaker_white_1_1.jpg?v=1738917662', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Sneaker_white_1_1.jpg?v=1738917662' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Sneaker_white_1_1.jpg?v=1738917662');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-schwarz-mit-weisser-sohle
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-schwarz-mit-weisser-sohle', 9000000028, 'smove-dance-sneaker-in-schwarz-mit-weisser-sohle', 'SMOVE Dance Sneaker in Schwarz mit weisser Sohle', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-schwarz-mit-weisser-sohle' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Black_whitesohle_1_1.jpg?v=1738917971', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Black_whitesohle_1_1.jpg?v=1738917971' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_Black_whitesohle_1_1.jpg?v=1738917971');

-- www.dancingqueens.ch/products/dq02-tanzschuhe-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/dq02-tanzschuhe-in-schwarz', 9000000029, 'dq02-tanzschuhe-in-schwarz', 'DQ02 flache Tanzschuhe in Schwarz Wildleder', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/dq02-tanzschuhe-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/CamoscioNero_2.jpg?v=1744385132', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/CamoscioNero_2.jpg?v=1744385132' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/CamoscioNero_2.jpg?v=1744385132');

-- www.dancingqueens.ch/products/dq02-flache-tanzschuhe-in-multiglitter
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-multiglitter', 9000000030, 'dq02-flache-tanzschuhe-in-multiglitter', 'DQ02 flache Tanzschuhe in Multiglitter', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/dq02-flache-tanzschuhe-in-multiglitter' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/GlitterMulti_2.jpg?v=1744385398', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/GlitterMulti_2.jpg?v=1744385398' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/GlitterMulti_2.jpg?v=1744385398');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-weiss-leder
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-weiss-leder', 9000000031, 'smove-dance-sneaker-in-weiss-leder', 'SMOVE Dance Sneaker in Weiss Leder', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-weiss-leder' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/smove_sneaker_white_1_leder.jpg?v=1739459235', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/smove_sneaker_white_1_leder.jpg?v=1739459235' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/smove_sneaker_white_1_leder.jpg?v=1739459235');

-- www.dancingqueens.ch/products/smove-dance-sneaker-in-schwarz-leder
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/smove-dance-sneaker-in-schwarz-leder', 9000000032, 'smove-dance-sneaker-in-schwarz-leder', 'SMOVE Dance Sneaker in Schwarz Leder', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/smove-dance-sneaker-in-schwarz-leder' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_schwarz_Lederoptik_weisseSohle_1von6.jpg?v=1739457771', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_schwarz_Lederoptik_weisseSohle_1von6.jpg?v=1739457771' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/Smove_schwarz_Lederoptik_weisseSohle_1von6.jpg?v=1739457771');

-- www.dancingqueens.ch/products/dq-l3m-tanzschuhe-in-flesh-mit-strass
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/dq-l3m-tanzschuhe-in-flesh-mit-strass', 9000000033, 'dq-l3m-tanzschuhe-in-flesh-mit-strass', 'DQ L3M Tanzschuhe in Flesh mit Strass', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/dq-l3m-tanzschuhe-in-flesh-mit-strass' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/7630661210116_A2_f410c374-6c34-4db8-8b4b-7c27923b5cf8.jpg?v=1724239510', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/7630661210116_A2_f410c374-6c34-4db8-8b4b-7c27923b5cf8.jpg?v=1724239510' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/7630661210116_A2_f410c374-6c34-4db8-8b4b-7c27923b5cf8.jpg?v=1724239510');

-- www.dancingqueens.ch/products/1033-extra-high-waist-leggings-in-black&media=
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=', 9000000034, '1033-extra-high-waist-leggings-in-black&media=', '1033-extra-high-waist-leggings-in-black&media=', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, '', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE '' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = '');

-- www.dancingqueens.ch/products/192-decolette-spor-bra-purple
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/192-decolette-spor-bra-purple', 9000000035, '192-decolette-spor-bra-purple', '192 Decoltée Sport Bra in Violett', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/192-decolette-spor-bra-purple' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_0568c50e-05db-4765-8f9a-9f240d18a99c.jpg?v=1719300477', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_0568c50e-05db-4765-8f9a-9f240d18a99c.jpg?v=1719300477' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_0568c50e-05db-4765-8f9a-9f240d18a99c.jpg?v=1719300477');

-- www.dancingqueens.ch/products/187-decolette-sport-bra-black
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/187-decolette-sport-bra-black', 9000000036, '187-decolette-sport-bra-black', '187 Decoltée Sport Bra in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/187-decolette-sport-bra-black' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/3_b7a10360-ab61-4452-b99c-6437d5c35fd1.jpg?v=1665485551', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/3_b7a10360-ab61-4452-b99c-6437d5c35fd1.jpg?v=1665485551' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/products/3_b7a10360-ab61-4452-b99c-6437d5c35fd1.jpg?v=1665485551');

-- www.dancingqueens.ch/products/502-sport-bra-in-weiss
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/502-sport-bra-in-weiss', 9000000037, '502-sport-bra-in-weiss', '502 Sport Bra in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/502-sport-bra-in-weiss' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_915d66b0-f78e-44fd-a94e-b513e8139707.jpg?v=1750419727', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_915d66b0-f78e-44fd-a94e-b513e8139707.jpg?v=1750419727' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_915d66b0-f78e-44fd-a94e-b513e8139707.jpg?v=1750419727');

-- www.dancingqueens.ch/products/501-sport-bra-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/501-sport-bra-in-schwarz', 9000000038, '501-sport-bra-in-schwarz', '501 Sport Bra in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/501-sport-bra-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_979309f1-8764-4ba9-b9ef-c36e981e073f.jpg?v=1750419848', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_979309f1-8764-4ba9-b9ef-c36e981e073f.jpg?v=1750419848' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_979309f1-8764-4ba9-b9ef-c36e981e073f.jpg?v=1750419848');

-- www.dancingqueens.ch/products/473-bayonne-sport-bra-in-weiss
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/473-bayonne-sport-bra-in-weiss', 9000000039, '473-bayonne-sport-bra-in-weiss', '473 Bayonne Sport Bra in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/473-bayonne-sport-bra-in-weiss' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_2f0a42d3-ae38-4f0b-a8b6-b254320cfe8f.jpg?v=1736173573', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_2f0a42d3-ae38-4f0b-a8b6-b254320cfe8f.jpg?v=1736173573' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/2_2f0a42d3-ae38-4f0b-a8b6-b254320cfe8f.jpg?v=1736173573');

-- www.dancingqueens.ch/products/475-menton-sport-bra-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/475-menton-sport-bra-in-schwarz', 9000000040, '475-menton-sport-bra-in-schwarz', '475 Menton Sport Bra in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/475-menton-sport-bra-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_c0f2954d-e486-4612-bb44-45d9bd3cdde5.jpg?v=1736171955', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_c0f2954d-e486-4612-bb44-45d9bd3cdde5.jpg?v=1736171955' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_c0f2954d-e486-4612-bb44-45d9bd3cdde5.jpg?v=1736171955');

-- www.dancingqueens.ch/products/093-larissa-sport-top-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/093-larissa-sport-top-in-schwarz', 9000000041, '093-larissa-sport-top-in-schwarz', '093 Larissa Sport Top in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/093-larissa-sport-top-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_9d77449a-cb5d-4fae-b63f-eb3150b2f505.jpg?v=1729163360', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_9d77449a-cb5d-4fae-b63f-eb3150b2f505.jpg?v=1729163360' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_9d77449a-cb5d-4fae-b63f-eb3150b2f505.jpg?v=1729163360');

-- www.dancingqueens.ch/products/094-larissa-sport-top-in-weiss
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/094-larissa-sport-top-in-weiss', 9000000042, '094-larissa-sport-top-in-weiss', '094 Larissa Sport Top in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/094-larissa-sport-top-in-weiss' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_559d39b2-f26c-4e3b-a020-852dd45cc331.jpg?v=1729163052', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_559d39b2-f26c-4e3b-a020-852dd45cc331.jpg?v=1729163052' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_559d39b2-f26c-4e3b-a020-852dd45cc331.jpg?v=1729163052');

-- www.dancingqueens.ch/products/098-yoga-t-shirt-in-khaki
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/098-yoga-t-shirt-in-khaki', 9000000043, '098-yoga-t-shirt-in-khaki', '098 Yoga T-Shirt in Khaki', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/098-yoga-t-shirt-in-khaki' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_e4c802e9-15a1-4b16-a97b-a0a0deacc9c4.jpg?v=1729162899', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_e4c802e9-15a1-4b16-a97b-a0a0deacc9c4.jpg?v=1729162899' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_e4c802e9-15a1-4b16-a97b-a0a0deacc9c4.jpg?v=1729162899');

-- www.dancingqueens.ch/products/048-yoga-t-shirt-in-navy-blau
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/048-yoga-t-shirt-in-navy-blau', 9000000044, '048-yoga-t-shirt-in-navy-blau', '048 Yoga T-Shirt in Navy Blau', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/048-yoga-t-shirt-in-navy-blau' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4693135b-dac9-47c0-ba00-8b320fcaff2a.jpg?v=1729162738', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4693135b-dac9-47c0-ba00-8b320fcaff2a.jpg?v=1729162738' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4693135b-dac9-47c0-ba00-8b320fcaff2a.jpg?v=1729162738');

-- www.dancingqueens.ch/products/097-yoga-t-shirt-in-bordeaux
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/097-yoga-t-shirt-in-bordeaux', 9000000045, '097-yoga-t-shirt-in-bordeaux', '097 Yoga T-Shirt in Bordeaux', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/097-yoga-t-shirt-in-bordeaux' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_43d41c99-51e8-4a82-bf9c-9468c1c71b44.jpg?v=1729162584', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_43d41c99-51e8-4a82-bf9c-9468c1c71b44.jpg?v=1729162584' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_43d41c99-51e8-4a82-bf9c-9468c1c71b44.jpg?v=1729162584');

-- www.dancingqueens.ch/products/095-yoga-t-shirt-in-anthrazit
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/095-yoga-t-shirt-in-anthrazit', 9000000046, '095-yoga-t-shirt-in-anthrazit', '095 Yoga T-Shirt in Anthrazit', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/095-yoga-t-shirt-in-anthrazit' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4d86fb52-addc-4f7c-8d61-bf757896df41.jpg?v=1729162428', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4d86fb52-addc-4f7c-8d61-bf757896df41.jpg?v=1729162428' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4d86fb52-addc-4f7c-8d61-bf757896df41.jpg?v=1729162428');

-- www.dancingqueens.ch/products/096-yoga-t-shirt-in-weiss
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/096-yoga-t-shirt-in-weiss', 9000000047, '096-yoga-t-shirt-in-weiss', '096 Yoga T-Shirt in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/096-yoga-t-shirt-in-weiss' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_3b935de0-46b6-46a8-8eb3-e73abf79b083.jpg?v=1729162255', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_3b935de0-46b6-46a8-8eb3-e73abf79b083.jpg?v=1729162255' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_3b935de0-46b6-46a8-8eb3-e73abf79b083.jpg?v=1729162255');

-- www.dancingqueens.ch/products/100-yoga-t-shirt-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/100-yoga-t-shirt-in-schwarz', 9000000048, '100-yoga-t-shirt-in-schwarz', '100 Yoga T-Shirt in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/100-yoga-t-shirt-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_95ee53d9-7a83-4dc0-9ff2-9d1c67c4cfdc.jpg?v=1729161876', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_95ee53d9-7a83-4dc0-9ff2-9d1c67c4cfdc.jpg?v=1729161876' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_95ee53d9-7a83-4dc0-9ff2-9d1c67c4cfdc.jpg?v=1729161876');

-- www.dancingqueens.ch/products/089-sport-top-in-weiss
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/089-sport-top-in-weiss', 9000000049, '089-sport-top-in-weiss', '089 Sport-Top in Weiss', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/089-sport-top-in-weiss' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4b33eaf9-4549-4ac8-8a48-7b7406178c80.jpg?v=1699986898', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4b33eaf9-4549-4ac8-8a48-7b7406178c80.jpg?v=1699986898' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_4b33eaf9-4549-4ac8-8a48-7b7406178c80.jpg?v=1699986898');

-- www.dancingqueens.ch/products/088-sport-top-in-schwarz
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/088-sport-top-in-schwarz', 9000000050, '088-sport-top-in-schwarz', '088 Sport-Top in Schwarz', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/088-sport-top-in-schwarz' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bc1aa3d4-68a2-422f-9a23-a159be69f448.jpg?v=1699986681', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bc1aa3d4-68a2-422f-9a23-a159be69f448.jpg?v=1699986681' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = 'https://cdn.shopify.com/s/files/1/0047/2321/2377/files/1_bc1aa3d4-68a2-422f-9a23-a159be69f448.jpg?v=1699986681');

-- www.dancingqueens.ch/products/1033-extra-high-waist-leggings-in-black&media=
WITH p AS (
  INSERT INTO app.products (shop_id, external_gid, external_rest_id, handle, title, status, raw_json)
  VALUES ('387c18a4-057a-4662-9cae-45dd8ea26ed6', 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=', 9000000051, '1033-extra-high-waist-leggings-in-black&media=', '1033-extra-high-waist-leggings-in-black&media=', 'ACTIVE', '{}'::jsonb)
  ON CONFLICT (external_gid) DO UPDATE SET handle = EXCLUDED.handle, title = EXCLUDED.title, status = 'ACTIVE', updated_at = now()
  RETURNING id
), pid AS (
  SELECT id FROM p
  UNION ALL
  SELECT id FROM app.products WHERE external_gid = 'gid://dancingqueens/Product/1033-extra-high-waist-leggings-in-black&media=' AND NOT EXISTS (SELECT 1 FROM p)
)
INSERT INTO app.product_images (product_id, src_url, width, height, alt, raw_json)
SELECT pid.id, '', NULL, NULL, NULL, jsonb_build_object('source','fallback')
FROM pid
WHERE '' <> '' AND NOT EXISTS (SELECT 1 FROM app.product_images i WHERE i.product_id = pid.id AND i.src_url = '');
COMMIT;