\pset pager off
SELECT count(*) AS products FROM app.products;

WITH handles(handle) AS (
  VALUES 
    ('1259-split-spanish-leggings-in-navy-blue'),
    ('1257-split-spanish-leggings-in-black'),
    ('467-bilboa-leather-look-leggings-in-schwarz'),
    ('468-lanzarote-leather-look-leggings-in-schwarz'),
    ('1033-extra-high-waist-leggings-in-black'),
    ('1163-extra-high-waist-leggings-in-black-and-blue-with-stripes'),
    ('334-high-waist-leggings-in-black-blue-pink-fan'),
    ('876-extra-high-waist-leggings-in-black-green'),
    ('287-khaki-extra-high-waist-leggings'),
    ('296-extra-high-waist-leggings-in-navy-blue'),
    ('dq02-tanzschuhe-in-schwarz'),
    ('dq02-flache-tanzschuhe-in-multiglitter'),
    ('smove-dance-sneaker-in-weiss-leder'),
    ('smove-dance-sneaker-in-schwarz-leder'),
    ('dq-l3m-tanzschuhe-in-flesh-mit-strass'),
    ('dq-l3m-tanzschuhe-in-rose-cipria-mit-strass'),
    ('fuego-low-top-dance-sneakers-x-derek-hough'),
    ('fuego-dance-sneakers-split-sole-x-derek-hough'),
    ('2317-betty-tanzstiefel-in-schwarz'),
    ('2317-betty-tanzstiefel-in-beige'),
    ('192-decoltee-sport-bra-in-violet'),
    ('187-decoltee-sport-bra-in-black'),
    ('502-sport-bra-in-weiss'),
    ('501-sport-bra-in-schwarz'),
    ('473-bayonne-sport-bra-in-weiss'),
    ('475-menton-sport-bra-in-schwarz'),
    ('093-larissa-sport-top-in-black'),
    ('094-larissa-sport-top-in-weiss'),
    ('098-yoga-t-shirt-in-khaki'),
    ('048-yoga-t-shirt-in-navy-blue')
)
SELECT h.handle,
       p.id,
       p.title,
       p.status,
       (SELECT COUNT(*) FROM app.variants v WHERE v.product_id = p.id) AS variants,
       (SELECT COUNT(*) FROM app.product_images i WHERE i.product_id = p.id) AS images
FROM handles h
LEFT JOIN app.products p ON p.handle = h.handle
ORDER BY h.handle;

